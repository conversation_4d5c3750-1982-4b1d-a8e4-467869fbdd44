<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h4 align="center" style="margin: 0 auto 0 auto;">Drag & Drop GUI Creator</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>
 
<p align="center">
<a href="https://www.producthunt.com/posts/tkinter-designer?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-tkinter&#0045;designer" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=312995&theme=neutral" alt="Tkinter&#0032;Designer - No&#0045;code&#0032;solution&#0032;for&#0032;Python&#0032;GUI&#0039;s | Product Hunt" style="width: 250px; height: 54px;" width="250" height="54" /></a>
</p>
  <br>

## Translations

- [简体中文](/docs/README.zh-CN.md)
- [Français](/docs/README.fr-FR.md)
- [ગુજરાતી](/docs/README.gu-GU.md)
- [हिन्दी](/docs/README.hin-HIN.md)
- [Italiano](/docs/README.it-IT.md)
- [عربية](/docs/README.ar-DZ.md)
- [Turkish](/docs/README.tr-TR.md)
- [Brazil](/docs/README.pt-BR.md)
- [Spanish](/docs/README.spa-SPA.md)
- [मराठी](/docs/README.mr-MR.md)
- [Korean](/docs/README.kr-KR.md)
- [Tiếng Việt](/docs/README.vi-VN.md)

___

## 💡 Introduction

Tkinter Designer was created to speed up the GUI development process in Python. It uses the well-known design software [Figma](https://www.figma.com/) to make creating beautiful Tkinter GUIs in Python a piece of cake 🍰.

Tkinter Designer uses the Figma API to analyze a design file and create the respective code and files needed for the GUI. Even Tkinter Designer's GUI is created using Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## 📢 Announcement
### 🎉 Multi frame support is here! 🎉

You can now create multiple frames in a single design file and Tkinter Designer will create the respective code and files for each frame. This is a huge step for Tkinter Designer and I'm really excited to see what you guys create with it.

Feel free to share your creations with the community on [Discord](https://discord.gg/QfE5jMXxJv).

If you encounter any bugs or have any suggestions, please create an issue [here](https://github.com/ParthJadhav/Tkinter-Designer).
## ☄️  Advantages of Tkinter Designer

1. Interfaces with drag and drop.
2. A great deal quicker than writing code by hand
3. Produce more gorgeous interfaces

## ⚡️ Read the instruction here

View the YouTube video or read the instructions below.

<a href="/docs/instructions.md" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041925-64f81f75-8bee-42ac-a234-a93339bc8cdc.png" alt="Instructions" width="180px" ></a>
<a href="https://www.youtube.com/watch?v=Qd-jJjduWeQ" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041927-c94c1a94-c708-44a4-9c81-df83bac686d4.png" alt="Youtube Tutorial" width="180px" ></a>

## 🦋 Supporting Tkinter Designer

Consider making a donation to the Tkinter Designer project if you or your business have benefited from it. This will accelerate Tkinter Designer's development! Making coffee is simple; I'll be happy to enjoy one.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="180" ></a>
<a href="https://paypal.me/parthJadhav22?country.x=IN&locale.x=en_GB" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196043185-ebd61195-44ee-480f-9b76-f5eb7cfcaf55.png" alt="Paypal" width="180" ></a>


## 🔵 Discord server & Linkedin

Click the button below to join the discord server or Linkedin 

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="180px" ></a>
<a href="https://www.linkedin.com/in/parthjadhav04" target="_blank"><img src="https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin" alt="Connect on Linkedin" width="180px" height="58"></a>


## 📐 How it Works

The only thing the user needs to do is design an interface with Figma, and then paste the Figma file URL and API token into Tkinter Designer.

Tkinter Designer will automatically generate all the code and images required to create the GUI in Tkinter.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___

## 🎯 Examples

The possibilities are endless with Tkinter Designer, but here are a couple of GUIs that can be perfectly replicated in Tkinter.

<sup>The following are not my creations.</sup>

### HotinGo  [(More Info)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### CodTubify  [(More Info)](https://github.com/iamDyeus/CodTubify)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/181297276-fc8c4106-c988-4b1a-89d2-5e833a574aab.png">

### BeAnonymous [(More Info)](https://github.com/MambaCodes/BeAnonymous)

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/208241685-a3c51f59-746d-4e00-aaeb-c2c8357efb89.png">

### Frame Recorder [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

### Atarbals-Modern-Antivirus [(More Info)](https://github.com/HarshscGithub/Atarbals-Modern-Antivirus)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/205285178-74fb46c7-0c36-4fc5-983d-afbaaedb7cb9.png">

## 🔥 Showcase

Please let me know if Tkinter Designer was used to create your app. More illustrations will be
beneficial for other people!

(See: [Contact Me](#-contact-me)) or use [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) section in Discussions.

## 📄 License
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer is licensed under the BSD 3-Clause "New" or "Revised" License.  
[View Here.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Permissions | Restrictions | Conditions
| --- | --- | ---
&check; Commercial Use | &times; Liability | &#x1f6c8; License and Copyright Notice
&check; Modification   | &times; Warranty
&check; Distribution  
&check; Private Use

## Contribute

All contributions from the open-source community, individuals, and partners are welcomed. Our achievement is a result of your active participation.

[Contributing guidelines](docs/CONTRIBUTING.md)

[Code of conduct](CODE_OF_CONDUCT.md)

[LEARN.md](LEARN.md)

## 📝 Contact Me

If you want to contact me, you can
reach <NAME_EMAIL>

Connect with me on [![Linkedin](https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin)](https://www.linkedin.com/in/parthjadhav04/)
