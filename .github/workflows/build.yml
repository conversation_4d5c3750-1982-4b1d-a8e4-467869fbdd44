name: Test, Build, Release

on:
  # Allows for manual triggering and pull requests
  # from every branch, including forks.
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master
  
jobs:
  # Flake8 & Pytest
  lint_and_test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9]
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      - name: Lint
        run: |
          curl -sSL https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py | python -
          source $HOME/.poetry/env
          poetry install
          # stop the build if there are Python syntax errors or undefined 
          echo "Flake8 Syntax Error Check"
          poetry run flake8 --count --select=E9,F63,F7,F82 --show-source --statistics
          # stop the build if the conventions in .flake8 fail
          poetry run flake8
      
      - name: Test
        run: |
          source $HOME/.poetry/env
          poetry run pytest

  # Publish to Pypi
  build_and_release:
    runs-on: ubuntu-latest
    needs: lint_and_test
    if: github.ref == 'refs/heads/master'
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0 # Gets all tags and repo history
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'
      
      - name: Version & Publish
        run: |
          curl -sSL https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py | python -
          source $HOME/.poetry/env && echo "Poetry directory sourced."
          # set version number equal to latest git tag
          poetry version $(git tag | tail -1)
          # poetry config repositories.testpypi https://test.pypi.org/legacy/
          # poetry publish --build -r testpypi -u __token__ -p ${{ secrets.TEST_PYPI_TOKEN }}
          poetry publish --build -u __token__ -p ${{ secrets.PYPI_API_TOKEN }}
          
      # - name: Create GitHub Release
      #   uses: ncipollo/release-action@v1
      #   with:
      #     artifacts: "dist/*"
      #     bodyFile: "README.md"
      #     token: ${{ secrets.GITHUB_TOKEN }}