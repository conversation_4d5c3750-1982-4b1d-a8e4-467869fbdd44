#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Shutterstock CSV Generator

This script generates a CSV file for Shutterstock based on image/video metadata.
It extracts metadata from files and creates a CSV in the format required by Shutterstock.

Format:
Filename,Description,Keywords
"""

import os
import csv
import logging
import argparse
import sys
import pyexiv2
from typing import List, Tuple, Optional, Dict, Union
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define supported file extensions
SUPPORTED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.tif', '.tiff'}
SUPPORTED_VIDEO_EXTENSIONS = {'.mov', '.mp4', '.avi', '.mpeg', '.wmv'}
SUPPORTED_EXTENSIONS = SUPPORTED_IMAGE_EXTENSIONS.union(SUPPORTED_VIDEO_EXTENSIONS)

@contextmanager
def exif_handler(file_path: str) -> Optional[pyexiv2.Image]:
    """Context manager for safe EXIF handling."""
    image = None
    try:
        image = pyexiv2.Image(file_path)
        yield image
    except Exception as e:
        logging.error(f"EXIF error for {file_path}: {e}")
        yield None
    finally:
        if image:
            try:
                image.close()
            except Exception as e:
                logging.error(f"Error closing EXIF handler: {e}")

def check_for_matching_video(image_path: str) -> Tuple[bool, Optional[str]]:
    """
    Checks if an image file has a corresponding video file with the same name.

    Args:
        image_path (str): Path to the image file

    Returns:
        Tuple[bool, Optional[str]]: (has_matching_video, video_path)
            - has_matching_video: True if a matching video file exists
            - video_path: Path to the video file if found, None otherwise
    """
    # Skip check if the file is already a video
    file_extension = os.path.splitext(image_path)[1].lower()
    if file_extension in SUPPORTED_VIDEO_EXTENSIONS:
        return False, None

    # Get the base name and directory of the image
    base_name, _ = os.path.splitext(os.path.basename(image_path))
    dir_name = os.path.dirname(image_path)

    # Check for matching video files
    for ext in SUPPORTED_VIDEO_EXTENSIONS:
        potential_video = os.path.join(dir_name, base_name + ext)
        if os.path.exists(potential_video):
            try:
                # Simple file existence check is sufficient for this purpose
                logging.info(f"Found matching video for {os.path.basename(image_path)}: {os.path.basename(potential_video)}")
                return True, potential_video
            except Exception as e:
                logging.warning(f"Error checking video file {potential_video}: {e}")

    return False, None

def is_supported_file(file_path: str) -> bool:
    """
    Check if the file has a supported extension.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if the file has a supported extension, False otherwise
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower() in SUPPORTED_EXTENSIONS

def is_video_file(file_path: str) -> bool:
    """
    Check if the file is a video file.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if the file is a video, False otherwise
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower() in SUPPORTED_VIDEO_EXTENSIONS

def get_files_to_process(source_path: Union[str, List[str]]) -> List[str]:
    """
    Get a list of files to process from the source path(s).

    Args:
        source_path (str or list): Path to a file, directory, or list of files

    Returns:
        list: List of absolute paths to files that should be processed
    """
    files_to_process = []

    # Handle single path string
    if isinstance(source_path, str):
        if os.path.isfile(source_path):
            if is_supported_file(source_path):
                files_to_process.append(source_path)
        elif os.path.isdir(source_path):
            # Process all files in the directory
            for item in os.listdir(source_path):
                item_path = os.path.join(source_path, item)
                if os.path.isfile(item_path) and is_supported_file(item_path):
                    files_to_process.append(item_path)

    # Handle list of paths
    elif isinstance(source_path, list):
        for path in source_path:
            if os.path.isfile(path) and is_supported_file(path):
                files_to_process.append(path)
            elif os.path.isdir(path):
                # Process all files in the directory
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    if os.path.isfile(item_path) and is_supported_file(item_path):
                        files_to_process.append(item_path)

    return files_to_process

def extract_metadata(file_path: str) -> Optional[Tuple[str, str, str]]:
    """
    Extracts Title and Keywords from image metadata (EXIF/IPTC/XMP).

    If the file is a video or has a matching video file, the metadata is extracted from
    the image but the video filename is used in the CSV.

    Args:
        file_path (str): Absolute path to the image file.

    Returns:
        tuple: (filename, title, keywords_string) or None if extraction fails.
               Keywords are returned as a comma-separated string.
               Returns empty strings for title/keywords if tags are not found.
    """
    # Check if this is an image with a matching video file
    has_matching_video, video_path = check_for_matching_video(file_path)
    
    # Determine which file to use for metadata extraction and which filename to use in CSV
    if is_video_file(file_path):
        # This is a video file - use it directly
        metadata_source_path = file_path
        filename_for_csv = os.path.basename(file_path)
    elif has_matching_video and video_path:
        # This is an image with a matching video - extract metadata from image but use video filename
        metadata_source_path = file_path
        filename_for_csv = os.path.basename(video_path)
    else:
        # This is a standalone image - use it directly
        metadata_source_path = file_path
        filename_for_csv = os.path.basename(file_path)
    
    logging.debug(f"Extracting metadata from {os.path.basename(metadata_source_path)} for CSV entry: {filename_for_csv}")
    
    try:
        # Read metadata using pyexiv2
        with exif_handler(metadata_source_path) as image_exif:
            if not image_exif:
                logging.error(f"Failed to open {metadata_source_path} for metadata extraction")
                return None
                
            raw_exif = image_exif.read_exif()
            raw_iptc = image_exif.read_iptc()
            raw_xmp = image_exif.read_xmp()
            
            # Extract Title (Priority: EXIF XPTitle > IPTC ObjectName > XMP dc:title)
            xmp_title = ''
            if 'Xmp.dc.title' in raw_xmp:
                value = raw_xmp['Xmp.dc.title']
                if isinstance(value, list) and value:
                    xmp_title = value[0]
                elif isinstance(value, str):
                    xmp_title = value
                elif isinstance(value, dict) and 'lang' in value and 'value' in value:
                    xmp_title = value['value']
            
            title_bytes_or_str = raw_exif.get('Exif.Image.XPTitle',
                                            raw_iptc.get('Iptc.Application2.ObjectName',
                                                        xmp_title))
            
            # Handle title decoding if it's bytes
            if isinstance(title_bytes_or_str, bytes):
                try:
                    title = title_bytes_or_str.decode('utf-16le', errors='ignore').rstrip('\x00')
                    if not title:
                        title = title_bytes_or_str.decode('utf-8', errors='ignore').rstrip('\x00')
                except Exception as dec_err:
                    logging.warning(f"Could not decode title bytes for {filename_for_csv}: {dec_err}")
                    title = str(title_bytes_or_str)  # Fallback
            elif title_bytes_or_str:
                title = str(title_bytes_or_str)
            else:
                title = ''
            
            # Extract Keywords (Combine EXIF, IPTC, XMP)
            keywords_semi = raw_exif.get('Exif.Image.XPKeywords', b'')
            keywords_iptc = raw_iptc.get('Iptc.Application2.Keywords', [])
            keywords_xmp = raw_xmp.get('Xmp.dc.subject', [])
            
            kw_set = set()  # Use set to avoid duplicates
            
            # Process EXIF keywords (often semicolon-separated)
            if isinstance(keywords_semi, bytes):
                try:
                    # Try UTF-16LE first (common for Windows EXIF)
                    kw_text = keywords_semi.decode('utf-16le', errors='ignore').rstrip('\x00')
                    if not kw_text:  # If empty, try UTF-8
                        kw_text = keywords_semi.decode('utf-8', errors='ignore').rstrip('\x00')
                    
                    # Split by semicolon and add to set
                    for kw in kw_text.split(';'):
                        if kw.strip():
                            kw_set.add(kw.strip())
                except Exception as e:
                    logging.warning(f"Error decoding EXIF keywords for {filename_for_csv}: {e}")
            elif isinstance(keywords_semi, str):
                for kw in keywords_semi.split(';'):
                    if kw.strip():
                        kw_set.add(kw.strip())
            
            # Add IPTC keywords (usually a list)
            for kw in keywords_iptc:
                if kw.strip():
                    kw_set.add(kw.strip())
            
            # Add XMP keywords (usually a list)
            if isinstance(keywords_xmp, list):
                for kw in keywords_xmp:
                    if isinstance(kw, str) and kw.strip():
                        kw_set.add(kw.strip())
            
            # Remove any empty strings
            kw_set.discard('')
            
            # Convert to sorted list and join with commas for CSV
            keywords = sorted(list(kw_set))
            keywords_string = ",".join(keywords)
            
            logging.info(f"Extracted metadata for {os.path.basename(metadata_source_path)}: "
                        f"Title='{title[:50]}...', Keywords='{keywords_string[:50]}...' ({len(keywords)} keywords)")
            
            return filename_for_csv, title, keywords_string
            
    except Exception as e:
        logging.error(f"Error extracting metadata from {file_path}: {e}")
        return None

def generate_shutterstock_csv(metadata_list: List[Tuple[str, str, str]], output_csv_path: str) -> bool:
    """
    Generates a CSV file for Shutterstock based on extracted metadata.

    Args:
        metadata_list (list): A list of tuples [(filename, title, keywords_string), ...].
        output_csv_path (str): The full path where the CSV file should be saved.

    Returns:
        bool: True if CSV generation was successful, False otherwise.
    """
    # Shutterstock CSV Headers
    header = ['Filename', 'Description', 'Keywords']
    
    logging.info(f"Generating Shutterstock CSV at: {output_csv_path}")
    logging.debug(f"Data for Shutterstock CSV (first 5 items): {metadata_list[:5]}")
    
    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(header)
            
            if not metadata_list:
                logging.warning("No metadata provided for CSV generation.")
                return True  # Still created a valid CSV with headers
            
            for item_index, metadata_item in enumerate(metadata_list):
                try:
                    filename, title, keywords_string = metadata_item
                    
                    # Prepare the row according to the header order
                    row_to_write = [filename, title, keywords_string]
                    writer.writerow(row_to_write)
                    
                except ValueError as unpack_err:
                    logging.error(f"Error unpacking metadata item #{item_index+1}: {metadata_item}. Error: {unpack_err}. Skipping item.")
                    continue
                except Exception as loop_err:
                    logging.error(f"Unexpected error processing item #{item_index+1}: {loop_err}")
                    continue
            
        logging.info(f"Successfully generated Shutterstock CSV: {output_csv_path} with {len(metadata_list)} data rows.")
        return True
        
    except IOError as e:
        logging.error(f"Error writing Shutterstock CSV file {output_csv_path}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error generating Shutterstock CSV {output_csv_path}: {e}")
        return False

def main():
    """Main function to process command line arguments and generate CSV."""
    parser = argparse.ArgumentParser(description="Generate Shutterstock CSV from image/video metadata")
    parser.add_argument("source", help="File, directory, or comma-separated list of files/directories to process")
    parser.add_argument("--output", "-o", default=None, help="Output CSV file path (default: Desktop/shutterstock_metadata.csv)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Set logging level based on verbose flag
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Handle comma-separated list of sources
    if "," in args.source:
        source_list = [s.strip() for s in args.source.split(",")]
    else:
        source_list = args.source
    
    # Get files to process
    files_to_process = get_files_to_process(source_list)
    
    if not files_to_process:
        logging.error("No supported files found to process.")
        return 1
    
    logging.info(f"Found {len(files_to_process)} file(s) to process")
    
    # Extract metadata from each file
    metadata_list = []
    for file_path in files_to_process:
        metadata = extract_metadata(file_path)
        if metadata:
            metadata_list.append(metadata)
    
    if not metadata_list:
        logging.error("No metadata could be extracted from the files.")
        return 1
    
    # Determine output path
    if args.output:
        output_csv_path = args.output
    else:
        # Default to Desktop
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        os.makedirs(os.path.join(desktop_path, "CSV", "Shutterstock"), exist_ok=True)
        output_csv_path = os.path.join(desktop_path, "CSV", "Shutterstock", "shutterstock_metadata.csv")
    
    # Generate CSV
    success = generate_shutterstock_csv(metadata_list, output_csv_path)
    
    if success:
        logging.info(f"CSV generation complete. File saved to: {output_csv_path}")
        return 0
    else:
        logging.error("CSV generation failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
