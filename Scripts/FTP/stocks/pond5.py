# -*- coding: utf-8 -*-
import os
import csv
import logging
import ftplib
import pyexiv2
import tempfile
import socket
from Scripts.utils import mark_as_done, check_for_matching_video

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Pond5 Specific Configuration
POND5_HOSTNAME = "ftp.pond5.com"
POND5_PORT = 21  # Default FTP port

def upload_to_pond5_ftp(username, password, source_path, hostname=POND5_HOSTNAME, port=POND5_PORT):
    """
    Uploads files/directories to Pond5 via FTP.

    Args:
        username (str): Pond5 FTP username
        password (str): Pond5 FTP password
        source_path (str): Absolute path to the local file or directory to upload
        hostname (str): Pond5 FTP hostname
        port (int): FTP port

    Returns:
        list or bool:
            - If uploading media files: List of successfully uploaded filenames
            - If uploading a single CSV file: True if successful, False otherwise
    """
    uploaded_files = []
    ftp = None
    is_single_file_upload = os.path.isfile(source_path)
    is_csv_upload = is_single_file_upload and source_path.lower().endswith('.csv')

    try:
        logging.info(f"Connecting to Pond5 FTP: {hostname}:{port} as {username}")
        ftp = ftplib.FTP()
        # Set longer timeout
        ftp.timeout = 300  # 5 minutes timeout

        # Connect and configure
        ftp.connect(hostname, port, timeout=60)  # 60 seconds connection timeout
        ftp.login(username, password)
        ftp.encoding = 'utf-8'

        # Enable passive mode and configure buffer size
        ftp.set_pasv(True)

        # Set binary mode for transfers
        ftp.voidcmd('TYPE I')

        logging.info("Pond5 FTP connection established.")

        # Handle single file or directory upload
        if is_single_file_upload:
            # Check if this is an image with a matching video file
            has_matching_video = False
            video_path = None
            file_to_upload = source_path

            # Only check for matching videos if not a CSV file
            if not is_csv_upload:
                has_matching_video, video_path = check_for_matching_video(source_path)
                if has_matching_video and video_path:
                    file_to_upload = video_path
                    logging.info(f"Found matching video for {os.path.basename(source_path)}: {os.path.basename(video_path)}")

            # Get the filename to use for the upload
            if has_matching_video and video_path:
                # Use the video filename for upload
                filename = os.path.basename(video_path)
                # We'll still mark the image file as done after upload
                file_to_mark = source_path
            else:
                filename = os.path.basename(source_path)
                file_to_mark = source_path

            logging.info(f"Uploading file to Pond5: {file_to_upload}")

            try:
                with open(file_to_upload, 'rb') as file:
                    # Use larger buffer size and callback for progress
                    ftp.storbinary(f'STOR {filename}', file, blocksize=262144,  # 256KB blocks
                                 callback=lambda block: logging.debug(f"Uploaded {len(block)} bytes"))
                logging.info(f"Successfully uploaded {filename} to Pond5")

                if not is_csv_upload:
                    uploaded_files.append(filename)  # Add the actual uploaded filename
                    try:
                        # Mark the original file as done (image file in case of video)
                        mark_as_done(file_to_mark, "Pond5")
                    except Exception as e:
                        logging.warning(f"Couldn't mark the file {file_to_mark} as done for Pond5: {e}")
                return True if is_csv_upload else uploaded_files

            except ftplib.error_temp as temp_err:
                logging.error(f"Temporary FTP error uploading {filename}: {temp_err}")
                return False if is_csv_upload else uploaded_files
            except socket.timeout:
                logging.error(f"Timeout uploading {filename}. Consider increasing timeout or checking network.")
                return False if is_csv_upload else uploaded_files
            except Exception as e:
                logging.error(f"Failed to upload {filename}: {e}")
                return False if is_csv_upload else uploaded_files

        elif os.path.isdir(source_path):
            logging.info(f"Uploading directory contents to Pond5: {source_path}")
            # First, scan the directory to identify image files with matching videos
            image_to_video_map = {}
            for item in os.listdir(source_path):
                local_item_path = os.path.join(source_path, item)
                if os.path.isfile(local_item_path) and not item.lower().endswith('.csv'):
                    # Check if this is an image with a matching video
                    has_matching_video, video_path = check_for_matching_video(local_item_path)
                    if has_matching_video and video_path:
                        image_to_video_map[local_item_path] = video_path

            # Now process all files in the directory
            for item in os.listdir(source_path):
                local_item_path = os.path.join(source_path, item)
                if os.path.isfile(local_item_path) and not item.lower().endswith('.csv'):
                    # Skip video files that will be uploaded via their image counterparts
                    if any(video_path == local_item_path for video_path in image_to_video_map.values()):
                        logging.info(f"Skipping video file {item} - will be uploaded via its image counterpart")
                        continue

                    # Determine what to upload
                    if local_item_path in image_to_video_map:
                        # This is an image with a matching video - upload the video instead
                        video_path = image_to_video_map[local_item_path]
                        file_to_upload = video_path
                        upload_filename = os.path.basename(video_path)
                        file_to_mark = local_item_path  # Mark the image as done
                        logging.info(f"Uploading video for image {item}: {os.path.basename(video_path)}")
                    else:
                        # Regular file - upload as is
                        file_to_upload = local_item_path
                        upload_filename = item
                        file_to_mark = local_item_path
                        logging.info(f"Uploading file to Pond5: {item}")

                    try:
                        with open(file_to_upload, 'rb') as file:
                            ftp.voidcmd('TYPE I')  # Ensure binary mode for each file
                            ftp.storbinary(f'STOR {upload_filename}', file, blocksize=262144)
                        logging.info(f"Successfully uploaded {upload_filename} to Pond5")
                        uploaded_files.append(upload_filename)
                        try:
                            mark_as_done(file_to_mark, "Pond5")
                        except Exception as mark_err:
                            logging.warning(f"Couldn't mark the file {file_to_mark} as done for Pond5: {mark_err}")
                    except Exception as e:
                        logging.error(f"Failed to upload {item}: {e}")
                elif item.lower().endswith('.csv'):
                    logging.info(f"Skipping CSV file '{item}' during directory content upload")
                else:
                    logging.warning(f"Skipping non-file item in directory: {item}")
            return uploaded_files

        else:
            logging.error(f"Source path is neither a file nor a directory: {source_path}")
            return [] if not is_single_file_upload else False

    except ftplib.error_perm as perm_err:
        logging.error(f"FTP permission error during Pond5 upload: {perm_err}")
        return [] if not is_single_file_upload else False
    except socket.timeout:
        logging.error("FTP connection timed out. Check network connection and firewall settings.")
        return [] if not is_single_file_upload else False
    except ftplib.all_errors as e:
        logging.error(f"FTP error during Pond5 upload: {e}")
        return [] if not is_single_file_upload else False
    except Exception as e:
        logging.error(f"An error occurred during Pond5 FTP operation: {e}", exc_info=True)
        return [] if not is_single_file_upload else False
    finally:
        if ftp:
            try:
                ftp.quit()
            except:
                try:
                    ftp.close()
                except:
                    pass
            logging.info("Pond5 FTP connection closed.")

# --- Metadata Extraction (Largely Unchanged) ---

def extract_metadata(file_path):
    """
    Extracts Title and Keywords from image metadata (EXIF/IPTC/XMP) using a
    robust method, adapted for CSV generation.

    If the file is a video or has a matching video file, the metadata is extracted from
    the image but the video filename is used in the CSV.

    Args:
        file_path (str): Absolute path to the image file.

    Returns:
        tuple: (filename, title, keywords_string) or None if extraction fails.
               Keywords are returned as a comma-separated string.
               Returns empty strings for title/keywords if tags are not found.
    """
    # Check if this is an image with a matching video file
    has_matching_video, video_path = check_for_matching_video(file_path)

    # Determine which filename to use in the CSV
    if has_matching_video and video_path:
        # Use the video filename for the CSV
        filename_for_csv = os.path.basename(video_path)
        # But extract metadata from the image file
        metadata_source_path = file_path
        logging.info(f"Using video filename {filename_for_csv} but extracting metadata from {os.path.basename(file_path)}")
    else:
        # Regular file - use its own filename and extract metadata from it
        filename_for_csv = os.path.basename(file_path)
        metadata_source_path = file_path

    image_exif = None
    title = ''
    keywords = [] # Store final keywords list here

    # Check if the metadata source file exists before attempting to open
    if not os.path.isfile(metadata_source_path):
        logging.error(f"Metadata extraction skipped: File not found or not a file at {metadata_source_path}")
        return None

    try:
        # --- Read Metadata using pyexiv2 ---
        logging.debug(f"Attempting to read metadata for {os.path.basename(metadata_source_path)}")
        image_exif = pyexiv2.Image(metadata_source_path)
        raw_exif = image_exif.read_exif()
        raw_iptc = image_exif.read_iptc()
        raw_xmp = image_exif.read_xmp()
        logging.debug(f"Raw metadata read for {os.path.basename(metadata_source_path)}")

        # --- Extract Title (Priority: EXIF XPTitle > IPTC ObjectName > XMP dc:title) ---
        xmp_title = ''
        if 'Xmp.dc.title' in raw_xmp:
            value = raw_xmp['Xmp.dc.title']
            if isinstance(value, list) and value:
                xmp_title = value[0]
            elif isinstance(value, str):
                xmp_title = value

        title_bytes_or_str = raw_exif.get('Exif.Image.XPTitle',
                                          raw_iptc.get('Iptc.Application2.ObjectName',
                                                       xmp_title))

        if isinstance(title_bytes_or_str, bytes):
            try:
                title = title_bytes_or_str.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not title:
                    title = title_bytes_or_str.decode('utf-8', errors='ignore').rstrip('\x00')
            except Exception as dec_err:
                logging.warning(f"Could not decode title bytes for {filename_for_csv}: {dec_err}. Raw: {title_bytes_or_str}")
                title = str(title_bytes_or_str) # Fallback
        elif title_bytes_or_str:
            title = str(title_bytes_or_str)
        else:
            title = ''

        # --- Extract Keywords (Combine EXIF, IPTC, XMP) ---
        keywords_semi = raw_exif.get('Exif.Image.XPKeywords', b'')
        keywords_iptc = raw_iptc.get('Iptc.Application2.Keywords', [])
        keywords_xmp = raw_xmp.get('Xmp.dc.subject', [])

        kw_set = set()

        # Process EXIF XPKeywords
        if isinstance(keywords_semi, bytes) and keywords_semi:
            try:
                kw_str = keywords_semi.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not kw_str and len(keywords_semi) > 0:
                    kw_str = keywords_semi.decode('utf-8', errors='ignore').rstrip('\x00')
                if kw_str:
                    kw_set.update(k.strip() for k in kw_str.split(';') if k.strip())
            except Exception as dec_err:
                logging.warning(f"Could not decode XPKeywords bytes for {filename_for_csv}: {dec_err}. Raw: {keywords_semi}")
        elif isinstance(keywords_semi, str) and keywords_semi:
             kw_set.update(k.strip() for k in keywords_semi.split(';') if k.strip())

        # Process IPTC Keywords
        if isinstance(keywords_iptc, list):
            for k in keywords_iptc:
                decoded_k = ""
                if isinstance(k, bytes):
                    try:
                         decoded_k = k.decode('utf-8', errors='ignore').strip()
                    except Exception as dec_err:
                         logging.warning(f"Could not decode IPTC keyword bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k:
                    decoded_k = str(k).strip()
                if decoded_k: kw_set.add(decoded_k) # Add only if non-empty after decode/strip

        # Process XMP Subject
        if isinstance(keywords_xmp, list):
            for k in keywords_xmp:
                decoded_k = ""
                if isinstance(k, bytes):
                    try:
                         decoded_k = k.decode('utf-8', errors='ignore').strip()
                    except Exception as dec_err:
                         logging.warning(f"Could not decode XMP subject bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k:
                     decoded_k = str(k).strip()
                if decoded_k: kw_set.add(decoded_k) # Add only if non-empty after decode/strip

        kw_set.discard('') # Remove empty strings again just in case
        keywords = sorted(list(kw_set))

        # --- Prepare return values ---
        keywords_string = ",".join(keywords) # Comma-separated for Pond5 CSV

        logging.info(f"Extracted metadata for {os.path.basename(metadata_source_path)}: Title='{title[:50]}...', Keywords='{keywords_string[:50]}...' ({len(keywords)} keywords)")
        # Return the filename to use in CSV (video filename if available), along with metadata
        return filename_for_csv, title, keywords_string

    except FileNotFoundError:
        logging.error(f"Metadata extraction failed: File not found at {file_path}")
        return None
    except Exception as e:
        logging.error(f"Error extracting metadata from {filename_for_csv}: {e}", exc_info=True)
        return None
    finally:
        if image_exif:
            try:
                image_exif.close()
                logging.debug(f"Closed pyexiv2 image object for {filename_for_csv}")
            except Exception as close_err:
                logging.warning(f"Error closing pyexiv2 Image object for {filename_for_csv}: {close_err}")


# --- Pond5 CSV Generation ---

def generate_pond5_csv(metadata_list, output_csv_path):
    """
    Generates a CSV file for Pond5 based on extracted metadata.

    Args:
        metadata_list (list): A list of tuples [(filename, title, keywords_string), ...].
        output_csv_path (str): The full path where the CSV file should be saved.

    Returns:
        bool: True if CSV generation was successful, False otherwise.
    """
    # Common Pond5 Headers (Adjust if your needs differ)
    header = ['originalfilename', 'title', 'description', 'keywords']
    # Optional common headers you might add: 'Category', 'Price', 'Country', 'City', 'State/Province', 'Release Filenames'
    logging.info(f"Attempting to generate Pond5 CSV at: {output_csv_path}")
    logging.debug(f"Data received for Pond5 CSV (first 5 items): {metadata_list[:5]}")

    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(header)
            logging.debug(f"Pond5 CSV Header written to {output_csv_path}")

            if not metadata_list:
                logging.warning("metadata_list provided to generate_pond5_csv is empty.")

            for item_index, metadata_item in enumerate(metadata_list):
                try:
                    filename, title, keywords_string = metadata_item
                    logging.debug(f"POND5_CSV_ROW {item_index+1} - Unpacked: Filename='{filename}', Title='{title}', Keywords='{keywords_string}'")

                    # --- Map extracted data to Pond5 columns ---
                    # Using extracted title for both Title and Description for simplicity
                    description = title
                    # Optional: Set other fields like Category, Price etc. here if needed
                    # category = "Nature" # Example
                    # price = "" # Example: Leave blank for default or set value

                    # Prepare the row list according to the header order
                    row_to_write = [filename, title, description, keywords_string]
                    logging.debug(f"POND5_CSV_ROW {item_index+1} - Writing row: {row_to_write}")

                    writer.writerow(row_to_write)

                except ValueError as unpack_err:
                    logging.error(f"Error unpacking metadata item #{item_index+1} in generate_pond5_csv: {metadata_item}. Error: {unpack_err}. Skipping item.")
                    continue
                except Exception as loop_err:
                    logging.error(f"Unexpected error processing item #{item_index+1} ({metadata_item}) in generate_pond5_csv loop: {loop_err}", exc_info=True)
                    continue

        logging.info(f"Successfully generated Pond5 CSV: {output_csv_path} with {len(metadata_list)} data rows.")
        return True
    except IOError as e:
        logging.error(f"Error writing Pond5 CSV file {output_csv_path}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error generating Pond5 CSV {output_csv_path}: {e}", exc_info=True)
        return False


# --- Main Pond5 Process Orchestrator ---

def upload_pond5_process(ftp_username, ftp_password, source_path_or_list, pond5_hostname=POND5_HOSTNAME, pond5_port=POND5_PORT):
    """
    Orchestrates the full Pond5 upload process:
    1. Uploads media file(s) via FTP
    2. Extracts metadata
    3. Generates CSV
    4. Uploads CSV via FTP

    Args:
        ftp_username (str): Pond5 FTP username
        ftp_password (str): Pond5 FTP password
        source_path_or_list (str or list): Path(s) to upload
        pond5_hostname (str): Pond5 FTP hostname
        pond5_port (int): FTP port

    Returns:
        bool: True if successful, False otherwise
    """
    logging.info(f"Starting full Pond5 upload process for: {source_path_or_list}")

    all_uploaded_filenames = [] # Accumulate media filenames from all SFTP uploads
    source_files_map = {} # Map filename back to original full path for metadata extraction

    # --- Step 1: Upload Media Files via FTP (Handle list or single path) ---
    if isinstance(source_path_or_list, list):
        logging.info("Processing a list of source paths for Pond5.")
        for item_path in source_path_or_list:
            abs_item_path = os.path.abspath(item_path)
            if not os.path.exists(abs_item_path):
                logging.warning(f"Source path in list does not exist: {abs_item_path}. Skipping.")
                continue

            uploaded_for_item = [] # Files uploaded from this specific item
            if os.path.isfile(abs_item_path):
                 # Check if it's a CSV - we don't upload these in the first pass
                 if abs_item_path.lower().endswith('.csv'):
                      logging.info(f"Skipping CSV file '{abs_item_path}' found in source list during media upload phase.")
                      continue
                 # Upload single media file
                 success = upload_to_pond5_ftp(ftp_username, ftp_password, abs_item_path, pond5_hostname, pond5_port)
                 if success: # upload_to_pond5_ftp returns True for single file success
                     filename = os.path.basename(abs_item_path)
                     uploaded_for_item.append(filename)
                     source_files_map[filename] = abs_item_path # Map successful upload
            elif os.path.isdir(abs_item_path):
                 logging.info(f"Processing directory from list: {abs_item_path}")
                 # Upload directory contents (returns list of uploaded filenames)
                 uploaded_in_dir = upload_to_pond5_ftp(ftp_username, ftp_password, abs_item_path, pond5_hostname, pond5_port)
                 uploaded_for_item.extend(uploaded_in_dir)
                 # Map files *within* the directory after successful upload
                 for fname in uploaded_in_dir:
                      potential_path = os.path.join(abs_item_path, fname)
                      if os.path.exists(potential_path):
                           source_files_map[fname] = potential_path
                      else:
                           logging.warning(f"Could not map uploaded file '{fname}' from directory '{abs_item_path}' back to source.")
            else:
                logging.warning(f"Item in source list is neither file nor directory: {abs_item_path}. Skipping.")

            all_uploaded_filenames.extend(uploaded_for_item) # Add successfully uploaded files from this item to the main list

    elif isinstance(source_path_or_list, str):
        logging.info("Processing a single source path for Pond5.")
        abs_source_path = os.path.abspath(source_path_or_list)
        if not os.path.exists(abs_source_path):
            logging.error(f"Source path does not exist: {abs_source_path}")
            return False

        if os.path.isfile(abs_source_path):
            # Check if it's a CSV - we don't upload these in the first pass
            if abs_source_path.lower().endswith('.csv'):
                 logging.error(f"Single source path provided is a CSV file: '{abs_source_path}'. This process uploads media first, then generates and uploads the CSV. Provide media file path(s) or directory.")
                 return False
            # Upload single media file
            success = upload_to_pond5_ftp(ftp_username, ftp_password, abs_source_path, pond5_hostname, pond5_port)
            if success:
                filename = os.path.basename(abs_source_path)
                all_uploaded_filenames.append(filename)
                source_files_map[filename] = abs_source_path # Map successful upload
        elif os.path.isdir(abs_source_path):
             # Upload directory contents
             uploaded_in_dir = upload_to_pond5_ftp(ftp_username, ftp_password, abs_source_path, pond5_hostname, pond5_port)
             all_uploaded_filenames.extend(uploaded_in_dir)
             # Build map after upload for the single source directory
             if uploaded_in_dir:
                  for fname in uploaded_in_dir:
                       potential_path = os.path.join(abs_source_path, fname)
                       if os.path.exists(potential_path):
                            source_files_map[fname] = potential_path
                       else:
                            logging.warning(f"Could not map uploaded file '{fname}' from directory '{abs_source_path}' back to source.")
        else:
             logging.error(f"Single source path is neither file nor directory: {abs_source_path}")
             return False
    else:
        logging.error(f"Invalid source_path_or_list type: {type(source_path_or_list)}. Must be string or list.")
        return False

    # --- Step 1 check ---
    if not all_uploaded_filenames:
        logging.error("FTP upload failed or no media files were uploaded to Pond5. Aborting CSV process.")
        return False

    logging.info(f"Successfully FTP'd {len(all_uploaded_filenames)} media files to Pond5 in total: {all_uploaded_filenames}")
    logging.debug(f"Source files map created: {source_files_map}")

    # --- Step 2: Extract Metadata ---
    metadata_for_csv = []
    logging.info("Extracting metadata from original source files using the map...")

    # Iterate through unique filenames that were successfully uploaded
    unique_uploaded_filenames = sorted(list(set(all_uploaded_filenames)))

    for filename in unique_uploaded_filenames:
        if filename in source_files_map:
            original_file_path = source_files_map[filename]
            logging.info(f"Extracting metadata for {filename} (from {original_file_path})")
            metadata = extract_metadata(original_file_path)
            if metadata:
                if len(metadata) == 3:
                    metadata_for_csv.append(metadata)
                else:
                    logging.warning(f"Incorrect metadata tuple format returned for {filename}: {metadata}. Skipping.")
            else:
                logging.warning(f"Failed to extract metadata for {filename} (source: {original_file_path}). It will be excluded or have empty fields in Pond5 CSV.")
                # Optionally add with empty fields: metadata_for_csv.append((filename, "", ""))
        else:
            logging.warning(f"Filename '{filename}' was uploaded but couldn't be mapped back to a source file path. Skipping metadata extraction.")

    if not metadata_for_csv:
        logging.error("No metadata could be extracted for the uploaded files. Cannot generate Pond5 CSV.")
        # Note: Files are already uploaded to Pond5 at this point. Manual CSV might be needed.
        return False # Indicate failure as CSV wasn't generated/uploaded.

    # --- Step 3: Generate Pond5 CSV ---
    csv_dir = os.path.expanduser("~/Desktop/CSV/Pond5") # Specific directory for Pond5
    os.makedirs(csv_dir, exist_ok=True)
    csv_filename = "pond5_metadata.csv" # Pond5 often looks for metadata.csv or similar
    csv_final_path = os.path.join(csv_dir, csv_filename)
    temp_csv_path = ""

    try:
        # Use a temporary file for safety during generation
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".csv", encoding='utf-8', newline='') as temp_csv_file:
            temp_csv_path = temp_csv_file.name
        # Note: Closed the temp file, generate_pond5_csv will reopen and write
        logging.info(f"Generating Pond5 metadata CSV at temporary path: {temp_csv_path} for {len(metadata_for_csv)} files.")
        if not generate_pond5_csv(metadata_for_csv, temp_csv_path):
            logging.error("Failed to generate Pond5 metadata CSV.")
            if os.path.exists(temp_csv_path): os.remove(temp_csv_path)
            return False # Indicate failure

        # Move the temporary CSV to the final destination
        os.replace(temp_csv_path, csv_final_path)
        logging.info(f"Pond5 CSV successfully saved locally to: {csv_final_path}")

    except Exception as e:
        logging.error(f"Error during Pond5 CSV file creation/saving: {e}", exc_info=True)
        if temp_csv_path and os.path.exists(temp_csv_path):
             try:
                 os.remove(temp_csv_path)
             except OSError as rm_err:
                 logging.error(f"Error removing temporary CSV file {temp_csv_path}: {rm_err}")
        return False # Indicate failure

    # --- Step 4: Upload the Generated CSV via FTP ---
    logging.info(f"Attempting to upload the generated CSV file '{csv_final_path}' to Pond5 FTP...")
    csv_upload_success = upload_to_pond5_ftp(ftp_username, ftp_password, csv_final_path, pond5_hostname, pond5_port)

    if csv_upload_success:
        logging.info(f"Successfully uploaded Pond5 metadata CSV via FTP.")
        return True
    else:
        logging.error(f"Failed to upload the Pond5 metadata CSV via FTP.")
        return False
