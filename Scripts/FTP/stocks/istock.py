# -*- coding: utf-8 -*-
import os
import csv
import logging
import pyexiv2
import tempfile
from datetime import datetime
from Scripts.utils import check_for_matching_video

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Metadata Extraction for iStock ---

def extract_metadata(file_path):
    """
    Extracts metadata from image/video files for iStock CSV generation.

    If the file is a video or has a matching video file, the metadata is extracted from
    the image but the video filename is used in the CSV.

    Args:
        file_path (str): Absolute path to the image file.

    Returns:
        tuple: (filename, title, description, keywords_string, date_created) or None if extraction fails.
               Returns empty strings for fields if tags are not found.
    """
    # Check if this is an image with a matching video file
    has_matching_video, video_path = check_for_matching_video(file_path)

    # Determine which filename to use in the CSV
    if has_matching_video and video_path:
        # Use the video filename for the CSV
        filename_for_csv = os.path.basename(video_path)
        # But extract metadata from the image file
        metadata_source_path = file_path
        logging.info(f"Using video filename {filename_for_csv} but extracting metadata from {os.path.basename(file_path)}")
    else:
        # Regular file - use its own filename and extract metadata from it
        filename_for_csv = os.path.basename(file_path)
        metadata_source_path = file_path

    # Ensure proper file extensions for iStock
    name, ext = os.path.splitext(filename_for_csv)
    if not ext:
        # Add default extensions if missing (per iStock rules)
        if has_matching_video and video_path:
            filename_for_csv = name + '.mov'  # Default for videos
        else:
            filename_for_csv = name + '.jpg'  # Default for images

    image_exif = None
    title = ''
    description = ''
    keywords = []
    date_created = ''

    # Check if the metadata source file exists before attempting to open
    if not os.path.isfile(metadata_source_path):
        logging.error(f"Metadata extraction skipped: File not found or not a file at {metadata_source_path}")
        return None

    try:
        # --- Read Metadata using pyexiv2 ---
        logging.debug(f"Attempting to read metadata for {os.path.basename(metadata_source_path)}")
        image_exif = pyexiv2.Image(metadata_source_path)
        raw_exif = image_exif.read_exif()
        raw_iptc = image_exif.read_iptc()
        raw_xmp = image_exif.read_xmp()
        logging.debug(f"Raw metadata read for {os.path.basename(metadata_source_path)}")

        # --- Extract Title (Priority: EXIF XPTitle > IPTC ObjectName > XMP dc:title) ---
        xmp_title = ''
        if 'Xmp.dc.title' in raw_xmp:
            value = raw_xmp['Xmp.dc.title']
            if isinstance(value, list) and value:
                xmp_title = value[0]
            elif isinstance(value, str):
                xmp_title = value

        title_bytes_or_str = raw_exif.get('Exif.Image.XPTitle',
                                          raw_iptc.get('Iptc.Application2.ObjectName',
                                                       xmp_title))

        if isinstance(title_bytes_or_str, bytes):
            try:
                title = title_bytes_or_str.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not title:
                    title = title_bytes_or_str.decode('utf-8', errors='ignore').rstrip('\x00')
            except Exception as dec_err:
                logging.warning(f"Could not decode title bytes for {filename_for_csv}: {dec_err}. Raw: {title_bytes_or_str}")
                title = str(title_bytes_or_str) # Fallback
        elif title_bytes_or_str:
            title = str(title_bytes_or_str)
        else:
            title = ''

        # Use title as description if no separate description is found
        description = title

        # --- Extract Keywords (Combine EXIF, IPTC, XMP) ---
        keywords_semi = raw_exif.get('Exif.Image.XPKeywords', b'')
        keywords_iptc = raw_iptc.get('Iptc.Application2.Keywords', [])
        keywords_xmp = raw_xmp.get('Xmp.dc.subject', [])

        kw_set = set()

        # Process EXIF XPKeywords
        if isinstance(keywords_semi, bytes) and keywords_semi:
            try:
                kw_str = keywords_semi.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not kw_str and len(keywords_semi) > 0:
                    kw_str = keywords_semi.decode('utf-8', errors='ignore').rstrip('\x00')
                if kw_str:
                    kw_set.update(k.strip() for k in kw_str.split(';') if k.strip())
            except Exception as dec_err:
                logging.warning(f"Could not decode XPKeywords bytes for {filename_for_csv}: {dec_err}. Raw: {keywords_semi}")
        elif isinstance(keywords_semi, str) and keywords_semi:
             kw_set.update(k.strip() for k in keywords_semi.split(';') if k.strip())

        # Process IPTC Keywords
        if isinstance(keywords_iptc, list):
            for k in keywords_iptc:
                decoded_k = ""
                if isinstance(k, bytes):
                    try:
                         decoded_k = k.decode('utf-8', errors='ignore').strip()
                    except Exception as dec_err:
                         logging.warning(f"Could not decode IPTC keyword bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k:
                    decoded_k = str(k).strip()
                if decoded_k: kw_set.add(decoded_k)

        # Process XMP Subject
        if isinstance(keywords_xmp, list):
            for k in keywords_xmp:
                decoded_k = ""
                if isinstance(k, bytes):
                    try:
                         decoded_k = k.decode('utf-8', errors='ignore').strip()
                    except Exception as dec_err:
                         logging.warning(f"Could not decode XMP subject bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k:
                     decoded_k = str(k).strip()
                if decoded_k: kw_set.add(decoded_k)

        kw_set.discard('')
        keywords = sorted(list(kw_set))

        # --- Extract Date Created ---
        # Try to get creation date from EXIF
        date_created = ''
        date_fields = [
            'Exif.Image.DateTime',
            'Exif.Photo.DateTimeOriginal',
            'Exif.Photo.DateTimeDigitized',
            'Iptc.Application2.DateCreated'
        ]
        
        for field in date_fields:
            if field in raw_exif:
                try:
                    date_str = str(raw_exif[field])
                    # Convert to iStock format (YYYY-MM-DD)
                    if ':' in date_str and len(date_str) >= 10:
                        # Format: "YYYY:MM:DD HH:MM:SS" -> "YYYY-MM-DD"
                        date_created = date_str[:10].replace(':', '-')
                        break
                except Exception as e:
                    logging.warning(f"Error parsing date from {field}: {e}")
            elif field in raw_iptc:
                try:
                    date_str = str(raw_iptc[field])
                    # IPTC dates are usually YYYYMMDD
                    if len(date_str) == 8 and date_str.isdigit():
                        date_created = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                        break
                except Exception as e:
                    logging.warning(f"Error parsing IPTC date from {field}: {e}")

        # If no date found in metadata, use file modification time
        if not date_created:
            try:
                file_mtime = os.path.getmtime(metadata_source_path)
                date_created = datetime.fromtimestamp(file_mtime).strftime('%Y-%m-%d')
            except Exception as e:
                logging.warning(f"Error getting file modification time: {e}")
                date_created = datetime.now().strftime('%Y-%m-%d')

        # --- Prepare return values ---
        keywords_string = ",".join(keywords)  # Comma-separated for iStock CSV

        logging.info(f"Extracted metadata for {os.path.basename(metadata_source_path)}: Title='{title[:50]}...', Keywords='{keywords_string[:50]}...' ({len(keywords)} keywords)")
        # Return the filename to use in CSV (video filename if available), along with metadata
        return filename_for_csv, title, description, keywords_string, date_created

    except FileNotFoundError:
        logging.error(f"Metadata extraction failed: File not found at {file_path}")
        return None
    except Exception as e:
        logging.error(f"Error extracting metadata from {filename_for_csv}: {e}", exc_info=True)
        return None
    finally:
        if image_exif:
            try:
                image_exif.close()
                logging.debug(f"Closed pyexiv2 image object for {filename_for_csv}")
            except Exception as close_err:
                logging.warning(f"Error closing pyexiv2 Image object for {filename_for_csv}: {close_err}")


# --- iStock CSV Generation ---

def generate_istock_csv(metadata_list, output_csv_path):
    """
    Generates a CSV file for iStock based on extracted metadata.
    
    iStock CSV Template:
    file name,description,country,title,poster timecode,keywords,date created,shot speed

    Args:
        metadata_list (list): A list of tuples [(filename, title, description, keywords_string, date_created), ...].
        output_csv_path (str): The full path where the CSV file should be saved.

    Returns:
        bool: True if CSV generation was successful, False otherwise.
    """
    # iStock CSV Headers - must match their template exactly
    header = ['file name', 'description', 'country', 'title', 'poster timecode', 'keywords', 'date created', 'shot speed']
    
    logging.info(f"Attempting to generate iStock CSV at: {output_csv_path}")
    logging.debug(f"Data received for iStock CSV (first 5 items): {metadata_list[:5]}")

    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(header)
            logging.debug(f"iStock CSV Header written to {output_csv_path}")

            if not metadata_list:
                logging.warning("metadata_list provided to generate_istock_csv is empty.")

            for item_index, metadata_item in enumerate(metadata_list):
                try:
                    filename, title, description, keywords_string, date_created = metadata_item
                    logging.debug(f"ISTOCK_CSV_ROW {item_index+1} - Unpacked: Filename='{filename}', Title='{title}', Keywords='{keywords_string}'")

                    # --- Map extracted data to iStock columns ---
                    # file name - required, cannot be empty
                    file_name = filename
                    
                    # description - use extracted description/title
                    csv_description = description
                    
                    # country - leave empty for user to fill
                    country = ""
                    
                    # title - use extracted title
                    csv_title = title
                    
                    # poster timecode - leave empty (mainly for videos)
                    poster_timecode = ""
                    
                    # keywords - comma-separated
                    csv_keywords = keywords_string
                    
                    # date created - YYYY-MM-DD format
                    csv_date_created = date_created
                    
                    # shot speed - leave empty for user to fill
                    shot_speed = ""

                    # Prepare the row list according to the header order
                    row_to_write = [file_name, csv_description, country, csv_title, poster_timecode, csv_keywords, csv_date_created, shot_speed]
                    logging.debug(f"ISTOCK_CSV_ROW {item_index+1} - Writing row: {row_to_write}")

                    writer.writerow(row_to_write)

                except ValueError as unpack_err:
                    logging.error(f"Error unpacking metadata item #{item_index+1} in generate_istock_csv: {metadata_item}. Error: {unpack_err}. Skipping item.")
                    continue
                except Exception as loop_err:
                    logging.error(f"Unexpected error processing item #{item_index+1} ({metadata_item}) in generate_istock_csv loop: {loop_err}", exc_info=True)
                    continue

        logging.info(f"Successfully generated iStock CSV: {output_csv_path} with {len(metadata_list)} data rows.")
        return True
    except IOError as e:
        logging.error(f"Error writing iStock CSV file {output_csv_path}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error generating iStock CSV {output_csv_path}: {e}", exc_info=True)
        return False


# --- Main iStock CSV Generation Function ---

def generate_istock_csv_from_files(source_path_or_list):
    """
    Generates an iStock CSV file from the provided files.
    
    Args:
        source_path_or_list (str or list): Path(s) to process for CSV generation

    Returns:
        tuple: (success, output_csv_path) where success is a boolean and
               output_csv_path is the path to the generated CSV file
    """
    logging.info(f"Starting iStock CSV generation for: {source_path_or_list}")

    # Collect all files to process
    files_to_process = []
    
    if isinstance(source_path_or_list, list):
        for item_path in source_path_or_list:
            abs_item_path = os.path.abspath(item_path)
            if not os.path.exists(abs_item_path):
                logging.warning(f"Source path in list does not exist: {abs_item_path}. Skipping.")
                continue
                
            if os.path.isfile(abs_item_path):
                files_to_process.append(abs_item_path)
            elif os.path.isdir(abs_item_path):
                # Process all supported files in the directory
                for item in os.listdir(abs_item_path):
                    local_item_path = os.path.join(abs_item_path, item)
                    if os.path.isfile(local_item_path):
                        # Check for common image/video extensions
                        ext = os.path.splitext(item)[1].lower()
                        if ext in ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp', '.gif', 
                                  '.mp4', '.mov', '.avi', '.wmv', '.mpg', '.mpeg']:
                            files_to_process.append(local_item_path)
                            
    elif isinstance(source_path_or_list, str):
        abs_source_path = os.path.abspath(source_path_or_list)
        if not os.path.exists(abs_source_path):
            logging.error(f"Source path does not exist: {abs_source_path}")
            return False, None
            
        if os.path.isfile(abs_source_path):
            files_to_process.append(abs_source_path)
        elif os.path.isdir(abs_source_path):
            # Process all supported files in the directory
            for item in os.listdir(abs_source_path):
                local_item_path = os.path.join(abs_source_path, item)
                if os.path.isfile(local_item_path):
                    # Check for common image/video extensions
                    ext = os.path.splitext(item)[1].lower()
                    if ext in ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp', '.gif', 
                              '.mp4', '.mov', '.avi', '.wmv', '.mpg', '.mpeg']:
                        files_to_process.append(local_item_path)
    else:
        logging.error(f"Invalid source_path_or_list type: {type(source_path_or_list)}. Must be string or list.")
        return False, None

    if not files_to_process:
        logging.error("No supported files found to process for iStock CSV.")
        return False, None

    logging.info(f"Found {len(files_to_process)} files to process for iStock CSV.")

    # --- Extract Metadata ---
    metadata_for_csv = []
    processed_filenames = set()  # Track processed filenames to avoid duplicates

    for file_path in files_to_process:
        # Skip video files that will be processed via their image counterparts
        has_matching_video, video_path = check_for_matching_video(file_path)
        if video_path and video_path in files_to_process and file_path == video_path:
            # This is a video file, check if its corresponding image is in the list
            image_name = os.path.splitext(file_path)[0]
            image_extensions = ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp']
            skip_this_video = False
            for ext in image_extensions:
                potential_image = image_name + ext
                if potential_image in files_to_process:
                    logging.info(f"Skipping video file {os.path.basename(file_path)} - will be processed via its image counterpart")
                    skip_this_video = True
                    break
            if skip_this_video:
                continue

        logging.info(f"Extracting metadata for {os.path.basename(file_path)}")
        metadata = extract_metadata(file_path)
        if metadata:
            if len(metadata) == 5:
                # Check for duplicate filenames
                filename = metadata[0]
                if filename not in processed_filenames:
                    metadata_for_csv.append(metadata)
                    processed_filenames.add(filename)
                else:
                    logging.warning(f"Duplicate filename {filename} found, skipping duplicate.")
            else:
                logging.warning(f"Incorrect metadata tuple format returned for {os.path.basename(file_path)}: {metadata}. Skipping.")
        else:
            logging.warning(f"Failed to extract metadata for {os.path.basename(file_path)}. Skipping.")

    if not metadata_for_csv:
        logging.error("No metadata could be extracted for iStock CSV generation.")
        return False, None

    # --- Generate CSV ---
    csv_dir = os.path.expanduser("~/Desktop/CSV/iStock")
    os.makedirs(csv_dir, exist_ok=True)
    
    # Generate CSV filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"istock_metadata.csv"
    csv_final_path = os.path.join(csv_dir, csv_filename)
    temp_csv_path = ""

    try:
        # Use a temporary file for safety during generation
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".csv", encoding='utf-8', newline='') as temp_csv_file:
            temp_csv_path = temp_csv_file.name
            
        logging.info(f"Generating iStock metadata CSV at temporary path: {temp_csv_path} for {len(metadata_for_csv)} files.")
        if not generate_istock_csv(metadata_for_csv, temp_csv_path):
            logging.error("Failed to generate iStock metadata CSV.")
            if os.path.exists(temp_csv_path): 
                os.remove(temp_csv_path)
            return False, None

        # Move the temporary CSV to the final destination
        os.replace(temp_csv_path, csv_final_path)
        logging.info(f"iStock CSV successfully saved locally to: {csv_final_path}")
        return True, csv_final_path

    except Exception as e:
        logging.error(f"Error during iStock CSV file creation/saving: {e}", exc_info=True)
        if temp_csv_path and os.path.exists(temp_csv_path):
             try:
                 os.remove(temp_csv_path)
             except OSError as rm_err:
                 logging.error(f"Error removing temporary CSV file {temp_csv_path}: {rm_err}")
        return False, None

if __name__ == "__main__":
    main()
