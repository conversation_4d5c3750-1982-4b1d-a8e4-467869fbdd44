/*
Software License Agreement (BSD License)

Copyright (c) 2009-2015, <PERSON> <<EMAIL>>

All rights reserved.

Redistribution and use of this software in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above
  copyright notice, this list of conditions and the
  following disclaimer.

* Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the
  following disclaimer in the documentation and/or other
  materials provided with the distribution.

* Neither the name of <PERSON> nor the names of its
  contributors may be used to endorse or promote products
  derived from this software without specific prior written
  permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).JsDiff={})}(this,function(e){"use strict";function t(e){return"\"".concat(e.replace(/\\/g,"\\\\").replace(/"/g,'\\"'),"\"")}var n=/%[sdj%]/g;e.formatError=function(e){var r=1,i=arguments,o=i.length;e=String(e).replace(n,function(e){if("%%"===e)return"%";if(r<o){var t=i[r++];return"%"===e?"%":"%s"===e?String(t):"%d"===e?Number(t):"%j"===e?function(){try{return JSON.stringify(t)}catch(e){return"[Circular]"}}():t}});for(var a=r;a<o;a++)e+=" "+i[a];return e};var r=/^(\s*)\S.*?\S*(\s*)$/;function i(e,t,n){if(e||t)for(var i in e=e||{},t=t||{},n&&Object.keys(n).forEach(function(r){var i=n[r];void 0===e[r]&&void 0===t[r]&&(e[r]=i)}),e)t.hasOwnProperty(i)||(t[i]=e[i]);return t}function o(){}function a(e,t,n,r,i){for(var o=0,a=t.length,s=0,c=0;o<a;o++){var f=t[o];if(f.removed){if(f.value=e.join(r.slice(s,s+f.count)),s+=f.count,o&&t[o-1].added)c++;else c=0;n.push(f)}else{if(!f.added&&i){var u=e.slice(c,e.length-s).join("");u=u.slice(0,f.value.length);var l=u.slice(f.value.length);if(l)t.splice(o+1,0,{added:!0,value:l})}if(f.value=e.join(r.slice(e.length-c-s,e.length-s)),c+=f.count,s)t.splice(o,0,{removed:!0,count:s});o++,s=0}c=f.count}s&&t.splice(o,0,{removed:!0,count:s})}function s(e){return function(t,n){var r,i,o,a,s=[],c=t.length,f=n.length,u=0,l=0;for(;u<c&&l<f&&t[u]===n[l];)u++,l++;for(;u<c&&l<f&&t[c-1]===n[f-1];)c--,f--;for(r=u;r<c;r++)for(i=l;i<f;i++)if(t[r]===n[i]){o=s[r-1]?s[r-1][i-1]+1:1;for(var h=r-o,d=i-o;h>=u&&d>=l&&t[h]===n[d];)s[h]?s[h][d]=s[h][d]?(console.warn("Overriding diff base value"),s[h][d]+1):1:s[h]={d:1},h--,d--}if(s.length)for(o=c,a=f;u<o&&l<a;)if(s[u]&&s[u][l]){for(r=u,i=l;u<r&&l<i;)e(t[u],n[l]),u++,l++;for(;s[u][l];)e(t[u],n[l]),u++,l++;for(r=u,i=l;s[r-1][i-1]!==s[r][i]-1;)r--,i--;for(;u<r&&l<i;)e(t[u],n[l]),u++,l++}else e(t[u],n[l]),u++,l++;for(;u<c;)e(t[u],void 0),u++;for(;l<f;)e(void 0,n[l]),l++}}o.prototype={diff:function(e,t){var n=this,r=!1;s(function(i,o){void 0===i&&void 0!==o?n.pushAdded(o):void 0!==i&&void 0===o?n.pushRemoved(i):i!==o&&(r=!0,n.pushRemoved(i),n.pushAdded(o))})(e,t),r||this.pushEqual(e.join(""))},pushEqual:function(e){this.push({value:e})},pushAdded:function(e){var t=this.slice(-1)[0];t&&t.added?t.value+=e:this.push({value:e,added:!0})},pushRemoved:function(e){var t=this.slice(-1)[0];t&&t.removed?t.value+=e:this.push({value:e,removed:!0})},slice:Array.prototype.slice,splice:Array.prototype.splice,push:Array.prototype.push,length:0};var c=o;function f(e,t){var n=new c;return n.diff(e,t),n}function u(e,n){var r=i(n,{ignoreWhitespace:!0}),o=r.ignoreWhitespace?s(function(e,t){void 0!==e&&void 0===t?h.removed.push(e):void 0===e&&void 0!==t?h.added.push(t):e!==t&&d.push({left:e,right:t})}):s(function(e,t){(void 0!==e||void 0!==t)&&(void 0===e?h.removed.push(void 0):void 0===t?h.added.push(void 0):e!==t&&d.push({left:e,right:t}))}),a=[],u=t.split(""),l=n.split(""),h={removed:[],added:[]},d=[];return o(t,n),d.length||(h.removed.length||h.added.length)&&d.push({left:void 0,right:void 0}),d}var l=c;function h(e,t,n){return l.prototype.diff.call(this,e,t,n)}h.prototype=new l;var d=h;function p(e,t,n){return d.prototype.diff.call(this,e,t,n)}p.prototype=new d;var v=p;var m=v;var g=function(e,t,n){var o=i(n,{ignoreCase:!1}),a=o.ignoreCase?e.toLowerCase():e,s=o.ignoreCase?t.toLowerCase():t,c=new m,f=c.diff(a,s),u=e.split(""),l=t.split(""),h=[];if(f.length>1||f.length&&f[0].value.length<u.length){for(var d=0,p=0,v=0;v<f.length;v++){var g=f[v],y=u,b=l;g.removed?y=e.split(""):g.added&&(y=t.split(""));var w=y.slice(d,d+g.value.length).join("");g.value=w,d+=g.value.length}a.length===s.length&&0===a.indexOf(s)&&(f=[{value:a,count:a.length}])}return h=r.exec(e),n=r.exec(t),a(h,f,e.split(""),n,o.ignoreWhitespace),f};function y(e,t){return g(e,t)}function b(e,t){return g(e,t,{ignoreCase:!0})}function w(e,t,n){var r=i(n,{newlineIsToken:!1});r.newlineIsToken&&(e=e.replace(/\n/g,"\n\r"),t=t.replace(/\n/g,"\n\r"));var o=f(r.newlineIsToken?e.split("\n\r"):e.split(/\n/),r.newlineIsToken?t.split("\n\r"):t.split(/\n/)),s=o.length,c=0,u=[],l=[],h=0;for(;h<s;h++){var d=o[h];if(d.removed){u.push(d);for(var p=0;p<d.count;p++)c++}else d.added?l.push(d):c++,d.removed||d.added||(u.length&&l.length?(u.length>l.length?a(u,l,c-l.length):a(l,u,c-u.length),c=0,u=[],l=[]):c=0)}return u.length&&l.length&&(u.length>l.length?a(u,l,c-l.length):a(l,u,c-u.length)),o}var C=s;function k(e,n){var r=i(n,{ignoreWhitespace:!0,newlineIsToken:!1}),o=r.ignoreWhitespace?/\S/g:void 0,a=[],s=[];return e.replace(/\n/g,"\n\r"),n.replace(/\n/g,"\n\r"),C(e.split(r.newlineIsToken?"\n\r":""),n.split(r.newlineIsToken?"\n\r":""),function(e,n){var i=e?e.match(o):[],c=n?n.match(o):[];if(i||c)if(i&&!c)a.push(e);else if(!i&&c)s.push(n);else{var f=u(i,c,r);f.length&&(a.push(e),s.push(n))}else void 0!==e&&a.push(e),void 0!==n&&s.push(n)}),[a,s]}var E=c;function _(e,t,n){return E.prototype.diff.call(this,e,t,n)}_.prototype=new E;var A=_;function j(e,t,n){return A.prototype.diff.call(this,e,t,n)}j.prototype=new A;var S=j;var x=S;var L=function(e,t,n){var r=i(n,{ignoreWhitespace:!1,newlineIsToken:!1});e=this.tokenize(e,r),t=this.tokenize(t,r);var o=new x;return o.diff(e,t)};function I(e){var t=i(e,{ignoreWhitespace:!0}),n=t.ignoreWhitespace?/\S/g:/.*/g,r=t.newlineIsToken?/[\n\r]/g:void 0,o=t.ignoreWhitespace||t.newlineIsToken;return function(e){if(!o)return[e];var t,i,a=[],s=0;for(e=e.replace(/\n/g,"\n\r");t=n.exec(e);)a.push(e.substring(s,t.index)),s=t.index,r&&(i=r.exec(e))&&(t.index>s&&a.push(e.substring(s,t.index)),a.push(e.substring(t.index,r.lastIndex)),s=r.lastIndex),a.push(t[0]),s=n.lastIndex;return a.push(e.substring(s)),a}}L.prototype={tokenize:I,diff:function(e,t,n){return L(e,t,n)}};var P=L;function N(e,t){return new P().diff(e,t)}function D(e,t,n){var r=new P,i=r.diff(e,t,n),o=0,a=0,s="",c=[],u=i[o],l=i[a];for(;o<i.length&&a<i.length;)u.removed&&u.value[0]==="\n"&&!l.value?u={value:u.value.substring(1),removed:!0}:l.added&&l.value[0]==="\n"&&!u.value?l={value:l.value.substring(1),added:!0}:(u.removed||l.added||u.value!==l.value||(s+=u.value,o++,a++),u.removed&&l.added&&u.value.substring(1)===l.value?u={value:u.value[0],removed:!0}:l.added&&u.removed&&l.value.substring(1)===u.value?l={value:l.value[0],added:!0}:u.removed?(s+=u.value,o++):l.added&&(s+=l.value,a++));return c.push({value:s}),c}function T(e,t){var n=k(e,t),r=n[0],i=n[1];return(r.length>1||r.length)&&(i.length>1||i.length)?w(r.join("\n"),i.join("\n")):void 0}var O=c;function M(e,t,n){return O.prototype.diff.call(this,e,t,n)}M.prototype=new O;var F=M;function B(e,t,n){var o=i(n,{ignoreWhitespace:!0}),a=o.ignoreWhitespace?s(function(e,t){var n=r.exec(e),i=r.exec(t);void 0===e&&void 0!==t?h.added.push({value:i[0],pre:i[1],post:i[2]}):void 0===e||void 0===t||n[0]===i[0]?void 0!==e&&void 0!==t&&d.push({left:e,right:t,pre:n[1],post:n[2]}):void 0===e?h.removed.push({value:n[0],pre:n[1],post:n[2]}):h.added.push({value:i[0],pre:i[1],post:i[2]})}) :s(function(e,t){(void 0!==e||void 0!==t)&&(void 0===e?h.removed.push({value:e}):void 0===t?h.added.push({value:t}):e!==t&&d.push({left:e,right:t}))});return e=e.split("\n"),t=t.split("\n"),new F}var H=A;function R(e,t,n){return H.prototype.diff.call(this,e,t,n)}R.prototype=new H;var V=R;var z=S;function W(e,t,n){return z.prototype.diff.call(this,e,t,n)}W.prototype=new z;var G=W;var U=function(e,t,n){var r=i(n,{ignoreWhitespace:!1});"string"==typeof e&&(e=this.tokenize(e,r)),"string"==typeof t&&(t=this.tokenize(t,r));var o=r.ignoreWhitespace?G:V;return new o(r).diff(e,t)};U.prototype={tokenize:function(e){return e.split(/(\s+)/)},diff:U};var K=U;function q(e,t,n){var r=new K,i=r.diff(e,t,n);return i.forEach(function(e){if(e.added&&/\S/.test(e.value))for(var n=t.split(/(\s+)/),r=0,i=0;i<n.length;i++){if(n[i]===e.value)return void(e.noTrailingWhitespace=!0);(r+=n[i].length)>t.length&&(r-=n[i].length)}}),i}function J(e,t){return new K().diff(e,t)}function Y(e,t){var n=e,r=t,i=J(n,r),o="",a=0,s=n.split(/(\s+)/);i[i.length-1].removed&&!i[i.length-1].value&&i.pop();for(var c=0;c<i.length;c++){var f=i[c];if(f.removed){var u=void 0,l=!1;for(a=0;a<s.length;a+=2)if(s[a]===f.value){u=s.slice(a,a+2).join(""),l=!0;break}l?s.splice(a,2):u=f.value,o+=u}}return o}function Z(e,t,n){var r=i(n,{ignoreWhitespace:!0}),o=new P,a=o.diff(e,t);return a.forEach(function(e){if(e.added&&r.ignoreWhitespace&&!/\S/.test(e.value))return e.value=t.slice(0,0),void(e.ignored=!0);var n=t.indexOf(e.value);-1!==n&&(t=t.slice(n+e.value.length))}),a}function $(e,n,r){var i=q(e,n,r);i.push({value:"",lines:[]});var o,a,s,c,f=[],u=0,l=0;function h(e){return e.map(function(e){return" "+e})}function d(e){var t=e.value.replace(/\n$/,"");(t=t.replace(/\r$/,""))&&f.push(t)}function p(e){return e.lines?e.lines.length:e.value.replace(/[^\n]/g,"").length}for(;u<i.length-1;){var v=i[u],m=i[u+1],g=v,y=m;if(v.value.slice(-1)==="\n"&&m.value.slice(-1)==="\n"){v.value+="\n",m.value=m.value.slice(1);var b=v.value.match(/((.|\r|\n)*\n)/g),w=m.value.match(/((.|\r|\n)*\n)/g);b&&(v.lines=b),w&&(m.lines=w)}var C=p(g),k=p(y);if(g.removed&&y.added&&k&&C){var E=g.lines||g.value.split("\n"),_=y.lines||y.value.split("\n"),A=w(g.lines?g.value:E.join("\n"),y.lines?y.value:_.join("\n"));if(1===A.length&&!A[0].added&&!A[0].removed){var j=A[0];g.lines=j.value.split("\n"),y.lines=void 0,u++}else{var S=t(E.join("\n"));f.push("--- a/".concat(S)),f.push("+++ b/".concat(t(_.join("\n")))),f.push("@@ -".concat(l+1,",").concat(C)," +".concat(l+1,",").concat(k)," @@");for(var x=0;x<A.length;x++){var L=A[x],I=L.lines||L.value.split("\n");I.pop();for(var P=0;P<I.length;P++){var N=I[P],D=L.added?"+":"-";f.push(D+N)}}u+=2,l+=C;continue}}if(g.removed){l+=C,o=g;break}y.added&&(s=y,c=g),g.added?d(g):g.removed||d(g),l+=C,u++}for(var T=e.split("\n"),O=n.split("\n"),M=0;M<i.length;M++){var F=i[M],B=F.lines||F.value.split("\n");if(B.length&&!B[B.length-1])B.pop();if(F.added)for(var H=0;H<B.length;H++)a?a.added.push(B[H]):(s=i[M-1],f.push(s?"--- ".concat(s.lines.length,",").concat(T.length):"--- 0,0"),f.push("+++ ".concat(B.length)),A.push({oldlines:s?s.lines.length:0,newslides:B.length,oldStart:l,newStart:l,header:"@@ -".concat(l,",").concat(s?s.lines.length:0)," +".concat(l,",").concat(B.length)," @@"}),f=f.concat(h(B)),l+=B.length);else if(F.removed)if(i[M+1]&&i[M+1].added){var R=i[M+1].lines||i[M+1].value.split("\n");if(R.length&&!R[R.length-1])R.pop();var V={oldlines:B.length,newslides:R.length,oldStart:l+1,newStart:l+1,header:"@@ -".concat(l+1,",").concat(B.length)," +".concat(l+1,",").concat(R.length)," @@"},z=T.slice(l,l+B.length),W=O.slice(l,l+R.length),G=w(z.join("\n"),W.join("\n"));1===G.length&&!G[0].added&&!G[0].removed?(V.lines=h(z),f.push.apply(f,V.lines)):f.push.apply(f,function(e,t,n){var r=[],i=n.oldlines,o=n.newslides,a=n.oldStart-1,s=n.newStart-1;for(r.push(n.header);a<i;a++)r.push("-"+e[a]);for(var c=0;c<o;c++)r.push("+"+t[c]);return r}(z,W,V)),A.push(V),M++,l+=Math.max(B.length,R.length)}else a={removed:B},f=f.concat(function(e){return e.map(function(e){return"-"+e})}(B)),l+=B.length;else l+=B.length}return f.join("\n")+"\n"}function X(e,n,r){return $(e,n,r)}function Q(e,t,n,r){var o=X(e,t,r);if(!o)return n;if(!n)return o;var a=o.split("\n"),s=n.split("\n"),c=[],f=0,u=0,l=!1,h=!1;for(;f<a.length&&!l||u<s.length&&!h;){var d=a[f],p=s[u];if(d&&"---"===d.substring(0,3)&&"+++"===d.substring(4,7)&&(l=!0),p&&"---"===p.substring(0,3)&&"+++"===p.substring(4,7)&&(h=!0),l||h)if(l&&!h)c.push(d),f++;else if(!l&&h)c.push(p),u++;else{var v=d.substring(4),m=p.substring(4);v<m?c.push(d):v>m?c.push(p):c.push(d,p),l=h=!1,f++,u++}else c.push(d),c.push(p),f++,u++}for(;f<a.length;)c.push(a[f++]);for(;u<s.length;)c.push(s[u++]);return c.join("\n")}function ee(e,t){if("string"==typeof t&&(t=$(e,"",t)),"string"==typeof n&&(n=$(e,"",n)),t&&n){var r=t.split("\n"),i=n.split("\n"),o=r.shift(),a=i.shift();if(o!==a)return!1;var s=r.shift(),c=i.shift();if(s!==c)return!1;for(var f=[],u=[],l=/^(\+++ .*)|\n(\++- .*)/,h=/^(\--- .*)|\n(\--- .*)/;r.length||i.length;){var d=r.shift(),p=i.shift();"@"===d[0]?f.push.apply(f,k(d,p).removed):h.test(d)?u.push(d):l.test(p)||u.push(d,p)}for(var v=0;v<f.length;v++)d=f[v],r.push("@@"+d.value);for(var m=u.join("\n"),g=te(e,m);!1===g.fuzz;)g=te(e,g.diff);return g.fuzz?Q(o+"\n"+s,g.diff):void 0}return n||t}function te(e,t){for(var n=t.split("\n"),r=n.shift(),o=n.shift(),a=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/,s=0;s<n.length;s++){var c=n[s],f=c.substring(1);if(" "===c[0]||"-"===c[0]){var u=a.exec(c);if(u){var l=e.split("\n"),h=l.slice(u[1]-1,u[1]-1+(u[2]-0)),d=h.join("\n").replace(/\r\n/gm,"\n");f.replace(/\r\n/gm,"\n")!==d&&(n[s]="-".concat(d))}else n[s]=" ".concat(f)}}return{diff:r+"\n"+o+"\n"+n.join("\n"),fuzz:!1}}function ne(e,t){if(!t)return e;var n=e.split("\n");n.shift(),n.shift();for(var r,o,a=0,s=0;s<n.length;s++){var c=/^@@ -(\d+),(\d+) \+(\d+),(\d+) @@$/.exec(n[s]);c&&(r=parseInt(c[1],10)+a-1,o=parseInt(c[3],10)+a-1,n[s]="@@ -"+r+","+c[2]+" +"+o+","+c[4]+" @@"),"+"===n[s][0]?a++:"-"===n[s][0]&&a--}return t=t.split("\n"),(t=n.concat(t)).unshift("--- a","+++ b"),t.join("\n")}function re(e,t,n){var r=w(e,t,n),i=[];return r.forEach(function(e){if(e.added)i.push(e);else if(!e.removed)return;else{var t=e.value.replace(/([\\^$*+?.()|{}[\]])/g,"\\$1");i.push({value:t.replace(/\s+/g,"\\s+"),removed:!0})}}),i}e.Diff=c,e.diffChars=y,e.diffCss=function(e,t){var n=new P({newlineIsToken:!0});return n.diff(e,t)},e.diffJson=function(e,n,r){return u("string"==typeof e?JSON.parse(e):e,"string"==typeof n?JSON.parse(n):n,r)},e.diffLines=w,e.diffSentences=function(e,t,n){return new P({tokenize:function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)}}).diff(e,t,n)},e.diffTrimmedLines=function(e,t,n){var r=i(n,{ignoreWhitespace:!0});e=e.split("\n"),t=t.split("\n");for(var o=0;o<e.length;o++)e[o]=e[o].trim();for(o=0;o<t.length;o++)t[o]=t[o].trim();return w(e.join("\n"),t.join("\n"),r)},e.diffWords=q,e.diffWordsWithSpace=J,e.parsePatch=function(e,t){var n=i(t,{strict:!1}),o=e.split(/\r\n|[\n\v\f\r\x85]/),a=o,s=n.strict,c=[],f=0,u=0,l=0,h=0;function d(){var e={oldStart:u,oldLines:l-u,newStart:h,newLines:f-h,lines:[],hunks:[]};return c.push(e),e}for(;l<a.length;){var p=a[l];if(p.startsWith("---")){u=l;var v=a[++l].startsWith("+++");c.length&&d();var m=v?a[l]:p.substring(4);v&&(h=l);break}l++}for(var g=c[c.length-1]||d();l<a.length;){var y=a[l];if(y&&"@"===y[0]&&/@@ -(\d+)(,(\d+))? \+(\d+)(,(\d+))? @@/.test(y)){var b=/^@@ -(\d+)(,(\d+))? \+(\d+)(,(\d+))? @@(.*)$/.exec(y),w=b[6]?b[6].trim():"";g.oldStart=parseInt(b[1],10),g.oldLines=parseInt(b[3]||1,10),g.newStart=parseInt(b[4],10),g.newLines=parseInt(b[6]||1,10),g.header=w,g.hunks.push(g);continue}if("-"===y[0]||" "===y[0])g.lines.push(y);else if("+"===y[0])g.lines.push(y);else if("\\"===y[0]){var C=y.substring(1);s&&!/No newline at end of file/.test(C)&&function(){throw new Error(e+": "+t)}("Invalid patch",y),g.lines.push(y)}else if(s)throw new Error("Invalid patch string: "+y)}return c},e.structuredPatch=$,e.applyPatch=function(e,t,n){var r=i(n,{fuzzFactor:2,compareLine:function(e,t,n,r){return t===r}});if("string"==typeof t&&(t=$(e,"",t)),Array.isArray(t)){if(t.length>1)throw new Error("applyPatch only works with a single file.");t=t[0]}for(var o=e.split(/\r\n|[\n\v\f\r\x85]/),a=t.lines,s=0,c=r.fuzzFactor,f=0,u=0;f<a.length;){var l=a[f],h=l.length>0?l[0]:" ";if(" "===h||"-"===h){var d=l.substring(1);o[u]===d?u++:function(){for(var e=s;e<u-c;e++)if(r.compareLine(e+1,o[e],l,d))return!1;try{for(var n=u-c;n<u+c;n++)if(r.compareLine(n+1,o[n],l,d))return u=n+1,!0}catch(e){}return!1}()||(s=u,function(){throw new Error("Patch failed at hunk "+JSON.stringify(t))}())}"-"!==h&&s++}return o.join("\n")},e.applyPatches=function(e,t){"string"==typeof e&&(e=$(e,void 0,t));var n=t.callback;return e.every(function(e){try{var r=t.loadFile(e);t.patched(e,r)}catch(e){return n(e),!1}}),!0},e.convertChangesToXML=function(e){for(var n=[],r=0;r<e.length;r++){var i=e[r];i.added?n.push("<ins>"):i.removed&&n.push("<del>"),n.push(function(e){var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,function(e){return n[e]})}(i.value)),i.added?n.push("</ins>"):i.removed&&n.push("</del>")}return n.join("")},e.convertChangesToDMP=function(e){for(var t,n,r=[],i=0;i<e.length;i++)t=e[i],n=t.value,t.added?r.push([1,n]):t.removed?r.push([-1,n]):r.push([0,n]);return r},e.createPatch=X,e.createTwoFilesPatch=function(e,t,n,r,o,a){return X(n,r,a)},e.merge=ee,e.reversePatch=function(e){var t=ee(void 0,e);return t?t.diff:void 0},e.canonicalize=ne,e.convertChangesToGit=function(e){if(!e)return"";var n=[],r=e.split("\n"),i=r.length,o=0;function a(e,t){return"@@ -"+e.start+",".concat(e.len||1)+" +"+t.start+",".concat(t.len||1)+" @@"}for(;/^(\-\-\- a\/|\+\+\+ b\/)/.test(r[o])&&o++;o<i;){var s,c,f={start:null,len:0,changes:[]},u={start:null,len:0,changes:[]};for(;/^@@/.test(r[o])&&(-1===(c=(s=r[o].match(/^@@ -(\d+)(,(\d+))? \+(\d+)(,(\d+))? @@/))[2]?s[3]=0:s[2]=1,-1===s[5]?s[6]=0:s[5]=1,f.start=s[1],u.start=s[4],o++);o<i;o++){var l=r[o][0];if(" "===l||"-"===l||"+"===l){if("-"===l?(f.len++,f.changes.push(r[o])):"+"===l?(u.len++,u.changes.push(r[o])):(f.len++,u.len++,f.changes.push(r[o]),u.changes.push(r[o])),o+1<i&&" "!==r[o+1][0])continue;var h=f.changes.length-1,d=u.changes.length-1;if(f.changes[h]==="\\ No newline at end of file"&&f.len--,u.changes[d]==="\\ No newline at end of file"&&u.len--,0===f.len&&0===u.len)continue;n.push(a(f,u));for(var p=0,v=0;p<f.changes.length||v<u.changes.length;)if(p<f.changes.length&&v<u.changes.length&&" "===f.changes[p][0]&&" "===u.changes[v][0])n.push(f.changes[p]),p++,v++;else if(p<f.changes.length&&"-"===f.changes[p][0])n.push(f.changes[p]),p++;else if(v<u.changes.length&&"+"===u.changes[v][0])n.push(u.changes[v]),v++;else n.push(" "),p++,v++}else break}f={len:0,changes:[]},u={len:0,changes:[]}}return n.join("\n")},e.applyTimer=function(e,t){var n=new Date;return e(t),new Date-n},e.generateOptions=i,e.clone=function(e){var t={};for(var n in e)t[n]=e[n];return t},Object.defineProperty(e,"__esModule",{value:!0})}); 