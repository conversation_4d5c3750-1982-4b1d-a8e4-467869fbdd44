// Wait for the DOM and pywebview API to be ready
window.addEventListener('pywebviewready', () => {
    console.log("Pywebview ready. JS managing visual DND cues, Python DOM handles drop logic.");

    const dropArea = document.getElementById('drop-area');
    const progressLabel = document.getElementById('progress-label');
    const uploadBtn = document.getElementById('upload-btn');
    const reviewBtn = document.getElementById('review-btn');

    // --- Drag and Drop Handling ---

    // Prevent default behaviors - STILL CRITICAL for dragover and drop
    // This allows the Python DOM event handler to receive the drop.
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.body.addEventListener(eventName, preventDefaults, false);
        if (dropArea) {
            dropArea.addEventListener(eventName, preventDefaults, false);
        }
    });

    function preventDefaults(e) {
        // console.log(`JS: Preventing default for: ${e.type}`); // Uncomment for verbose debugging
        e.preventDefault();
        e.stopPropagation();
    }

    // --- Visual Feedback for Drop Area (Managed by JS) ---
    if (dropArea) {
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        ['dragleave', 'drop'].forEach(eventName => {
            // Make sure unhighlight happens on drop too
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            // Check if files are being dragged using standard property
             if (e.dataTransfer && e.dataTransfer.types && ( e.dataTransfer.types.includes('Files') || e.dataTransfer.types.includes('application/x-moz-file') )) {
                dropArea.classList.add('drag-over');
            }
        }

        function unhighlight(e) {
            dropArea.classList.remove('drag-over');
        }

        // --- Handle the Drop (JS ONLY prevents default & unhighlights) ---
        // The actual file processing is done by python_on_drop bound via DOMEventHandler
        // NO need for a specific JS drop listener on dropArea beyond the preventDefaults above.
        // If we add one, ensure it doesn't interfere. Let's rely on the preventDefaults added in the loop.

        /* Commenting out the previous JS drop handler as it's replaced by Python DOM handler
        dropArea.addEventListener('drop', (e) => {
            console.log("JS: Drop event detected on drop-area. Only preventing default.");
            // preventDefaults(e); // Handled by the loop listener
            // unhighlight(e);   // Handled by the loop listener
            // DO NOT attempt to process files here.
        }, false);
        */

    } else {
         console.error("Drop area element not found!");
    }


    // --- Button Click Handlers (Keep as before) ---
    uploadBtn.addEventListener('click', () => {
        console.log("JS: Upload button clicked.");
        const checkboxes = document.querySelectorAll('.checkbox-container input[type="checkbox"]:checked');
        const selectedHosts = Array.from(checkboxes).map(cb => cb.value);
        console.log("JS: Selected hosts:", selectedHosts);
        try {
             window.pywebview.api.request_upload(selectedHosts);
        } catch (error) {
             console.error("JS Error calling python request_upload:", error);
             updateProgressLabel(`JS Error starting upload: ${error}`, 'red');
        }
    });

    reviewBtn.addEventListener('click', () => {
        console.log("JS: Review button clicked.");
        try {
             window.pywebview.api.review_files();
        } catch (error) {
             console.error("JS Error calling python review_files:", error);
             updateProgressLabel(`JS Error starting review: ${error}`, 'red');
        }
    });

    // --- Utility Functions (Keep as before) ---
    window.updateProgressLabel = (message, color = 'black') => {
        // --- ADD LOGGING ---
        console.log(`JS DEBUG: updateProgressLabel called. Message: "${message}", Color: ${color}`);
        const progressLabel = document.getElementById('progress-label');
        if (progressLabel) {
            console.log("JS DEBUG: Found #progress-label element:", progressLabel); // Log the element itself
            progressLabel.textContent = message;
            progressLabel.style.color = color;
            // Ensure the initial warning class is removed if it exists
            progressLabel.classList.remove('initial-warning');
            console.log("JS DEBUG: #progress-label updated.");
        } else {
             // --- Make error more prominent ---
             console.error("JS ERROR: !!! Progress label element #progress-label NOT FOUND !!!");
        }
    };

    // Function to show the completion indicator
    window.showCompletionIndicator = () => {
        console.log("JS: Showing completion indicator");
        const overlay = document.getElementById('completion-overlay');
        if (overlay) {
            // Play completion sound
            playCompletionSound();

            overlay.classList.add('visible');
            // Automatically hide after 1.5 seconds
            setTimeout(() => {
                hideCompletionIndicator();
            }, 1500);
        } else {
            console.error("Completion overlay element not found!");
        }
    };

    // Function to play the completion sound
    window.playCompletionSound = () => {
        try {
            // Create an audio element
            const audio = new Audio('sounds/notification-complete.mp3');
            // Set volume (0.0 to 1.0)
            audio.volume = 0.5;
            // Play the sound
            audio.play().catch(e => {
                // Handle any errors (e.g., file not found)
                console.warn("Could not play completion sound:", e);
            });
        } catch (e) {
            console.warn("Error playing completion sound:", e);
        }
    };

    // Function to hide the completion indicator
    window.hideCompletionIndicator = () => {
        console.log("JS: Hiding completion indicator");
        const overlay = document.getElementById('completion-overlay');
        if (overlay) {
            overlay.classList.remove('visible');
        }
    };

    // --- Initial Setup ---
    console.log("Frontend script loaded. JS handles visual cues. Python DOM handles drop.");

}); // End pywebviewready listener