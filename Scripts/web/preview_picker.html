<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Picker</title>
    <!-- Reuse gallery styles where applicable -->
    <link rel="stylesheet" href="key.css">
    <link rel="stylesheet" href="preview_picker.css"> <!-- Specific styles -->
</head>
<body>
    <div class="main-container">
        <h1 class="title">Preview Picker</h1>

        <div class="preview-area">
            <img id="frame-preview" src="" alt="Video Frame Preview">
            <p id="loading-indicator" style="display: none;">Loading frame...</p>
        </div>

        <div class="timeline-area">
            <input type="range" id="timeline-slider" min="0" max="100" value="0" step="1" disabled>
            <div class="time-display">
                Frame: <span id="current-frame">0</span> / <span id="total-frames">?</span>
            </div>
        </div>

        <div class="controls-area">
             <p id="info-text" class="info-text">Image: <span id="image-filename">?</span> | Video: <span id="video-filename">?</span></p>
            <button id="save-preview-btn" class="btn" disabled>Save Preview</button>
        </div>

         <!-- Hidden inputs to store paths if needed, though usually handled in Python backend -->
         <!-- <input type="hidden" id="image-path-input"> -->
         <!-- <input type="hidden" id="video-path-input"> -->
    </div>

     <div id="status-bar" class="status-bar">Initializing...</div>

    <script src="preview_picker.js"></script>
</body>
</html>