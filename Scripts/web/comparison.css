/* comparison.css */

/* Basic Reset & Body */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
    color: #333;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Title */
.title {
    font-size: 32px;
    font-weight: 500;
    color: #222;
    text-align: center;
    margin-bottom: 10px;
}

/* Progress Info */
.progress-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    gap: 5px;
}

#file-counter {
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

#filename {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Image Preview */
.image-preview-container {
    width: 1000%;
    max-width: 920px;
    max-height: 150px;
    height: 200px;
    background-color: #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.5s ease;
}

/* Comparison Container */
.comparison-container {
    display: flex;
    width: 100%;
    max-width: 1000px;
    gap: 20px;
    align-items: stretch;
}

/* Metadata Boxes */
.metadata-box {
    flex: 1;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 500px;
}

.original-box {
    border-top: 4px solid #FF6600; /* Blue for original */
}

.refined-box {
    border-top: 4px solid #FF6600; /* Orange for refined */
}

/* Metadata Header */
.metadata-header {
    padding: 20px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.metadata-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Metadata Content */
.metadata-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metadata-item {
    width: 100%;
}

.metadata-item label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    display: block;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-text {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    min-height: 20px;
    word-wrap: break-word;
}

/* Keywords Container */
.keywords-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding-top: 5px;
}

.keyword-box {
    background-color: #f0f0f0;
    color: #444;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.3s ease;
}

/* Choice Buttons */
.choice-btn {
    margin: 20px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.original-btn {
    background: linear-gradient(145deg, #ff7e29, #ff6600);
    box-shadow: 0 3px 10px rgba(255, 102, 0, 0.3);
}

.original-btn:hover {
    background: linear-gradient(145deg, #ff8840, #e65c00);
    box-shadow: 0 5px 14px rgba(255, 102, 0, 0.4);
    transform: translateY(-2px);
}

.refined-btn {
    background: linear-gradient(145deg, #ff7e29, #ff6600);
    box-shadow: 0 3px 10px rgba(255, 102, 0, 0.3);
}

.refined-btn:hover {
    background: linear-gradient(145deg, #ff8840, #e65c00);
    box-shadow: 0 5px 14px rgba(255, 102, 0, 0.4);
    transform: translateY(-2px);
}

.choice-btn:active {
    transform: translateY(0px);
}

.choice-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.choice-btn.chosen {
    transform: scale(0.95);
    opacity: 0.8;
}

/* Divider Line */
.divider-line {
    width: 2px;
    background: linear-gradient(to bottom, transparent, #ddd, transparent);
    margin: 20px 0;
    flex-shrink: 0;
}

/* Completion Overlay */
.completion-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease, visibility 0.4s ease;
}

.completion-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.completion-circle {
    width: 100px;
    height: 100px;
    background: linear-gradient(145deg, #4CAF50, #388E3C);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 30px rgba(76, 175, 80, 0.3);
    transform: scale(0.5);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin-bottom: 20px;
}

.completion-overlay.visible .completion-circle {
    transform: scale(1);
}

.completion-text {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.checkmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    stroke: white;
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    animation: draw-checkmark 0.6s ease-in-out forwards 0.2s;
}

@keyframes draw-checkmark {
    from {
        stroke-dashoffset: 100;
    }
    to {
        stroke-dashoffset: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .comparison-container {
        flex-direction: column;
        gap: 20px;
    }
    
    .divider-line {
        width: 100%;
        height: 2px;
        margin: 0;
        background: linear-gradient(to right, transparent, #ddd, transparent);
    }
    
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 24px;
    }
}
