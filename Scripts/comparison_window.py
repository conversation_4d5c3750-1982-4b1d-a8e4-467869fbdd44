# comparison_window.py

import os
import threading
import json
import sys
import webview
from ai_keyworder import update_exif
from PIL import Image
import base64
from io import BytesIO
import cv2

# --- Global Variables ---
_comparison_api_instance = None
_comparison_window = None
_current_metadata_variants = []
_current_file_index = 0
_user_choices = []

# --- Comparison API Class ---
class ComparisonApi:
    def __init__(self, metadata_variants):
        self.metadata_variants = metadata_variants
        self.current_index = 0
        self.user_choices = []

    def get_current_file_data(self):
        """Get data for the current file being compared"""
        if self.current_index < len(self.metadata_variants):
            current_file = self.metadata_variants[self.current_index]

            # Generate thumbnail
            thumbnail_data = self.generate_thumbnail(current_file['file_path'])

            return {
                'filename': current_file['filename'],
                'file_index': self.current_index + 1,
                'total_files': len(self.metadata_variants),
                'thumbnail': thumbnail_data,
                'initial': current_file['initial'],
                'refined': current_file['refined']
            }
        return None

    def generate_thumbnail(self, file_path):
        """Generate base64 thumbnail for the file"""
        try:
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']:
                # Image file
                img = Image.open(file_path)
                if img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')

                # Resize to thumbnail size
                img.thumbnail((300, 200), Image.LANCZOS)

                # Convert to base64
                buffer = BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_data = buffer.getvalue()
                return f"data:image/jpeg;base64,{base64.b64encode(img_data).decode()}"

            elif file_extension in ['.mp4', '.mov', '.avi', '.wmv']:
                # Video file - extract first frame
                cap = cv2.VideoCapture(file_path)
                ret, frame = cap.read()
                cap.release()

                if ret:
                    # Convert BGR to RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame_rgb)
                    img.thumbnail((300, 200), Image.LANCZOS)

                    buffer = BytesIO()
                    img.save(buffer, format='JPEG', quality=85)
                    img_data = buffer.getvalue()
                    return f"data:image/jpeg;base64,{base64.b64encode(img_data).decode()}"

        except Exception as e:
            print(f"Error generating thumbnail for {os.path.basename(file_path)}: {e}")

        return None

    def choose_metadata(self, choice):
        """Handle user's metadata choice"""
        if self.current_index < len(self.metadata_variants):
            current_file = self.metadata_variants[self.current_index]

            if choice == 'original':
                chosen_metadata = current_file['initial']
                print(f"User chose ORIGINAL metadata for {current_file['filename']}")
            else:  # choice == 'refined'
                chosen_metadata = current_file['refined']
                print(f"User chose REFINED metadata for {current_file['filename']}")

            # Save the chosen metadata to EXIF
            try:
                update_exif(
                    current_file['file_path'],
                    chosen_metadata['title'],
                    chosen_metadata['description'],
                    chosen_metadata['keywords']
                )
                print(f"Successfully saved metadata for {current_file['filename']}")
            except Exception as e:
                print(f"Error saving metadata for {current_file['filename']}: {e}")

            # Store the choice
            self.user_choices.append({
                'file_path': current_file['file_path'],
                'filename': current_file['filename'],
                'choice': choice,
                'metadata': chosen_metadata
            })

            # Move to next file
            self.current_index += 1

            # Check if we're done
            if self.current_index >= len(self.metadata_variants):
                # All files processed - show completion and close
                self.show_completion()
                return {'status': 'completed'}
            else:
                # Return next file data
                return {
                    'status': 'next',
                    'data': self.get_current_file_data()
                }

        return {'status': 'error'}

    def show_completion(self):
        """Show completion animation and close window"""
        try:
            window = self._get_window()
            if window:
                window.evaluate_js("showCompletionAnimation()")
                # Close window after animation
                threading.Timer(3.0, self.close_window).start()
        except Exception as e:
            print(f"Error showing completion: {e}")

    def close_window(self):
        """Close the comparison window"""
        try:
            window = self._get_window()
            if window:
                window.destroy()
        except Exception as e:
            print(f"Error closing window: {e}")

    def _get_window(self):
        """Helper to safely get the window object"""
        global _comparison_window
        if _comparison_window:
            return _comparison_window
        elif webview.windows:
            for window in webview.windows:
                if hasattr(window, 'title') and window.title == 'Metadata Comparison':
                    _comparison_window = window
                    return _comparison_window
        print("Error: Comparison window object not found.")
        return None


def get_asset_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    # For Scripts folder structure
    scripts_path = os.path.join(base_path, 'Scripts')
    if os.path.exists(scripts_path):
        return os.path.join(scripts_path, relative_path)
    else:
        return os.path.join(base_path, relative_path)


def run_comparison_window(metadata_variants):
    """Run the comparison window"""
    global _comparison_api_instance, _comparison_window

    print(f"Starting comparison window for {len(metadata_variants)} files")

    _comparison_api_instance = ComparisonApi(metadata_variants)

    # Define paths for HTML and CSS
    html_file = get_asset_path('web/comparison.html')

    if not os.path.exists(html_file):
        print(f"ERROR: Comparison HTML file not found at {html_file}")
        return

    _comparison_window = webview.create_window(
        'Metadata Comparison',
        url=html_file,
        js_api=_comparison_api_instance,
        width=1000,
        height=850,
        resizable=True,
        confirm_close=False,
        background_color='#f5f5f5'
    )

    print("Starting comparison window...")
    webview.start(debug=False)

if __name__ == "__main__":
    run_comparison_window(_current_metadata_variants)