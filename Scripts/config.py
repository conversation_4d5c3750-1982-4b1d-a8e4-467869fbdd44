import os
import subprocess
import sys

# do not delete: API_KEY = '********************************************************'
API_KEY = 'AIzaSyAcfYN4yQfJIFx2UH8amk2OKpgz__Vza34'
MODEL = "gemini-2.5-flash-preview-04-17"
# Get the path to the config.txt file
config_path = os.path.join(os.path.dirname(__file__), "data", "config.txt")
try:
    with open(config_path, "r", encoding="utf-8") as file:
        PYTHON = str(file.read().strip())  # Strip to remove any whitespace or newlines
    print(f"Loaded Python path from config: {PYTHON}")
    # Verify the Python executable exists
    if not os.path.exists(PYTHON):
        print(f"Warning: Python executable not found at {PYTHON}")
        # Try to find a fallback Python
        if os.path.exists(sys.executable):
            print(f"Using system Python instead: {sys.executable}")
            PYTHON = sys.executable
except Exception as e:
    print(f"Error reading config file: {e}")
    # Fallback to system Python
    PYTHON = sys.executable
    print(f"Using system Python as fallback: {PYTHON}")

def open_app(app, args=[]): # <-- Added 'args=[]' parameter
    """
    Launches another Python script in a separate, detached process,
    passing optional command-line arguments.
    Redirects stdout/stderr of the child process to null.

    Args:
        app (str): The filename of the Python script to launch (e.g., "preview_picker.py").
        args (list, optional): A list of string arguments to pass to the script.
                               Defaults to an empty list.
    """
    print(f"\n--- open_app called with app={app}, args={args} ---")
    try:
        # --- Improved Path Resolution ---
        # Get the absolute path to the Scripts directory
        scripts_dir = os.path.abspath(os.path.dirname(__file__))
        print(f"Scripts directory: {scripts_dir}")
        # Go up one level to the project root if needed
        project_root = os.path.abspath(os.path.join(scripts_dir, '..'))
        print(f"Project root: {project_root}")

        # First try to find the script in the Scripts directory
        script_path = os.path.join(scripts_dir, app)
        print(f"Looking for script at: {script_path} (exists: {os.path.exists(script_path)})")

        # If not found, try other locations
        if not os.path.exists(script_path):
            # Try in the current working directory
            cwd_path = os.path.join(os.getcwd(), app)
            print(f"Looking for script at: {cwd_path} (exists: {os.path.exists(cwd_path)})")
            if os.path.exists(cwd_path):
                script_path = cwd_path
                print(f"Found script '{app}' in current working directory: {script_path}")
            else:
                # Try in the project root
                root_path = os.path.join(project_root, app)
                print(f"Looking for script at: {root_path} (exists: {os.path.exists(root_path)})")
                if os.path.exists(root_path):
                    script_path = root_path
                    print(f"Found script '{app}' in project root: {script_path}")
                else:
                    # Try in the project root's Scripts directory
                    root_scripts_path = os.path.join(project_root, 'Scripts', app)
                    print(f"Looking for script at: {root_scripts_path} (exists: {os.path.exists(root_scripts_path)})")
                    if os.path.exists(root_scripts_path):
                        script_path = root_scripts_path
                        print(f"Found script '{app}' in project root's Scripts directory: {script_path}")
                    else:
                        print(f"Error: Script '{app}' not found in any expected locations.")
                        print(f"Tried: \n- {script_path}\n- {cwd_path}\n- {root_path}\n- {root_scripts_path}")
                        raise FileNotFoundError(f"Script '{app}' not found.")

        # Check if Python executable exists
        print(f"Python executable: {PYTHON} (exists: {os.path.exists(PYTHON)})")
        if not os.path.exists(PYTHON):
            print(f"Warning: Python executable not found at {PYTHON}")
            print(f"Falling back to system Python: {sys.executable}")
            python_exe = sys.executable
        else:
            python_exe = PYTHON

        # Set up environment to ensure Scripts directory is in Python's path
        env = os.environ.copy()
        python_path = env.get("PYTHONPATH", "")
        print(f"Current PYTHONPATH: {python_path}")
        if scripts_dir not in python_path:
            env["PYTHONPATH"] = f"{scripts_dir}{os.pathsep}{project_root}{os.pathsep}{python_path}"
            print(f"Updated PYTHONPATH: {env['PYTHONPATH']}")

        # Construct the command list
        command = [python_exe, script_path] + args

        print(f"Launching command: {command}") # Good for debugging

        try:
            # Use Popen to launch the process
            process = subprocess.Popen(
                command, # <-- Use the modified command list
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP, # Keep existing flags
                close_fds=True,                                    # Keep existing flags (mainly for Unix)
                stdout=subprocess.DEVNULL,                         # Keep suppressing output
                stderr=subprocess.DEVNULL,                         # Keep suppressing output
                env=env                                            # Use the modified environment
            )
            print(f"Launched {app} with PID {process.pid}")
            return True # Indicate success
        except Exception as e:
            print(f"Error launching process: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    except FileNotFoundError as e:
        print(f"Error launching '{app}': Script not found. {e}")
        import traceback
        print(traceback.format_exc())
        return False # Indicate failure
    except Exception as e:
        print(f"An unexpected error occurred launching '{app}' with args {args}: {e}")
        import traceback
        print(traceback.format_exc())
        return False # Indicate failure