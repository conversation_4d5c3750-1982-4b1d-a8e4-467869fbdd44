# ai_keywording.py

import logging
import os
import re
from os import path
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
import cv2
from PIL import Image
import piexif
import time  # Added for video processing delay
from config import API_KEY, MODEL
from utils import check_for_matching_video
from video_compressor import compress_video_for_ai

# Global variables to store processing results and errors
glob_keys = []
glob_titles = []
errs = 0
errs_files = []

# --- PROMPT DEFINITIONS ---

def create_generative_prompt(custom_prompt=None, place="", country="", time_info="", is_editorial=False):
    """
    PROMPT 1: Generates a rich, comprehensive first draft of metadata.
    This prompt is intentionally less strict to encourage creative brainstorming.
    """
    return (
        f"""You are a highly precise and objective AI assistant specializing in creating commercial metadata for stock photography. Your primary goal is to describe what is LITERALLY VISIBLE and what a buyer would physically search for. You must strictly avoid all subjective, emotional, or abstract interpretations. Your output must be data-driven and optimized for search algorithms.

**MASTER EXAMPLE (Your goal is to replicate this structure and objectivity):**
*   **Asset:** A photo of a young man on a scooter in Paris at sunset, with the Eiffel Tower in the background.
*   **Generated Output:**
    *   **Title:** A young man on a scooter near the Eiffel Tower in Paris at sunset
    *   **Description:** A young man wearing a helmet rides a scooter on a cobblestone street in Paris, France. The Eiffel Tower is visible in the background during vibrant sunset. This image is suitable for travel, European lifestyle, and transportation themes.
    *   **Keywords:** 1. paris, 2. eiffel tower, 3. scooter, 4. man, 5. sunset, 6. travel, 7. france, 8. cityscape, 9. lifestyle, 10. urban, 11. street, 12. europe, 13. tourism, 14. young man, 15. riding, 16. evening, 17. landmark, 18. transportation, 19. vacation, 20. vintage, 21. road, 22. cobblestone, 23. rider, 24. male, 25. people, 26. tour, 27. journey, 28. architecture, 29. city, 30. outdoor, 31. leisure, 32. romantic, 33. background, 34. building, 35. historic, 36. french, 37. destination, 38. famous, 39. european, 40. helmet, 41. vehicle, 42. moped, 43. sightseeing

    If content is aerial, mention this in title (eg: "Aerial view of a city"), in description and in keywords (TOP PRIORITY!).
---
**YOUR TASK AND CRITICAL RULES:**

Analyze the asset. If a `custom_prompt` is provided, its information is a FACTUAL OVERRIDE that you MUST incorporate.
{f"**FACTUAL OVERRIDE FROM USER:** {custom_prompt}" if custom_prompt else ""}
{f"*   ADD TO THE END of the description: {place}, {country}, {time_info}." if is_editorial else ""}

**RULE #1: BE CONCRETE AND LITERAL.**
Focus ONLY on tangible nouns, visible actions, and locations.
*   **GOOD (Searchable):** `hiker`, `backpack`, `mountain`, `lake`, `fjord`, `pine tree`.
*   **BAD (Not Searchable):** `solitude`, `freedom`, `wanderlust`, `serenity`, `achievement`, `breathtaking`, `spirit`.

**RULE #2: STRICTLY AVOID FORBIDDEN WORDS.**
Your final output MUST NOT contain ANY of the following words in the Title, Description, or Keywords:
`stunning`, `beautiful`, `breathtaking`, `scenic`, `amazing`, `vibrant`, `tranquility`, `serenity`, `peace`, `contemplation`, `solitude`, `wanderlust`, `challenge`, `achievement`, `spirit`, `feeling`, `emotion`, `perspective`, `foreground`, `background`, `panoramic`.

**RULE #3: STRICTLY AVOID GENERIC PHOTOGRAPHY TERMS.**
UNDER NO CIRCUMSTANCES SAY "The image contains" or "shows", etc!

---
**OUTPUT REQUIREMENTS:**

**1. Title (5-10 words):**
*   Literal and objective. State the main subject, action, and location.
*   Example: "A young hiker with a backpack overlooking a mountain fjord."

**2. Description (1-3 sentences):**
*   Factually describe the scene based on visible elements. Mention colors, weather, or key objects.
*   End with a practical sentence about commercial use cases, avoiding emotional language. Example: "Suitable for travel websites, outdoor gear advertisements, or corporate retreat themes."

**3. Keywords (Generate between 35 and 50, numbered sequentially):**
*   **PRIORITY:** The first 10-15 keywords must be the most important, literal objects and locations.
*   **CONTENT:** Use only concrete nouns, locations, actions, and technical shot types (e.g., `aerial`, `close up`).
*   **CHECK:** Before finalizing, double-check that you have not used any words from the "FORBIDDEN WORDS" list.

**Output Format (Follow EXACTLY):**
Title: [Your 5-10 word title here]
Description: [Your 1-3 sentence description here]
Keywords: 1. keyword, 2. keyword, 3. keyword, ..., N. keyword
"""
    )

def create_refinement_prompt(initial_title, initial_description, initial_keywords, custom_prompt=None, place="", country="", time_info="", is_editorial=False):
    """
    PROMPT 2: Takes raw metadata and refines it into a final, commercially-optimized product.
    This prompt is extremely strict and rule-based.
    """
    return (
        f"""You are an automated Quality Control and Refining engine for stock media metadata. You will receive a DRAFT Title, Description, and Keywords. Your job is to analyze, clean, and reformat this draft according to strict commercial rules.

**CRITICAL RULES:**

**1. BANNED WORDS:** The final output MUST NOT contain any subjective, emotional, or unsearchable words. This is a non-negotiable filter.
   - **BANNED LIST:** `beautiful`, `stunning`, `majestic`, `picturesque`, `scenic` (unless part of `scenic view`), `breathtaking`, `tranquil`, `tranquility`, `peace`, `serene`, `idyllic`, `freedom`, `feeling`, `spirit`, `wanderlust`, `contemplation`, `photo`, `video`, `escape`, `offers`, `captures`.

**2. KEYWORD REFINEMENT:**
   - **Remove Banned Words:** Delete any keyword from the list that is on the BANNED LIST.
   - **Remove Weak Concepts:** Delete abstract concepts that buyers don't search for (e.g., `achievement`, `challenge`, `serenity`).
   - **Remove Duplicates:** Ensure every keyword is unique.
   - **Add location keywords:** Add any location keywords that are missing from the DRAFT Keywords. If there're already location keywords - add similar location keywords.
   - **Add missing relevant keywords:** Add any keywords that are missing from the DRAFT Keywords list that are relevant. AVOID keywords that contain 2 or more words.
   - **Enforce Count:** Trim the final list to between 35 and 50 of the STRONGEST, most relevant keywords.

**3. TITLE & DESCRIPTION REFINEMENT:**
   - Rewrite the DRAFT Title and Description to be factual and objective.
   - Remove ALL words from the BANNED LIST.
   - Ensure the description ends with a practical "Suitable for..." sentence.

**DRAFT METADATA FOR REFINEMENT:**

**Factual Context (Override if present):** {custom_prompt if custom_prompt else "None"}
{f"*   ADD TO THE END of the description: {place}, {country}, {time_info}." if is_editorial else ""}
**Draft Title:** {initial_title}
**Draft Description:** {initial_description}
**Draft Keywords:** {', '.join(initial_keywords)}

---
**YOUR TASK: Generate the FINAL, REFINED metadata below.**

**FINAL OUTPUT FORMAT (Follow EXACTLY):**
Title: [Your refined title here]
Description: [Your refined description here]
Keywords: 1. keyword, 2. keyword, 3. keyword, ..., N. keyword
"""
    )


# --- CORE FUNCTIONS ---

def parse_llm_output(content):
    """Parses the LLM response to extract Title, Description, and Keywords."""
    title_match = re.search(r'Title:\s*(.*?)\s*(?:Description:|$)', content, re.DOTALL | re.IGNORECASE)
    description_match = re.search(r'Description:\s*(.*?)\s*(?:Keywords:|$)', content, re.DOTALL | re.IGNORECASE)
    keywords_match = re.search(r'Keywords:\s*(.*)', content, re.DOTALL | re.IGNORECASE)

    if not title_match or not description_match or not keywords_match:
        parse_fail_msg = f"Failed to parse LLM response. Raw response: {content}"
        logging.error(parse_fail_msg)
        raise ValueError("Failed to parse the API response for title, description, and keywords.")

    title = title_match.group(1).strip()
    description = description_match.group(1).strip()
    keywords_raw = keywords_match.group(1).strip()

    # Handle both comma-separated and newline-separated keywords with numbers
    keywords_list = re.split(r'\s*,\s*|\s*\n\s*', keywords_raw)
    keywords = [re.sub(r'^\d+\.\s*', '', kw).strip().lower() for kw in keywords_list if kw.strip()]
    keywords = [kw for kw in keywords if kw] # Remove any empty strings

    return title, description, keywords


def ai_keywording(file_path, custom_prompt, enhance_output, is_editorial=False, place="", country="", time_info="", progress_callback=None):
    global glob_titles, glob_keys, errs, errs_files
    print(f"DEBUG AIK: START for {os.path.basename(file_path)}", flush=True)
    title, description, keywords = None, None, None
    initial_metadata = None  # Store initial metadata variant
    refined_metadata = None  # Store refined metadata variant
    video_file_to_delete = None  # To hold the uploaded file object for cleanup
    compressed_video_path = None  # To hold the compressed video path for cleanup

    try:
        genai.configure(api_key=API_KEY)
        model = genai.GenerativeModel(MODEL)
        generation_config = genai.types.GenerationConfig(temperature=0.8, max_output_tokens=4096)

        # --- Prepare Media for Gemini ---
        gemini_media_parts = []
        is_video = False
        media_path_to_process = None
        file_extension = os.path.splitext(file_path)[1].lower()

        # Determine if we should process a video or an image
        if file_extension in ('.mp4', '.mov', '.avi', '.wmv'):
            is_video = True
            media_path_to_process = file_path
        else:
            # For images, check if there's an associated video file to use instead
            has_matching_video, matching_video = check_for_matching_video(file_path)
            if has_matching_video and matching_video:
                is_video = True
                media_path_to_process = matching_video
            else:
                # It's confirmed to be an image file with no associated video
                media_path_to_process = file_path

        if is_video:
            print(f"DEBUG AIK: Processing video asset: {media_path_to_process}. Compressing to 480p first...", flush=True)

            # Compress video to 480p before uploading to reduce processing time and costs
            try:
                compressed_video_path = compress_video_for_ai(media_path_to_process, target_height=480)
                print(f"DEBUG AIK: Video compressed successfully. Original: {os.path.basename(media_path_to_process)}, Compressed: {os.path.basename(compressed_video_path)}", flush=True)

                # Show compression stats
                original_size = os.path.getsize(media_path_to_process)
                compressed_size = os.path.getsize(compressed_video_path)
                compression_ratio = compressed_size / original_size if original_size > 0 else 0
                print(f"DEBUG AIK: Compression ratio: {compression_ratio:.1%} ({original_size // 1024}KB -> {compressed_size // 1024}KB)", flush=True)

                # Use compressed video for upload
                video_to_upload = compressed_video_path
            except Exception as compression_error:
                print(f"DEBUG AIK: Video compression failed: {compression_error}. Using original video.", flush=True)
                logging.warning(f"Video compression failed for {media_path_to_process}: {compression_error}")
                video_to_upload = media_path_to_process
                compressed_video_path = None  # Reset since compression failed

            print(f"DEBUG AIK: Uploading video to Gemini: {os.path.basename(video_to_upload)}...", flush=True)
            # Upload the video file (compressed or original)
            uploaded_video = genai.upload_file(path=video_to_upload, display_name=os.path.basename(media_path_to_process))
            video_file_to_delete = uploaded_video  # Mark for deletion in the finally block
            print(f"DEBUG AIK: Uploading '{uploaded_video.display_name}' ({uploaded_video.name})... Current state: {uploaded_video.state.name}", flush=True)

            # Poll for the video to be processed by Google's servers.
            while uploaded_video.state.name == "PROCESSING":
                print("DEBUG AIK: Video is processing, waiting 10 seconds...", flush=True)
                time.sleep(10)
                uploaded_video = genai.get_file(name=uploaded_video.name)

            if uploaded_video.state.name == "FAILED":
                raise ValueError(f"Video processing failed for {media_path_to_process}.")

            if uploaded_video.state.name != "ACTIVE":
                 raise ValueError(f"Video is not in an ACTIVE state after processing. Current state: {uploaded_video.state.name}")

            print(f"DEBUG AIK: Video processed and ready. State: {uploaded_video.state.name}", flush=True)
            gemini_media_parts.append(uploaded_video)

        else:  # It's an image
            if os.path.exists(media_path_to_process):
                print(f"DEBUG AIK: Processing image file: {media_path_to_process}", flush=True)
                img = Image.open(media_path_to_process)
                if img.mode in ('RGBA', 'P'): img = img.convert('RGB')
                # Resize to reduce token usage and improve speed
                scaling_factor = 2
                img = img.resize((img.width // scaling_factor, img.height // scaling_factor), Image.LANCZOS)
                gemini_media_parts.append(img)
            else:
                raise FileNotFoundError(f"Image file not found: {media_path_to_process}")

        # --- LLM Call Step 1: Generation ---
        print("DEBUG AIK: 1. Starting Generation Step...", flush=True)
        generative_prompt = create_generative_prompt(custom_prompt, place, country, time_info, is_editorial)
        gemini_parts_list = [generative_prompt] + gemini_media_parts

        response = model.generate_content(contents=gemini_parts_list, generation_config=generation_config)
        if not response.parts and hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
            raise ValueError(f"Gemini API request blocked. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}")
        if not response.text:
             raise ValueError(f"Gemini API response is empty or malformed. Full response: {response}")

        raw_content = response.text
        print("DEBUG AIK: 1. Generation Step Complete.", flush=True)

        # --- LLM Call Step 2: Refinement (Conditional) ---
        if enhance_output:
            print("DEBUG AIK: 2. Starting Refinement Step...", flush=True)
            initial_title, initial_description, initial_keywords = parse_llm_output(raw_content)

            # Store initial metadata variant
            initial_metadata = {
                'title': initial_title,
                'description': initial_description,
                'keywords': initial_keywords
            }

            # Call the progress callback with Stage 1 data
            if progress_callback:
                progress_callback(stage=1, data={'title': initial_title, 'description': initial_description, 'keywords': initial_keywords})

            refinement_prompt = create_refinement_prompt(initial_title, initial_description, initial_keywords, custom_prompt, place, country, time_info, is_editorial)
            response = model.generate_content(contents=[refinement_prompt], generation_config=generation_config)

            if not response.parts and hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
                raise ValueError(f"Gemini API (Refinement) request blocked. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}")
            if not response.text:
                raise ValueError(f"Gemini API (Refinement) response is empty or malformed. Full response: {response}")

            final_content = response.text
            print("DEBUG AIK: 2. Refinement Step Complete.", flush=True)
        else:
            print("DEBUG AIK: Skipping Refinement Step as enhance_output is False.", flush=True)
            final_content = raw_content

        # --- Final Parsing ---
        print("DEBUG AIK: 3. Parsing final LLM output...", flush=True)
        title, description, keywords = parse_llm_output(final_content)

        # Store refined metadata variant (or final if no refinement)
        refined_metadata = {
            'title': title,
            'description': description,
            'keywords': keywords
        }

        # If no refinement was done, both variants are the same
        if not enhance_output:
            initial_metadata = refined_metadata.copy()

        glob_titles.append(title)
        glob_keys.append(keywords)

        print(f"--- Successfully processed: {os.path.basename(file_path)} ---", flush=True)

    except (google_exceptions.GoogleAPIError, ValueError, Exception) as e:
        error_type = type(e).__name__
        print(f"DEBUG AIK: ERROR - {error_type} for {os.path.basename(file_path)}: {e}", flush=True)
        logging.error(f"{error_type} processing file {file_path}: {e}", exc_info=True)
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    finally:
        # Cleanup: Delete the video file from Google's servers if it was uploaded
        if video_file_to_delete:
            try:
                print(f"DEBUG AIK: Deleting uploaded video file: {video_file_to_delete.name}", flush=True)
                genai.delete_file(name=video_file_to_delete.name)
            except Exception as del_e:
                print(f"DEBUG AIK: ERROR - Failed to delete uploaded file {video_file_to_delete.name}: {del_e}", flush=True)
                logging.error(f"Failed to delete uploaded file {video_file_to_delete.name}: {del_e}")

        # Cleanup: Delete the compressed temporary video file if it was created
        if compressed_video_path and os.path.exists(compressed_video_path):
            try:
                print(f"DEBUG AIK: Deleting compressed temporary video: {os.path.basename(compressed_video_path)}", flush=True)
                os.remove(compressed_video_path)
            except Exception as del_e:
                print(f"DEBUG AIK: ERROR - Failed to delete compressed video {compressed_video_path}: {del_e}", flush=True)
                logging.error(f"Failed to delete compressed video {compressed_video_path}: {del_e}")

        print(f"DEBUG AIK: FINALLY for {os.path.basename(file_path)}", flush=True)

    print(f"DEBUG AIK: END for {os.path.basename(file_path)}. Returning both metadata variants.", flush=True)
    return initial_metadata, refined_metadata


def update_exif(file_path, title, description, keywords):
    """Updates image EXIF data with Title, Description (as Subject), and Keywords."""
    print(f"DEBUG UPEXIF: START for {path.basename(file_path)}", flush=True)
    exif_success = False

    try:
        img = Image.open(file_path)

        # Prepare Keyword List for EXIF
        keywords_str = ";".join(keywords) if keywords else ""

        try:
            exif_dict = piexif.load(file_path)
        except Exception:
            exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}

        # Encode strings as UTF-16LE with BOM for Windows compatibility
        def encode_for_xp(text):
            return text.encode('utf-16le') if text else b''

        # Standard EXIF tags
        if keywords_str:
            exif_dict["0th"][piexif.ImageIFD.XPKeywords] = encode_for_xp(keywords_str)
        elif piexif.ImageIFD.XPKeywords in exif_dict["0th"]:
            del exif_dict["0th"][piexif.ImageIFD.XPKeywords]

        if title:
            exif_dict["0th"][piexif.ImageIFD.XPTitle] = encode_for_xp(title)
        elif piexif.ImageIFD.XPTitle in exif_dict["0th"]:
            del exif_dict["0th"][piexif.ImageIFD.XPTitle]

        if description:
            exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode('ascii', 'ignore')
            exif_dict["Exif"][piexif.ExifIFD.UserComment] = b"ASCII\0\0\0" + description.encode('ascii', 'ignore')
        else:
            if piexif.ImageIFD.ImageDescription in exif_dict["0th"]:
                del exif_dict["0th"][piexif.ImageIFD.ImageDescription]
            if piexif.ExifIFD.UserComment in exif_dict["Exif"]:
                del exif_dict["Exif"][piexif.ExifIFD.UserComment]

        exif_bytes = piexif.dump(exif_dict)
        img.save(file_path, exif=exif_bytes)
        exif_success = True
        print(f'DEBUG UPEXIF: EXIF metadata update SUCCEEDED for {path.basename(file_path)}.', flush=True)

    except Exception as e:
        print(f"DEBUG UPEXIF: CRITICAL ERROR in update_exif for {path.basename(file_path)}: {type(e).__name__} - {e}", flush=True)
        exif_success = False

    print(f"DEBUG UPEXIF: END for {path.basename(file_path)}, final EXIF success status: {exif_success}", flush=True)
    return exif_success