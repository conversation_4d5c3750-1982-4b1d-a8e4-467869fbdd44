import os
import tkinter as tk
from tkinter import filedialog
import json
import sys
import base64
from io import BytesIO
import webview
from PIL import Image
import pyexiv2
import logging
import threading
import cv2
import queue

from ai_keyworder import ai_keywording, update_exif
from config import open_app

# --- Global Variables ---
_window = None
folder_path = None
# Set up logging (Keep as is)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Helper function to get asset path (Copied from gui.py) ---
def get_asset_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    # Ensure the 'web' directory is included for HTML/JS/CSS
    return os.path.join(base_path, 'web', relative_path) # Modified to look inside 'web' subdir


# --- Pywebview API Class ---
class Api:
    def __init__(self, app_instance):
        self.app = app_instance # Reference to the main application logic instance

    def _get_window(self):
        # Helper to safely get the window object
        global _window
        if _window:
            return _window
        elif webview.windows:
             _window = webview.windows[0]
             return _window
        print("Error: Window object not found.")
        return None

    def reload(self):
        self.app.trigger_load_images(folder_path)

    def request_folder_dialog(self):
        """Called by JS to open a folder selection dialog."""
        global folder_path
        window = self._get_window()
        if not window:
            self.app.update_status_js("Error: Could not access window object.", "error")
            return

        try:
            result = window.create_file_dialog(webview.FOLDER_DIALOG)
            print(f"Folder dialog result: {result}")
            if result and isinstance(result, (list, tuple)) and len(result) > 0:
                folder_path = result[0]
                if os.path.isdir(folder_path):
                    print(f"Folder selected: {folder_path}")
                    # Trigger the loading process in the main app logic
                    self.app.trigger_load_images(folder_path)
                else:
                    self.app.update_status_js(f"Selected path is not a valid folder: {folder_path}", "error")
            elif result is None:
                 self.app.update_status_js("Folder selection cancelled.", "info")
            else:
                 self.app.update_status_js("No folder selected or invalid result.", "warning")

        except Exception as e:
            print(f"Error opening folder dialog: {e}")
            # Fallback using tkinter dialog if pywebview one fails critically
            try:
                print("Falling back to tkinter folder dialog...")
                # Make sure tkinter root is handled properly if used
                root = tk.Tk()
                root.withdraw() # Hide the main tkinter window
                folder_path = filedialog.askdirectory()
                root.destroy()
                if folder_path and os.path.isdir(folder_path):
                     print(f"Folder selected (tkinter fallback): {folder_path}")
                     self.app.trigger_load_images(folder_path)
                else:
                     self.app.update_status_js("Folder selection cancelled or invalid (fallback).", "info")
            except Exception as tk_e:
                print(f"Tkinter fallback also failed: {tk_e}")
                self.app.update_status_js(f"Error opening folder dialog: {e}", "error")


    def select_image_api(self, image_path):
        """Called by JS when an image thumbnail is clicked."""
        print(f"API: Image selected: {image_path}")
        if not image_path or not isinstance(image_path, str):
             print("Error: Invalid image path received from JS.")
             self.app.update_status_js("Internal error: Invalid image path received.", "error")
             return
        # Call the main app's logic to handle selection and load metadata
        self.app.handle_image_selection(image_path)

    def open_preview_picker_api(self, image_path):
        """Called by JS to launch the Preview Picker app."""
        if not image_path or not isinstance(image_path, str):
             print("Error: Invalid image path received for preview picker.")
             self.app.update_status_js("Internal error: Invalid image path.", "error")
             return
        print(f"API: Request to launch Preview Picker for image: {image_path}")
        self.app.update_status_js("Preparing Preview Picker...", "info")
        # Call the main app logic to handle finding the video and launching
        # Run in a thread to avoid blocking UI during potential checks
        threading.Thread(target=self.app.launch_preview_picker, args=(image_path,), daemon=True).start()

    def save_metadata_api(self, metadata_json):
        """Called by JS with the edited metadata to save."""
        print(f"API: Request to save metadata")
        try:
            metadata = json.loads(metadata_json)
            image_path = metadata.get('imagePath')
            title = metadata.get('title', '')
            description = metadata.get('description', '')
            keywords_str = metadata.get('keywords', '')

            if not image_path:
                self.app.update_status_js("Error: No image path provided for saving.", "error")
                return

            # Call the main app's logic to save
            self.app.trigger_save_metadata(image_path, title, description, keywords_str)

        except json.JSONDecodeError as e:
            print(f"Error decoding metadata JSON from JS: {e}")
            self.app.update_status_js("Error: Invalid data format received from client.", "error")
        except Exception as e:
            print(f"Error processing save request: {e}")
            self.app.update_status_js(f"Error saving metadata: {e}", "error")

    def trigger_auto_keyword_api(self, image_path):
        """Called by JS to run AI keywording on the selected image."""
        print(f"API: Triggering auto-keywording for: {image_path}")
        if not image_path or not isinstance(image_path, str):
             print("Error: Invalid image path received for auto-keywording.")
             self.app.update_status_js("Internal error: Select an image first.", "error")
             return
        # Call the main app's logic
        self.app.run_auto_keyword_single(image_path)

    def open_ai_keyworder(self):
        """Called by JS to launch the separate AI Keyworder app."""
        print("API: Launching AI Keyworder app...")
        self.app.update_status_js("Launching AI Keyworder...", "info")
        try:
            # Run in a separate thread to avoid blocking the gallery UI
            open_app("gui.py")
        except Exception as e:
            print(f"Error launching AI Keyworder: {e}")
            self.app.update_status_js(f"Error launching AI Keyworder: {e}", "error")

    def open_stock_uploader(self):
        """Called by JS to launch the separate Stock Uploader app."""
        print("API: Launching Stock Uploader app...")
        self.app.update_status_js("Launching Stock Uploader...", "info")
        try:
            # Run in a separate thread
            open_app("stock_uploader.py")
        except Exception as e:
            print(f"Error launching Stock Uploader: {e}")
            self.app.update_status_js(f"Error launching Stock Uploader: {e}", "error")

# --- Main Application Logic Class (Adapted from ImageGalleryApp) ---
class ImageGalleryAppLogic:
    def __init__(self):
        self.current_folder = None
        self.image_files_list = []
        self.current_image_path = None
        self.thumbnail_cache = {}
        self.api = Api(self)

        # *** ADD LOCK FOR PYEXIV2 THREAD SAFETY ***
        self.pyexiv2_lock = threading.Lock()

        self.thumbnail_size = (800, 800)  # Increased from 140x140 to 800x800 for high resolution
        self.batch_size = 15

    def _get_window(self):
        # Reusing API's helper for consistency
        return self.api._get_window()

    def update_status_js(self, message, type="info"):
        """Sends status updates to the JS frontend (info, success, warning, error)."""
        window = self._get_window()
        if window:
            try:
                escaped_message = json.dumps(message)
                js_code = f"updateStatus({escaped_message}, '{type}');"
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error evaluating JS for status update: {e}")
        else:
            print(f"Status (window not found): {message}")

    def update_gallery_js(self, image_batch_data):
        """Sends a batch of image data (path, thumbnail) to JS to display."""
        window = self._get_window()
        if window:
            try:
                # Data should be a list of dicts: [{'path': '...', 'thumbnail': 'data:image/jpeg;base64,...'}, ...]
                json_data = json.dumps(image_batch_data)
                # Ensure JS function exists and handles this data
                js_code = f"addImagesToGallery({json_data});"
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error evaluating JS for gallery update: {e}")
        else:
            print(f"Gallery Update (window not found): {len(image_batch_data)} items")

    def clear_gallery_js(self):
         """Tells JS to clear the current gallery display."""
         window = self._get_window()
         if window:
             try:
                 window.evaluate_js("clearGallery();")
             except Exception as e:
                 print(f"Error evaluating JS for clearing gallery: {e}")
         else:
            print("Clear Gallery (window not found)")

    def update_metadata_js(self, metadata):
        """Sends metadata of the selected image to JS to populate the form."""
        window = self._get_window()
        if window:
            try:
                json_data = json.dumps(metadata)
                # Ensure JS function exists and handles this data
                js_code = f"displayMetadata({json_data});"
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error evaluating JS for metadata update: {e}")
        else:
            print(f"Metadata Update (window not found): {metadata.get('title')}")

    def clear_metadata_js(self):
        """Tells JS to clear the metadata form."""
        window = self._get_window()
        if window:
            try:
                window.evaluate_js("clearMetadataForm();")
            except Exception as e:
                print(f"Error evaluating JS for clearing metadata: {e}")
        else:
            print("Clear Metadata (window not found)")


    # --- Core Logic Methods (Adapted from original Tkinter app) ---

    def trigger_load_images(self, folder_path):
        """Starts the image loading process in a separate thread."""
        if not os.path.isdir(folder_path):
            self.update_status_js(f"Invalid folder path: {folder_path}", "error")
            return

        self.current_folder = folder_path
        self.current_image_path = None # Reset selection
        self.image_files_list = [] # Reset list
        self.thumbnail_cache = {} # Clear cache

        self.update_status_js(f"Loading images from: {os.path.basename(folder_path)}...", "info")
        self.clear_gallery_js() # Tell JS to clear old gallery items
        self.clear_metadata_js() # Clear metadata panel too

        # Start loading in a background thread
        threading.Thread(target=self.load_images_thread, args=(folder_path,), daemon=True).start()

    def load_images_thread(self, folder_path):
        """Background thread to find images, generate missing previews, generate thumbnails, read comments, and send batches to JS."""
        try:
            self.update_status_js("Scanning folder contents...", "info")
            all_files = os.listdir(folder_path)
            logging.info(f"Found {len(all_files)} items in {folder_path}")

            image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"}
            video_extensions = {".mp4", ".mov", ".avi", ".wmv", ".mkv"}

            image_files = set()
            image_basenames = set()
            video_files = {}

            for f in all_files:
                full_path = os.path.join(folder_path, f)
                if not os.path.isfile(full_path):
                    continue

                basename, ext = os.path.splitext(f)
                ext_lower = ext.lower()

                if ext_lower in image_extensions:
                    image_files.add(full_path)
                    if ext_lower in {".jpg", ".jpeg"}:
                        image_basenames.add(basename)
                elif ext_lower in video_extensions:
                    video_files[basename] = full_path

            logging.info(f"Identified {len(image_files)} existing images and {len(video_files)} videos.")

            # --- Auto-Generate Missing Previews ---
            newly_generated_previews = set()
            if video_files:
                self.update_status_js("Checking for missing video previews...", "info")
                previews_to_generate = []
                for video_basename, video_path in video_files.items():
                    if video_basename not in image_basenames:
                        previews_to_generate.append((video_path, video_basename))
                    else:
                        logging.debug(f"Preview already exists for video: {os.path.basename(video_path)}")

                if previews_to_generate:
                    total_to_gen = len(previews_to_generate)
                    generated_count = 0
                    logging.info(f"Found {total_to_gen} videos needing previews.")
                    self.update_status_js(f"Generating {total_to_gen} missing previews...", "info")
                    for video_path, video_basename in previews_to_generate:
                        target_preview_path = os.path.join(folder_path, f"{video_basename}.jpg")
                        generated_count += 1
                        self.update_status_js(f"Generating preview ({generated_count}/{total_to_gen}): {video_basename}.jpg", "info")
                        cap = None
                        try:
                            cap = cv2.VideoCapture(video_path)
                            if not cap.isOpened():
                                logging.warning(f"Could not open video file for preview: {video_path}")
                                continue
                            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                            target_frame_index = 29
                            if total_frames <= 0:
                                logging.warning(f"Video has no frames or failed to read count: {video_path}")
                                continue
                            if total_frames <= target_frame_index:
                                logging.warning(f"Video '{os.path.basename(video_path)}' too short ({total_frames} frames), using last frame ({total_frames-1}) for preview.")
                                target_frame_index = total_frames - 1
                            cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame_index)
                            ret, frame = cap.read()
                            if ret and frame is not None:
                                success = cv2.imwrite(target_preview_path, frame, [int(cv2.IMWRITE_JPEG_QUALITY), 90])
                                if success:
                                    logging.info(f"Successfully generated preview: {target_preview_path}")
                                    newly_generated_previews.add(target_preview_path)
                                else:
                                    logging.error(f"Failed to write preview image: {target_preview_path}")
                            else:
                                logging.error(f"Failed to read frame {target_frame_index} from video: {video_path}")
                        except Exception as e:
                            logging.error(f"Error generating preview for {video_path}: {e}", exc_info=True)
                        finally:
                            if cap and cap.isOpened():
                                cap.release()
                    self.update_status_js(f"Finished generating {len(newly_generated_previews)} previews.", "info")
                else:
                     self.update_status_js("All video previews seem to exist.", "info")

            # --- Combine existing images and newly generated previews ---
            self.image_files_list = sorted(list(image_files) + list(newly_generated_previews))

            # --- Proceed with Thumbnail Generation and Comment Reading ---
            total_images_to_display = len(self.image_files_list)
            if total_images_to_display == 0:
                self.update_status_js("No image files (including previews) found to display.", "warning")
                window = self._get_window()
                if window: window.evaluate_js("showNoImagesMessage();")
                return

            self.update_status_js(f"Found {total_images_to_display} images/previews. Processing thumbnails & comments...", "info")

            processed_count = 0
            for i in range(0, total_images_to_display, self.batch_size):
                batch_paths = self.image_files_list[i:i + self.batch_size]
                batch_data = []
                for img_path in batch_paths:
                    user_comment = '' # Default empty comment
                    thumbnail_b64 = None
                    filename = os.path.basename(img_path)
                    file_extension = os.path.splitext(filename)[1].lower()

                    try:
                        # Generate or get cached thumbnail
                        thumbnail_b64 = self._get_or_create_thumbnail(img_path)
                        progress_value = 0
                        if file_extension in ['.jpg', '.jpeg', '.tiff']:
                            img_exif = None
                            try:
                                # Acquire lock specifically for reading this image's comment
                                with self.pyexiv2_lock:
                                    # logging.debug(f"Acquiring lock to read comment for {filename}")
                                    img_exif = pyexiv2.Image(img_path)
                                    # Use UserComment primarily, fallback could be added if needed
                                    raw_comment = img_exif.read_exif().get('Exif.Image.XPComment', '')
                                    # Decode if bytes
                                    if isinstance(raw_comment, bytes):
                                        try:
                                            user_comment = raw_comment.decode('utf-16le', errors='replace')
                                            progress_value = user_comment.count("✅")
                                        except UnicodeDecodeError:
                                            try:
                                                 user_comment = raw_comment.decode(sys.getdefaultencoding(), errors='replace')
                                                 progress_value = user_comment.count("✅")
                                            except Exception:
                                                 user_comment = "(Could not decode comment)" # Placeholder on error
                                    else:
                                        user_comment = str(raw_comment) # Ensure it's a string
                                        progress_value = user_comment.count("✅")

                                    user_comment = user_comment.strip() # Remove leading/trailing whitespace

                            except FileNotFoundError:
                                logging.warning(f"File not found while trying to read comment: {img_path}")
                                progress_value = 0
                            except Exception as comment_err:
                                logging.warning(f"Could not read comment for {filename}: {comment_err}")
                                progress_value = 0
                            finally:
                                if img_exif:
                                    try: img_exif.close()
                                    except Exception: pass
                                # logging.debug(f"Released lock after reading comment for {filename}")

                        has_matching_video, video = self.video_check(True,
                                                                     img_path, {'imagePath': img_path},
                                                                     file_extension)
                        # Add data for JS, including the comment
                        batch_data.append({
                            'path': img_path,
                            'thumbnail': thumbnail_b64,
                            'filename': filename if not has_matching_video else filename.replace("jpg", "mp4"),
                            'userComment': user_comment, # Add the comment here
                            'progressValue': progress_value * (20 if has_matching_video else 25)
                        })
                        processed_count += 1

                    except Exception as thumb_e:
                        logging.error(f"Failed to process thumbnail for {img_path}: {thumb_e}", exc_info=True)

                        has_matching_video, video = self.video_check(True,
                                                                     img_path, {'imagePath': img_path},
                                                                     file_extension)
                        batch_data.append({
                             'path': img_path,
                             'thumbnail': None,
                             'filename': filename if not has_matching_video else filename.replace("jpg", "mp4"),
                             'userComment': '', # Ensure comment is empty string on error
                             'progressValue': progress_value * (20 if has_matching_video else 25)
                        })
                        processed_count += 1

                if batch_data:
                    self.update_gallery_js(batch_data) # Send batch to JS

                # Optional status update
                if i > 0 and i % (self.batch_size * 5) == 0:
                     self.update_status_js(f"Processing items: {processed_count}/{total_images_to_display}", "info")

            final_status = f"Finished loading {total_images_to_display} items."
            if newly_generated_previews:
                final_status += f" ({len(newly_generated_previews)} previews generated)"
            self.update_status_js(final_status, "success")

            window = self._get_window()
            if window:
                try:
                    window.evaluate_js("enableReloadButton();")
                except Exception as e:
                    print(f"JS Error enabling reload button: {e}")

            if self.image_files_list: # Only run if images were actually found/generated
                logging.info("Queueing background task to add default comments if missing.")
                # Pass a *copy* of the list to the thread
                thread_image_list = list(self.image_files_list)
                threading.Thread(target=self._apply_default_comments_thread,
                                 args=(thread_image_list,), # Pass the copied list
                                 daemon=True).start()

        except Exception as e:
            logging.error(f"Error during image/preview loading from {folder_path}: {e}", exc_info=True)
            self.update_status_js(f"Error loading folder contents: {e}", "error")

    def _apply_default_comments_thread(self, image_paths_to_check):
        """
        Background thread to check images and add a default comment
        if no UserComment/XPComment exists.
        """
        default_comment = ("❌ Adobe Stock\n\n"
                           "❌ Shutterstock\n\n"
                           "❌ iStock\n\n"
                           "❌ Pond5\n\n"
                           "❌ Element Envato")
        modified_count = 0
        processed_count = 0
        total_images = len(image_paths_to_check)
        logging.info(f"Starting default comment check for {total_images} images.")
        self.update_status_js(f"Checking {total_images} images for missing comments...", "info")

        for image_path in image_paths_to_check:
            processed_count += 1
            image_exif = None  # Ensure defined before try block

            # Skip non-writable image types early
            file_extension = os.path.splitext(image_path)[1].lower()
            if file_extension not in ('.jpg', '.jpeg', '.tiff'):  # Add other writable types if needed
                continue

            try:
                # *** Acquire lock for BOTH check and potential write ***
                with self.pyexiv2_lock:
                    img_exif = pyexiv2.Image(image_path)
                    existing_comment = img_exif.read_exif().get('Exif.Image.XPComment', '')
                    # 2. If no comment, add the default one
                    if not existing_comment:
                        try:
                            image_exif = pyexiv2.Image(image_path)
                            has_matching_video, video = self.video_check(True,
                                                                         image_path, {'imagePath': image_path},
                                                                         file_extension)
                            exif_modifications = {
                                # Supply XPComment as a string rather than pre-encoded bytes
                                'Exif.Image.XPComment': default_comment if has_matching_video else default_comment.replace("❌ iStock", "✖️ iStock (No Images)")
                            }
                            image_exif.modify_exif(exif_modifications)
                            image_exif.close()  # Save changes
                            modified_count += 1
                        except FileNotFoundError:
                            logging.warning(f"File not found when trying to add default comment: {image_path}")
                        except Exception as write_e:
                            logging.error(f"Failed to write default comment to {image_path}: {write_e}")
                            if image_exif:
                                try:
                                    image_exif.close()  # Attempt to close even on error
                                except Exception:
                                    pass

                    # Optional: Update status periodically
                if processed_count % 50 == 0:  # Update every 50 images
                    self.update_status_js(f"Checking comments: {processed_count}/{total_images}...", "info")

            except Exception as e:
                logging.error(f"Unexpected error processing default comment for {image_path}: {e}")
                # Safety: Force release lock if needed (shouldn't normally happen)
                if self.pyexiv2_lock.locked():
                    try:
                        self.pyexiv2_lock.release()
                        logging.warning(
                            f"Force-released pyexiv2 lock due to error during comment check for {os.path.basename(image_path)}")
                    except threading.ThreadError:
                        pass  # Lock may have already been released

            # Final status update
            if modified_count > 0:
                self.update_status_js(f"Finished comment check. Added default comment to {modified_count} images.",
                                      "success")
                logging.info(f"Finished default comment check. Added default comment to {modified_count} images.")
            else:
                self.update_status_js("Finished comment check. No missing comments found.", "info")
                logging.info("Finished default comment check. No missing comments found.")

        if modified_count > 0:
            self.api.reload()

    def _get_or_create_thumbnail(self, img_path):
        """Generates a Base64 thumbnail string for an image, using cache."""
        if img_path in self.thumbnail_cache:
            return self.thumbnail_cache[img_path]

        try:
            img = Image.open(img_path)
            img.thumbnail(self.thumbnail_size) # Resize in place

            # Handle transparency for PNG/GIF
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                 format = 'PNG'
            else:
                 format = 'JPEG' # Use JPEG for opaque images (smaller)

            buffered = BytesIO()
            img.save(buffered, format=format, quality=95) # Increased quality from 85 to 95
            img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')
            thumbnail_b64 = f"data:image/{format.lower()};base64,{img_str}"
            self.thumbnail_cache[img_path] = thumbnail_b64 # Cache it
            return thumbnail_b64
        except Exception as e:
            logging.error(f"Failed to create thumbnail for {img_path}: {e}")
            self.thumbnail_cache[img_path] = None # Cache failure
            return None


    def handle_image_selection(self, image_path):
        """Reads metadata for the selected image and sends it to JS."""
        # (Keep the existing implementation of this method)
        if not os.path.exists(image_path):
            self.update_status_js(f"Error: Selected image file not found: {os.path.basename(image_path)}", "error")
            self.clear_metadata_js()
            return

        self.current_image_path = image_path
        self.update_status_js(f"Loading metadata for: {os.path.basename(image_path)}...", "info")
        threading.Thread(target=self._read_metadata_thread, args=(image_path,), daemon=True).start()

    def video_check(self, is_potentially_preview, image_path, metadata, file_extension):
        matching_video_path = None
        has_matching_video = False
        if is_potentially_preview:
            base_name, _ = os.path.splitext(os.path.basename(image_path))
            dir_name = os.path.dirname(image_path)
            video_extensions = ('.mp4', '.mov', '.avi', '.wmv') # Define common video extensions
            for ext in video_extensions:
                potential_video = os.path.join(dir_name, base_name + ext)
                if os.path.exists(potential_video):
                    # Basic check if it's readable by OpenCV (optional but good)
                    temp_cap = cv2.VideoCapture(potential_video)
                    if temp_cap.isOpened():
                        matching_video_path = potential_video
                        has_matching_video = True
                        temp_cap.release()
                        break # Found one
                    temp_cap.release() # Release even if not opened correctly

        metadata['hasMatchingVideo'] = has_matching_video
        # We don't strictly need to pass matching_video_path to JS here,
        # but the flag 'hasMatchingVideo' is essential.
        # --- END VIDEO CHECK ---

        # Determine fileType based on check result
        return has_matching_video, file_extension in ('.mp4', '.mov', '.avi', '.wmv')

    def _read_metadata_thread(self, image_path):
        """Background thread to read EXIF metadata AND check for video."""
        metadata = {'imagePath': image_path}
        file_extension = os.path.splitext(image_path)[1].lower()
        is_potentially_preview = file_extension not in ('.mp4', '.mov', '.avi', '.wmv', '.gif') # Is it an image type?

        # Video check
        has_matching_video, video = self.video_check(is_potentially_preview, image_path, metadata, file_extension)
        if has_matching_video:
             metadata['fileType'] = "Video (Preview)"
        elif video:
             metadata['fileType'] = "Video"
        elif file_extension == '.gif':
             metadata['fileType'] = "GIF"
        else:
             metadata['fileType'] = "Photo"

        exif_data = {}
        keywords = []
        title = ''
        description = ''
        image_exif = None

        try:
            # Only read EXIF etc. for non-videos/gifs OR if it's a preview image
            if metadata['fileType'] in ["Photo", "Video (Preview)"]:
                # *** ACQUIRE LOCK FOR PYEXIV2 ACCESS ***
                with self.pyexiv2_lock:
                    logging.debug(f"Acquired lock for reading {os.path.basename(image_path)}")
                    try:
                        image_exif = pyexiv2.Image(image_path)
                        raw_exif = image_exif.read_exif()
                        raw_iptc = image_exif.read_iptc()
                        raw_xmp = image_exif.read_xmp()

                        # Updated metadata extraction using "Correct reading" approach:
                        title = raw_exif.get('Exif.Image.XPTitle', raw_exif.get('Exif.Image.DocumentName', ''))
                        description = raw_exif.get('Exif.Image.ImageDescription',
                                                   raw_exif.get('Exif.Photo.UserComment', ''))
                        keywords_str = raw_exif.get('Exif.Image.XPKeywords', '')
                        keywords = keywords_str.split(';') if keywords_str else []

                        # Decode title and description if they are in bytes
                        title = title.decode('utf-8', errors='ignore') if isinstance(title, bytes) else str(title)
                        description = description.decode('utf-8', errors='ignore') if isinstance(description,
                                                                                                 bytes) else str(
                            description)

                    except FileNotFoundError:
                        logging.error(f"File not found during locked metadata read: {image_path}")
                        self.update_status_js(f"Error: File disappeared: {os.path.basename(image_path)}", "error")
                        self.clear_metadata_js()
                        return  # Exit thread early
                    except Exception as e:
                        logging.error(f"Failed to read metadata (within lock) for {image_path}: {e}")
                        self.update_status_js(f"Warning: Could not read metadata for {os.path.basename(image_path)}.",
                                              "warning")
                        title = title or os.path.splitext(os.path.basename(image_path))[
                            0]  # Use filename as title fallback
                    finally:
                        if image_exif:
                            try:
                                image_exif.close()
                            except Exception as close_err:
                                logging.warning(f"Error closing pyexiv2 image object for {image_path}: {close_err}")
                        logging.debug(f"Released lock after reading {os.path.basename(image_path)}")

            metadata['title'] = title
            metadata['description'] = description
            metadata['keywords'] = '; '.join(keywords)
            metadata['keywordsCount'] = len(keywords)

            # Update JS with all collected info
            self.update_metadata_js(metadata)
            # Update status only *after* JS has been updated
            status_msg = f"Metadata loaded for: {os.path.basename(image_path)}"
            if has_matching_video: status_msg += " (Matching video found)"
            self.update_status_js(status_msg, "info")

        except Exception as e:
             logging.error(f"Unexpected error reading metadata thread for {image_path}: {e}", exc_info=True)
             self.update_status_js(f"Error reading metadata: {e}", "error")
             self.clear_metadata_js() # Clear form on major error

    def trigger_save_metadata(self, image_path, title, description, keywords_str):
        """Starts the metadata saving process in a separate thread."""
        if not self.current_image_path or self.current_image_path != image_path:
            self.update_status_js("Error: Image context mismatch. Please re-select the image.", "error")
            return

        if not os.path.exists(image_path):
            self.update_status_js(f"Error: File not found, cannot save metadata: {os.path.basename(image_path)}", "error")
            return

        self.update_status_js(f"Saving metadata for: {os.path.basename(image_path)}...", "info")
        # Start saving in background thread
        threading.Thread(target=self._save_metadata_thread,
                         args=(image_path, title, description, keywords_str),
                         daemon=True).start()

    def _save_metadata_thread(self, image_path, title, description, keywords_str):
        """Background thread to save EXIF metadata using modify_exif."""
        image_exif = None # Define outside try
        try:
            keywords = sorted(list(set(k.strip() for k in keywords_str.split(';') if k.strip())))
            keywords_count = len(keywords)

            file_extension = os.path.splitext(image_path)[1].lower()
            if file_extension in ('.mp4', '.mov', '.avi', '.wmv', '.gif'):
                self.update_status_js(f"Warning: Metadata saving not supported for {file_extension} files.", "warning")
                return

            # *** ACQUIRE LOCK FOR PYEXIV2 WRITE ACCESS ***
            with self.pyexiv2_lock:
                logging.debug(f"Acquired lock for saving {os.path.basename(image_path)}")
                try:
                    # Use modify_exif similar to the provided update_exif function
                    image_exif = pyexiv2.Image(image_path)
                    # Prepare EXIF data dictionary
                    exif_modifications = {
                        # Use XP standard tags primarily
                        'Exif.Image.XPTitle': str(title),
                        'Exif.Image.XPKeywords': ';'.join(keywords),
                        # Optionally add other tags if needed, e.g., Description
                        'Exif.Image.ImageDescription': str(description),
                        # UserComment is another place for description/comments
                        'Exif.Photo.UserComment': str(description),
                        # XPSubject often mirrors title
                        'Exif.Image.XPSubject': str(title)
                        # Add other EXIF tags here if necessary
                    }

                    # Clear tags if the corresponding input is empty
                    if not title:
                        exif_modifications['Exif.Image.XPTitle'] = ''
                        exif_modifications['Exif.Image.XPSubject'] = ''
                    if not description:
                        exif_modifications['Exif.Image.ImageDescription'] = ''
                        exif_modifications['Exif.Photo.UserComment'] = ''
                    if not keywords:
                        exif_modifications['Exif.Image.XPKeywords'] = '' # Send empty string to clear

                    # *** CALL modify_exif ***
                    image_exif.modify_exif(exif_modifications)
                    # pyexiv2 typically saves on close() or when the object is destroyed

                    self.update_status_js(f"Metadata saved successfully for: {os.path.basename(image_path)}", "success")

                    # Update keyword count in UI
                    window = self._get_window()
                    if window: window.evaluate_js(f"updateKeywordCount({keywords_count});")

                except FileNotFoundError:
                    logging.error(f"File not found during locked metadata save: {image_path}")
                    self.update_status_js(f"Error: File disappeared before saving: {os.path.basename(image_path)}", "error")
                    return # Exit thread early
                except Exception as e:
                    logging.error(f"Failed to save metadata (within lock) to {image_path}: {e}")
                    self.update_status_js(f"Error saving metadata: {e}", "error")
                finally:
                    # Ensure close is called to save changes and release file handle
                    if image_exif:
                        image_exif.close()
                    logging.debug(f"Released lock after saving {os.path.basename(image_path)}")
            # *** LOCK RELEASED ***

        except Exception as e:
            logging.error(f"Unexpected error in save metadata thread for {image_path}: {e}")
            self.update_status_js(f"Unexpected error saving: {e}", "error")

    def run_auto_keyword_single(self, image_path):
         """Runs AI keywording on the currently selected image."""
         if not image_path or not os.path.exists(image_path):
             self.update_status_js("Error: Please select a valid image first.", "error")
             return

         self.update_status_js(f"Starting AI keywording for {os.path.basename(image_path)}...", "info")
         # Run in background thread
         threading.Thread(target=self._auto_keyword_thread, args=(image_path,), daemon=True).start()

    def _auto_keyword_thread(self, image_path):
        """Background thread for AI keywording, checking save status."""
        import time
        import traceback
        import platform
        import sys
        import os.path

        # Function to write to a fallback log file in case everything else fails
        def write_emergency_log(message):
            try:
                log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
                os.makedirs(log_dir, exist_ok=True)
                log_file = os.path.join(log_dir, "emergency_gallery_log.txt")
                with open(log_file, "a", encoding="utf-8") as f:
                    f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
            except Exception as e:
                print(f"Failed to write to emergency log: {e}")
                # Last resort - try to write to current directory
                try:
                    with open("emergency_log.txt", "a") as f:
                        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
                except Exception:
                    pass  # Nothing more we can do

        # Re-enable button function placed here for clarity
        def re_enable_button():
             try:
                 window = self._get_window()
                 if window:
                     try: window.evaluate_js("document.getElementById('auto-keyword-btn').disabled = false;")
                     except Exception as e:
                         print(f"JS Error re-enabling button: {e}")
                         write_emergency_log(f"JS Error re-enabling button: {e}")
             except Exception as btn_err:
                 print(f"Error in re_enable_button: {btn_err}")
                 write_emergency_log(f"Error in re_enable_button: {btn_err}")

        # Function to show error indicator and play error sound
        def show_error_indicator():
            try:
                window = self._get_window()
                if window:
                    try:
                        window.evaluate_js('showErrorIndicator()')
                        print("Gallery: Called JS showErrorIndicator()")
                    except Exception as e:
                        print(f"Error calling JS showErrorIndicator: {e}")
                        write_emergency_log(f"Error calling JS showErrorIndicator: {e}")
            except Exception as indicator_err:
                print(f"Error in show_error_indicator: {indicator_err}")
                write_emergency_log(f"Error in show_error_indicator: {indicator_err}")

        # Log system information at the start
        try:
            system_info = f"System: {platform.system()} {platform.release()}, Python: {sys.version}, Encoding: {sys.getdefaultencoding()}"
            write_emergency_log(f"=== System Information: {system_info} ===")
        except Exception as sys_err:
            write_emergency_log(f"Error getting system info: {sys_err}")

        # Helper functions to handle logging with or without error_logger
        def log_info_safe(message):
            try:
                print(message)
                try:
                    from Scripts.error_logger import log_info
                    log_info(message)
                except ImportError:
                    logging.info(message)
            except Exception as e:
                print(f"Error in log_info_safe: {e}")
                write_emergency_log(f"Error in log_info_safe: {e}")

        def log_error_safe(message, exception=None, file_path=None):
            try:
                print(message)
                if exception:
                    print(traceback.format_exc())
                try:
                    from Scripts.error_logger import log_error
                    log_error(message, exception, file_path)
                except ImportError:
                    logging.error(message)
            except Exception as e:
                print(f"Error in log_error_safe: {e}")
                write_emergency_log(f"Error in log_error_safe: {e}")

        def log_warning_safe(message, file_path=None):
            try:
                print(message)
                try:
                    from Scripts.error_logger import log_warning
                    log_warning(message, file_path)
                except ImportError:
                    logging.warning(message)
            except Exception as e:
                print(f"Error in log_warning_safe: {e}")
                write_emergency_log(f"Error in log_warning_safe: {e}")

        # Function to safely get the basename of a file path
        def safe_basename(path_str):
            try:
                return os.path.basename(path_str)
            except Exception:
                return "unknown_file"

        try:
            from Scripts.error_logger import log_system_info
            log_system_info()

            message = f"Starting AI keywording for single image: {image_path}"
            log_info_safe(message)
            write_emergency_log(message)

            file_basename = safe_basename(image_path)
            keywords, title, description = None, None, None # Initialize variables

            try:
                file_extension = os.path.splitext(image_path)[1].lower()
                # *** NOTE: The original code passed 'filetype' to ai_keywording, but the function
                # does not accept it. I am keeping custom_prompt empty as in the original code.
                # If you intended to use 'filetype', you'd need to modify the ai_keywording signature.
                custom_prompt = ''

                print(f"Calling ai_keywording for: {file_basename}")
                write_emergency_log(f"Calling ai_keywording for: {file_basename}")

                result_queue = queue.Queue()

                def process_keywording_thread():
                    try:
                        # --- MODIFICATION 1: Unpack all three return values ---
                        t, d, k = ai_keywording(image_path, custom_prompt, enhance_output=True, is_editorial=False, place="", country="", time_info="", progress_callback=None)
                        # --- MODIFICATION 2: Put all three values into the queue on success ---
                        result_queue.put((True, k, t, d))
                    except Exception as e:
                        # On failure, put a consistent tuple structure
                        result_queue.put((False, None, None, None, e))

                keywording_thread = threading.Thread(target=process_keywording_thread, daemon=True)
                keywording_thread.start()
                keywording_thread.join(timeout=120)

                if keywording_thread.is_alive():
                    error_msg = f"Timeout occurred while processing {file_basename}"
                    log_error_safe(error_msg, None, image_path)
                    write_emergency_log(f"CRITICAL ERROR: {error_msg}")
                    self.update_status_js("Error: AI keywording timed out", "error")
                    show_error_indicator()
                    re_enable_button()
                    return

                try:
                    result = result_queue.get(block=False)
                    if result[0]:  # Success
                        # --- MODIFICATION 3: Unpack all three results from the queue ---
                        keywords, title, description = result[1], result[2], result[3]
                    else:  # Error
                        exc = result[4]
                        error_msg = f"Error in keywording thread: {exc}"
                        log_error_safe(error_msg, exc, image_path)
                        write_emergency_log(f"CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
                        self.update_status_js(f"Error during AI keywording: {exc}", "error")
                        show_error_indicator()
                        re_enable_button()
                        return
                except queue.Empty:
                    error_msg = "Unexpected error: Thread completed but no result in queue"
                    log_error_safe(error_msg, None, image_path)
                    write_emergency_log(f"CRITICAL ERROR: {error_msg}")
                    self.update_status_js("Error: AI keywording failed", "error")
                    show_error_indicator()
                    re_enable_button()
                    return

            except Exception as inner_err:
                error_msg = f"Error preparing for AI keywording: {inner_err}"
                log_error_safe(error_msg, inner_err, image_path)
                write_emergency_log(f"CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
                self.update_status_js(f"Error preparing for AI keywording: {inner_err}", "error")
                show_error_indicator()
                re_enable_button()
                return

            if keywords and title and description:
                try:
                    write_emergency_log(f"AI Keywording successful for {file_basename}")

                    # --- MODIFICATION 4: Enhanced logging with description preview ---
                    try:
                        title_preview = title[:30] + "..." if len(title) > 30 else title
                        desc_preview = description[:40] + "..." if len(description) > 40 else description
                        keywords_count = len(keywords)
                        write_emergency_log(f"Title: '{title_preview}', Desc: '{desc_preview}', Keywords: {keywords_count}")
                    except Exception as preview_err:
                        write_emergency_log(f"Error creating preview: {preview_err}")

                    update_success = False
                    if self.pyexiv2_lock.acquire(timeout=30):
                        try:
                            write_emergency_log(f"Acquired lock for update_exif on {file_basename}")
                            # --- MODIFICATION 5: Pass the description to update_exif ---
                            update_success = update_exif(image_path, title, description, keywords)
                            write_emergency_log(f"update_exif returned: {update_success}")
                        except Exception as ue_err:
                            error_msg = f"Error calling update_exif for {file_basename}: {ue_err}"
                            log_error_safe(error_msg, ue_err, image_path)
                            write_emergency_log(f"CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
                            self.update_status_js(f"Error applying AI keywords: {ue_err}", "error")
                            show_error_indicator()
                        finally:
                            self.pyexiv2_lock.release()
                            write_emergency_log(f"Released lock for {file_basename}")
                    else:
                        error_msg = f"Timeout acquiring metadata lock for {file_basename}"
                        log_error_safe(error_msg, None, image_path)
                        write_emergency_log(f"CRITICAL ERROR: {error_msg}")
                        self.update_status_js("Error: Metadata update timed out", "error")
                        show_error_indicator()
                        re_enable_button()
                        return

                    if update_success:
                        message = f"AI Keywords saved successfully for {file_basename}"
                        log_info_safe(message)
                        write_emergency_log(message)
                        self.update_status_js(message, "success")
                        self.handle_image_selection(image_path)
                    else:
                        error_msg = f"AI Keywording completed, but failed to save metadata for {file_basename}"
                        log_error_safe(error_msg, None, image_path)
                        write_emergency_log(f"ERROR: {error_msg}")
                        self.update_status_js(error_msg, "error")
                        show_error_indicator()
                        re_enable_button()
                except Exception as process_err:
                    error_msg = f"Error processing successful keywording results: {process_err}"
                    log_error_safe(error_msg, process_err, image_path)
                    write_emergency_log(f"CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
                    self.update_status_js(f"Error processing results: {process_err}", "error")
                    show_error_indicator()
                    re_enable_button()
            else:
                warning_msg = f"AI keywording returned no data for {file_basename}"
                log_warning_safe(warning_msg, image_path)
                write_emergency_log(f"WARNING: {warning_msg}")
                self.update_status_js(f"AI Keywording failed or produced no results for {file_basename}.", "warning")
                show_error_indicator()
                re_enable_button()

        except Exception as e:
            file_basename = safe_basename(image_path)
            error_msg = f"Error in AI keywording thread for {file_basename}: {e}"
            log_error_safe(error_msg, e, image_path)
            write_emergency_log(f"CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
            self.update_status_js(f"Error during AI keywording: {e}", "error")
            show_error_indicator()
            re_enable_button()

        write_emergency_log(f"Completed _auto_keyword_thread for {safe_basename(image_path)}")

    def launch_preview_picker(self, image_path):
        """Finds the corresponding video and launches the preview picker app."""
        self.update_status_js(f"Verifying video for {os.path.basename(image_path)}...", "info")
        # Re-calculate video path for robustness right before launch
        base_name, img_ext = os.path.splitext(os.path.basename(image_path))
        dir_name = os.path.dirname(image_path)
        video_extensions = ('.mp4', '.mov', '.avi', '.wmv') # Keep consistent
        matching_video_path = None
        for ext in video_extensions:
            potential_video = os.path.join(dir_name, base_name + ext)
            if os.path.exists(potential_video):
                # Final check: Can OpenCV open it?
                cap = cv2.VideoCapture(potential_video)
                if cap.isOpened():
                    matching_video_path = potential_video
                    cap.release()
                    break
                cap.release() # Release even if failed

        if not matching_video_path:
            self.update_status_js(f"Error: Could not find or open matching video for {os.path.basename(image_path)}", "error")
            # Re-enable button in gallery if launch fails? Maybe not necessary if called from API thread.
            return

        try:
            # Ensure open_app can handle args (modify config.py if needed)
            # It should look something like: open_app(script_name, args=[])
            # Make sure 'preview_picker.py' is in the right location relative to gallery.py or packaged correctly.
            open_app("preview_picker.py", [image_path, matching_video_path])
            self.update_status_js(f"Preview Picker launched for {os.path.basename(image_path)}.", "info")
        except NameError:
             logging.error("`open_app` function is not defined or imported correctly.")
             self.update_status_js("Error: Configuration problem preventing app launch.", "error")
        except FileNotFoundError:
             logging.error("preview_picker.py script not found.")
             self.update_status_js("Error: Preview Picker application file not found.", "error")
        except Exception as e:
            logging.error(f"Error launching Preview Picker: {e}", exc_info=True)
            self.update_status_js(f"Error launching Preview Picker: {e}", "error")

# --- Main Function ---
def main():
    global _window
    app_logic = ImageGalleryAppLogic() # Create instance of the logic class

    # --- Create Pywebview Window ---
    html_file = get_asset_path('gallery.html') # Path to HTML file inside 'web'
    icon_path = get_asset_path('../logo.ico') # Icon relative to base path, NOT web subfolder

    if not os.path.exists(html_file):
       print(f"ERROR: HTML file not found at {html_file}")
       # Basic alert if possible
       try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Could not find required file: gallery.html</p></body></html>', width=400, height=200); webview.start();
       except: pass
       sys.exit(1)

    if not os.path.exists(icon_path):
        print(f"Warning: Icon file not found at {icon_path}. Using default.")
        icon_path = None

    print("Creating window...")
    _window = webview.create_window(
        'Gallery v1.0', # Window Title
        url=html_file,              # Use the HTML file
        js_api=app_logic.api,       # Expose the API instance
        width=1200,                 # Match original Tkinter size intent
        height=750,                 # Adjusted height
        resizable=True,             # Allow resizing
        confirm_close=False,         # Ask before closing
        background_color='#f0f0f0',
        # text_select=True, # Allow text selection (useful for copy/paste)
    )

    # Set window icon if available (pass during creation if supported, otherwise ignore)
    # Current pywebview versions often handle this in create_window if icon path is valid

    print("Starting Pywebview event loop...")
    # debug=True opens developer console (VERY useful)
    webview.start(debug=False)
    print("Pywebview finished.")
    _window.maximize()

if __name__ == "__main__":
    main()