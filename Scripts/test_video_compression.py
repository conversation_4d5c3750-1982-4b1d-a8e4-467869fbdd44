#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test Video Compression

This script demonstrates the video compression functionality for AI processing.
It shows before/after file sizes and compression ratios.
"""

import os
import sys
import time
from video_compressor import VideoCompressor, compress_video_for_ai


def format_file_size(size_bytes):
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def test_video_compression(video_path):
    """Test video compression with detailed output."""
    if not os.path.exists(video_path):
        print(f"Error: Video file not found: {video_path}")
        return False
    
    print("=" * 60)
    print("VIDEO COMPRESSION TEST")
    print("=" * 60)
    print(f"Input video: {os.path.basename(video_path)}")
    
    # Get original file info
    original_size = os.path.getsize(video_path)
    print(f"Original size: {format_file_size(original_size)}")
    
    # Get video info
    try:
        compressor = VideoCompressor()
        width, height, fps, frame_count = compressor.get_video_info(video_path)
        duration = frame_count / fps if fps > 0 else 0
        
        print(f"Original dimensions: {width}x{height}")
        print(f"Frame rate: {fps:.1f} FPS")
        print(f"Duration: {duration:.1f} seconds")
        print(f"Frame count: {frame_count}")
        
    except Exception as e:
        print(f"Error getting video info: {e}")
        return False
    
    print("\nCompressing video for AI processing...")
    print("-" * 40)
    
    # Compress video
    start_time = time.time()
    try:
        compressed_path = compress_video_for_ai(video_path)
        compression_time = time.time() - start_time
        
        # Get compressed file info
        compressed_size = os.path.getsize(compressed_path)
        compression_ratio = compressed_size / original_size if original_size > 0 else 0
        
        # Get compressed video info
        comp_width, comp_height, comp_fps, comp_frame_count = compressor.get_video_info(compressed_path)
        comp_duration = comp_frame_count / comp_fps if comp_fps > 0 else 0
        
        print(f"✓ Compression completed in {compression_time:.1f} seconds")
        print(f"Compressed file: {os.path.basename(compressed_path)}")
        print(f"Compressed size: {format_file_size(compressed_size)}")
        print(f"Compression ratio: {compression_ratio:.1%}")
        print(f"Size reduction: {format_file_size(original_size - compressed_size)}")
        print(f"Compressed dimensions: {comp_width}x{comp_height}")
        print(f"Compressed FPS: {comp_fps:.1f}")
        print(f"Compressed duration: {comp_duration:.1f} seconds")
        
        # Calculate processing speed improvement estimate
        processing_speed_improvement = 1 / compression_ratio if compression_ratio > 0 else 1
        print(f"Estimated AI processing speed improvement: {processing_speed_improvement:.1f}x faster")
        
        # Show temp file location
        print(f"\nCompressed file location: {compressed_path}")
        print("(This temporary file will be automatically cleaned up after AI processing)")
        
        return True
        
    except Exception as e:
        print(f"✗ Compression failed: {e}")
        return False


def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python test_video_compression.py <video_file_path>")
        print("\nExample:")
        print("  python test_video_compression.py C:\\path\\to\\your\\video.mp4")
        print("  python test_video_compression.py /path/to/your/video.mov")
        return 1
    
    video_path = sys.argv[1]
    
    # Test compression
    success = test_video_compression(video_path)
    
    if success:
        print("\n" + "=" * 60)
        print("✓ Video compression test completed successfully!")
        print("Your AI keywording will now be much faster with compressed videos.")
        return 0
    else:
        print("\n" + "=" * 60)
        print("✗ Video compression test failed.")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 