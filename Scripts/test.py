import pyexiv2
import pyexiv2
import os
import shutil
import sys
import time

# --- Configuration ---
BASE_DIR = r"C:\Users\<USER>\Desktop\StockTools"
IMAGE_FILES_TO_TEST_ORIGINALS = [
    os.path.join(BASE_DIR, "UshuaiaSony001.jpg"),
    # Add more image paths here that you want to include in the loop test
    # For example, if you have another image that sometimes works:
    # os.path.join(BASE_DIR, "AnotherImage.jpg"),
    # To simulate processing the same file multiple times, just list it multiple times
    # or create multiple copies.
]
NUM_ITERATIONS_PER_FILE = 5 # How many times to try processing each file in the list
TEST_AREA_DIR = os.path.join(BASE_DIR, "pyexiv2_loop_test_area")

# --- Test Data ---
TEST_TITLE_BASE = "Loop Test Title"
TEST_KEYWORDS_BASE = ["loop_key1", "loop_key2", "loop_key3"]

# --- Helper Functions ---
def setup_test_area():
    if os.path.exists(TEST_AREA_DIR):
        print(f"INFO: Removing existing test area: {TEST_AREA_DIR}", flush=True)
        shutil.rmtree(TEST_AREA_DIR)
    os.makedirs(TEST_AREA_DIR, exist_ok=True)
    print(f"INFO: Created test area: {TEST_AREA_DIR}", flush=True)

def prepare_test_copy(original_path, iteration, file_index):
    if not os.path.exists(original_path):
        print(f"WARNING: Original image not found, skipping: {original_path}", flush=True)
        return None
    
    original_basename = os.path.splitext(os.path.basename(original_path))[0]
    test_file_name = f"{original_basename}_loop_f{file_index}_i{iteration}.jpg"
    test_file_path = os.path.join(TEST_AREA_DIR, test_file_name)
    
    try:
        shutil.copy2(original_path, test_file_path)
        # print(f"  DEBUG: Copied '{original_path}' to '{test_file_path}'", flush=True)
        return test_file_path
    except Exception as e:
        print(f"ERROR: Could not copy '{original_path}' to '{test_file_path}': {e}", flush=True)
        return None

def attempt_single_metadata_write_loop(image_path, test_description, iteration_num):
    # print(f"--- Starting: {test_description} on {os.path.basename(image_path)} (Iter: {iteration_num}) ---", flush=True)
    img_obj = None
    current_step_succeeded = True 

    title_to_write = f"{TEST_TITLE_BASE} Iter {iteration_num}"
    keywords_to_write = TEST_KEYWORDS_BASE + [f"iter_{iteration_num}"]

    try:
        # print(f"  L_STEP 1: Opening pyexiv2.Image...", flush=True)
        img_obj = pyexiv2.Image(image_path)
        # print(f"    SUCCESS: Image opened.", flush=True)

        # EXIF
        exif_data = {
            'Exif.Image.XPTitle': title_to_write,
            'Exif.Image.XPSubject': title_to_write,
            'Exif.Image.XPKeywords': ';'.join(keywords_to_write)
        }
        # print(f"  L_STEP 2a: Modifying EXIF...", flush=True)
        img_obj.modify_exif(exif_data)
        # print(f"    SUCCESS: EXIF modified.", flush=True)
        
        # XMP
        xmp_data = {
            'Xmp.dc.title': {'lang': 'x-default', 'value': title_to_write},
            'Xmp.dc.subject': keywords_to_write, 
            'Xmp.dc.description': {'lang': 'x-default', 'value': title_to_write}
        }
        # print(f"  L_STEP 3a: Modifying XMP...", flush=True)
        img_obj.modify_xmp(xmp_data)
        # print(f"    SUCCESS: XMP modified.", flush=True)

    except Exception as e:
        print(f"ERROR in attempt_single_metadata_write_loop ({os.path.basename(image_path)}, Iter {iteration_num}): {type(e).__name__} - {e}", flush=True)
        current_step_succeeded = False
    finally:
        if img_obj:
            try:
                # print(f"  L_STEP 4: Closing image object...", flush=True)
                img_obj.close()
                # print(f"    SUCCESS: Image closed.", flush=True)
            except Exception as close_e:
                print(f"  ERROR closing image object ({os.path.basename(image_path)}, Iter {iteration_num}): {type(close_e).__name__} - {close_e}", flush=True)
                current_step_succeeded = False 
        
    if not current_step_succeeded:
        print(f"--- FAILED: {test_description} on {os.path.basename(image_path)} (Iter: {iteration_num}) ---", flush=True)
    # else:
        # print(f"--- PASSED: {test_description} on {os.path.basename(image_path)} (Iter: {iteration_num}) ---", flush=True)
    return current_step_succeeded

# --- Main Test Execution ---
if __name__ == "__main__":
    print("Starting pyexiv2 Loop Test Script...", flush=True)
    setup_test_area()

    if not IMAGE_FILES_TO_TEST_ORIGINALS or not any(os.path.exists(p) for p in IMAGE_FILES_TO_TEST_ORIGINALS):
        print("FATAL: No valid original image files found for testing. Check IMAGE_FILES_TO_TEST_ORIGINALS list.", flush=True)
        sys.exit(1)

    overall_success_count = 0
    overall_fail_count = 0
    total_operations = 0

    for file_idx, original_image_path in enumerate(IMAGE_FILES_TO_TEST_ORIGINALS):
        if not os.path.exists(original_image_path):
            print(f"SKIPPING: Original file not found: {original_image_path}", flush=True)
            continue
        
        print(f"\nProcessing original file: {os.path.basename(original_image_path)} for {NUM_ITERATIONS_PER_FILE} iterations", flush=True)
        for i in range(1, NUM_ITERATIONS_PER_FILE + 1):
            total_operations += 1
            print(f"  Starting Iteration {i}/{NUM_ITERATIONS_PER_FILE} for {os.path.basename(original_image_path)} (Overall op: {total_operations})", flush=True)
            
            test_file_copy_path = prepare_test_copy(original_image_path, i, file_idx)
            if not test_file_copy_path:
                print(f"    Failed to prepare copy for iteration {i}, skipping.", flush=True)
                overall_fail_count +=1
                continue

            # Brief pause, sometimes helps reveal timing-related resource issues
            # time.sleep(0.1) 

            success = attempt_single_metadata_write_loop(
                test_file_copy_path,
                f"File {file_idx+1}", # Test description
                i # Iteration number
            )
            if success:
                overall_success_count += 1
            else:
                overall_fail_count += 1
                print(f"    STOPPING further iterations for {os.path.basename(original_image_path)} after failure at iteration {i}.", flush=True)
                # To break out of inner loop for this file:
                # break 
            
            # If you want to see if it crashes even after a failure, comment out the break above
            # And also uncomment the print for PASS for more verbosity

    print(f"\n--- Loop Test Summary ---", flush=True)
    print(f"Total operations attempted: {total_operations}", flush=True)
    print(f"Successful operations: {overall_success_count}", flush=True)
    print(f"Failed operations (incl. silent crashes if count doesn't match): {overall_fail_count}", flush=True)
    print("Loop test completed.", flush=True)
