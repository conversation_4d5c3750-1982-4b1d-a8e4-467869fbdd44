#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Video Compressor Utility

This module provides fast CPU-based video compression to reduce file size
and processing time for AI keywording. Videos are compressed to 480p with
optimized settings for speed and size reduction.
"""

import os
import cv2
import tempfile
import logging
from typing import Optional, Tuple


class VideoCompressor:
    """Fast video compressor optimized for AI processing."""

    def __init__(self, target_height: int = 480, quality_factor: float = 0.3):
        """
        Initialize the video compressor.

        Args:
            target_height (int): Target height for compression (width will be calculated to maintain aspect ratio)
            quality_factor (float): Quality factor (0.1-1.0, lower = smaller file, faster processing)
        """
        self.target_height = target_height
        self.quality_factor = max(0.1, min(1.0, quality_factor))  # Clamp between 0.1 and 1.0

    def get_video_info(self, video_path: str) -> Tuple[int, int, float, int]:
        """
        Get basic video information.

        Args:
            video_path (str): Path to the video file

        Returns:
            Tuple[int, int, float, int]: (width, height, fps, frame_count)
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")

        try:
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            return width, height, fps, frame_count
        finally:
            cap.release()

    def calculate_target_dimensions(self, original_width: int, original_height: int) -> Tuple[int, int]:
        """
        Calculate target dimensions maintaining aspect ratio.

        Args:
            original_width (int): Original video width
            original_height (int): Original video height

        Returns:
            Tuple[int, int]: (target_width, target_height)
        """
        if original_height <= self.target_height:
            # Video is already smaller than target, return original dimensions
            return original_width, original_height

        # Calculate width maintaining aspect ratio
        aspect_ratio = original_width / original_height
        target_width = int(self.target_height * aspect_ratio)

        # Ensure dimensions are even (required for some codecs)
        target_width = target_width if target_width % 2 == 0 else target_width - 1
        target_height = self.target_height if self.target_height % 2 == 0 else self.target_height - 1

        return target_width, target_height

    def compress_video(self, input_path: str, output_path: Optional[str] = None,
                      max_duration_seconds: Optional[float] = 30.0) -> str:
        """
        Compress video to reduce file size and processing time.

        Args:
            input_path (str): Path to input video file
            output_path (Optional[str]): Path for output file. If None, creates temp file
            max_duration_seconds (Optional[float]): Maximum duration to keep (None for full video)

        Returns:
            str: Path to compressed video file
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input video file not found: {input_path}")

        # Get video information
        original_width, original_height, fps, frame_count = self.get_video_info(input_path)
        target_width, target_height = self.calculate_target_dimensions(original_width, original_height)

        # Calculate frame limit if max_duration is specified
        max_frames = None
        if max_duration_seconds and fps > 0:
            max_frames = int(max_duration_seconds * fps)
            max_frames = min(max_frames, frame_count)

        # Create output path if not provided
        if output_path is None:
            temp_dir = tempfile.gettempdir()
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = os.path.join(temp_dir, f"{base_name}_compressed_480p.mp4")

        logging.info(f"Compressing video: {os.path.basename(input_path)}")
        logging.info(f"Original: {original_width}x{original_height}, Target: {target_width}x{target_height}")
        if max_frames:
            logging.info(f"Limiting to {max_frames} frames ({max_duration_seconds}s)")

        # Open input video
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open input video: {input_path}")

        try:
            # Define codec and create VideoWriter
            # Using MP4V codec for better compatibility and speed
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')

            # Reduce FPS for smaller file size (max 15 fps for AI processing)
            target_fps = min(15.0, fps)

            out = cv2.VideoWriter(output_path, fourcc, target_fps, (target_width, target_height))
            if not out.isOpened():
                raise ValueError(f"Cannot create output video writer for: {output_path}")

            try:
                frame_skip = max(1, int(fps / target_fps)) if fps > target_fps else 1
                frames_written = 0
                frames_read = 0

                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break

                    frames_read += 1

                    # Skip frames to achieve target FPS
                    if frames_read % frame_skip != 0:
                        continue

                    # Stop if we've reached the maximum duration
                    if max_frames and frames_written >= max_frames:
                        break

                    # Resize frame
                    if (target_width, target_height) != (original_width, original_height):
                        frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_AREA)

                    # Apply quality reduction (optional - reduces file size further)
                    if self.quality_factor < 1.0:
                        # Reduce quality by applying slight blur and compression
                        kernel_size = max(1, int(3 * (1 - self.quality_factor)))
                        # Ensure kernel size is odd (required by OpenCV GaussianBlur)
                        if kernel_size % 2 == 0:
                            kernel_size += 1
                        if kernel_size > 1:
                            frame = cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)

                    out.write(frame)
                    frames_written += 1

                    # Progress logging every 100 frames
                    if frames_written % 100 == 0:
                        logging.debug(f"Processed {frames_written} frames...")

                logging.info(f"Compression complete: {frames_written} frames written to {os.path.basename(output_path)}")

            finally:
                out.release()

        finally:
            cap.release()

        # Verify output file was created and has reasonable size
        if not os.path.exists(output_path):
            raise RuntimeError(f"Failed to create compressed video: {output_path}")

        original_size = os.path.getsize(input_path)
        compressed_size = os.path.getsize(output_path)
        compression_ratio = compressed_size / original_size if original_size > 0 else 0

        logging.info(f"Compression ratio: {compression_ratio:.2%} "
                    f"({original_size // 1024}KB -> {compressed_size // 1024}KB)")

        return output_path

    def compress_for_ai_processing(self, input_path: str) -> str:
        """
        Compress video specifically optimized for AI processing.
        Uses aggressive compression settings for maximum speed and minimum file size.

        Args:
            input_path (str): Path to input video file

        Returns:
            str: Path to compressed temporary video file
        """
        # Use aggressive settings for AI processing
        original_quality = self.quality_factor
        self.quality_factor = 0.2  # Very aggressive compression

        try:
            # Limit to 20 seconds for AI processing (usually sufficient for analysis)
            compressed_path = self.compress_video(
                input_path=input_path,
                output_path=None,  # Use temp file
                max_duration_seconds=20.0
            )
            return compressed_path
        finally:
            # Restore original quality setting
            self.quality_factor = original_quality


def compress_video_for_ai(video_path: str, target_height: int = 480) -> str:
    """
    Convenience function to compress a video for AI processing.

    Args:
        video_path (str): Path to the video file
        target_height (int): Target height for compression

    Returns:
        str: Path to compressed temporary video file
    """
    compressor = VideoCompressor(target_height=target_height, quality_factor=0.2)
    return compressor.compress_for_ai_processing(video_path)


if __name__ == "__main__":
    # Example usage
    import sys

    if len(sys.argv) != 2:
        print("Usage: python video_compressor.py <input_video_path>")
        sys.exit(1)

    input_video = sys.argv[1]

    try:
        compressed_video = compress_video_for_ai(input_video)
        print(f"Compressed video saved to: {compressed_video}")

        # Show file sizes
        original_size = os.path.getsize(input_video)
        compressed_size = os.path.getsize(compressed_video)
        print(f"Original size: {original_size // 1024}KB")
        print(f"Compressed size: {compressed_size // 1024}KB")
        print(f"Compression ratio: {compressed_size / original_size:.2%}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)