"""
Test script for the safe_exif module.
This script tests the safe_exif module with various inputs to ensure it works correctly.
"""
import os
import sys
import traceback
import time

def main():
    """Main function to test the safe_exif module."""
    print("Testing safe_exif module...")
    
    # Import the safe_exif module
    try:
        from Scripts.safe_exif import safe_update_exif, write_emergency_log
        print("Successfully imported safe_exif module")
    except ImportError:
        print("Failed to import safe_exif module")
        return
    
    # Log system information
    try:
        from Scripts.safe_exif import log_system_info
        log_system_info()
        print("Successfully logged system information")
    except Exception as e:
        print(f"Failed to log system information: {e}")
    
    # Test with a sample image
    test_image = input("Enter path to a test image: ")
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    # Test with various inputs
    test_cases = [
        {
            "name": "Normal case",
            "keywords": ["test", "keyword", "normal"],
            "title": "Test Title"
        },
        {
            "name": "Empty keywords",
            "keywords": [],
            "title": "Test Title"
        },
        {
            "name": "Empty title",
            "keywords": ["test", "keyword"],
            "title": ""
        },
        {
            "name": "Special characters",
            "keywords": ["test", "keyword", "special", "характеры", "中文", "日本語"],
            "title": "Test Title with special characters: характеры 中文 日本語"
        },
        {
            "name": "Very long title",
            "keywords": ["test", "keyword"],
            "title": "A" * 1000
        },
        {
            "name": "Very long keywords",
            "keywords": ["A" * 1000, "B" * 1000],
            "title": "Test Title"
        }
    ]
    
    # Run test cases
    for test_case in test_cases:
        print(f"\nRunning test case: {test_case['name']}")
        try:
            result = safe_update_exif(test_image, test_case["keywords"], test_case["title"])
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")
            print(traceback.format_exc())
    
    print("\nAll tests completed")

if __name__ == "__main__":
    main()
