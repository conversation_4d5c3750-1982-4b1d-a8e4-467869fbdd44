#!/usr/bin/env python3
"""
Simple HTTP server for serving web files
This can be used as a fallback if pywebview is not working properly
"""

import http.server
import socketserver
import os
import sys
import threading
import webbrowser
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Add CORS headers to allow cross-origin requests
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def start_server(port=7555, directory=None):
    """Start a simple HTTP server on the specified port"""
    
    if directory is None:
        # Default to the Scripts directory
        directory = os.path.dirname(os.path.abspath(__file__))
    
    os.chdir(directory)
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"Serving files from {directory}")
            print(f"Server running at http://127.0.0.1:{port}/")
            print(f"Comparison window available at: http://127.0.0.1:{port}/web/comparison.html")
            print("Press Ctrl+C to stop the server")
            
            # Start server in a separate thread
            server_thread = threading.Thread(target=httpd.serve_forever, daemon=True)
            server_thread.start()
            
            return httpd
            
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"Error: Port {port} is already in use")
            print("Try using a different port or stop the process using that port")
        else:
            print(f"Error starting server: {e}")
        return None

def open_comparison_in_browser(port=7555):
    """Open the comparison window in the default web browser"""
    url = f"http://127.0.0.1:{port}/web/comparison.html"
    print(f"Opening {url} in browser...")
    webbrowser.open(url)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Start a simple HTTP server for web files')
    parser.add_argument('--port', type=int, default=7555, help='Port to run the server on (default: 7555)')
    parser.add_argument('--directory', type=str, help='Directory to serve files from (default: current script directory)')
    parser.add_argument('--open-browser', action='store_true', help='Open comparison window in browser after starting server')
    
    args = parser.parse_args()
    
    httpd = start_server(args.port, args.directory)
    
    if httpd and args.open_browser:
        # Wait a moment for server to start
        import time
        time.sleep(1)
        open_comparison_in_browser(args.port)
    
    if httpd:
        try:
            # Keep the main thread alive
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nShutting down server...")
            httpd.shutdown()
            httpd.server_close()
