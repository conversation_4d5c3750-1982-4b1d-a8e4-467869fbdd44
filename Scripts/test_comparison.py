#!/usr/bin/env python3
"""
Test script for the metadata comparison feature
"""

import os
import sys

# Add the Scripts directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_comparison_window():
    """Test the comparison window with sample data"""
    
    # Sample metadata variants
    test_metadata = [
        {
            'file_path': 'test_image1.jpg',
            'filename': 'test_image1.jpg',
            'initial': {
                'title': 'A beautiful sunset over mountains',
                'description': 'This image shows a stunning sunset with vibrant colors over mountain peaks. The sky is filled with orange and pink hues.',
                'keywords': ['sunset', 'mountains', 'landscape', 'nature', 'sky', 'orange', 'pink', 'evening', 'scenic', 'outdoor']
            },
            'refined': {
                'title': 'Sunset over mountain peaks with vibrant sky',
                'description': 'A dramatic sunset scene featuring mountain silhouettes against a colorful sky with orange and pink tones. Suitable for travel websites, nature photography collections, and landscape art.',
                'keywords': ['sunset', 'mountains', 'landscape', 'nature', 'sky', 'orange', 'pink', 'evening', 'outdoor', 'dramatic', 'silhouette', 'colorful', 'travel', 'photography', 'peaks']
            }
        },
        {
            'file_path': 'test_image2.jpg',
            'filename': 'test_image2.jpg',
            'initial': {
                'title': 'City street at night',
                'description': 'A busy city street illuminated by streetlights and neon signs at night.',
                'keywords': ['city', 'street', 'night', 'lights', 'urban', 'neon', 'busy', 'traffic', 'downtown']
            },
            'refined': {
                'title': 'Urban street scene with neon lights at night',
                'description': 'A vibrant urban street scene featuring illuminated storefronts and neon signage during nighttime hours. Perfect for city lifestyle themes, urban photography, and metropolitan concepts.',
                'keywords': ['city', 'street', 'night', 'lights', 'urban', 'neon', 'traffic', 'downtown', 'vibrant', 'storefronts', 'signage', 'nighttime', 'lifestyle', 'metropolitan', 'illuminated']
            }
        }
    ]
    
    try:
        from comparison_window import run_comparison_window
        print("Starting comparison window test...")
        run_comparison_window(test_metadata)
    except ImportError as e:
        print(f"Error importing comparison window: {e}")
        return False
    except Exception as e:
        print(f"Error running comparison window: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("Testing metadata comparison feature...")
    success = test_comparison_window()
    if success:
        print("Test completed successfully!")
    else:
        print("Test failed!")
