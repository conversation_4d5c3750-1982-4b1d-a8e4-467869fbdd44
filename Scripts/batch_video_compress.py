#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Batch Video Compression

This script compresses multiple video files in a directory to 480p for faster AI processing.
Useful for preparing large batches of videos before running AI keywording.
"""

import os
import sys
import time
import glob
from pathlib import Path
from video_compressor import VideoCompressor


def format_file_size(size_bytes):
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def find_video_files(directory):
    """Find all video files in a directory."""
    video_extensions = ['*.mp4', '*.mov', '*.avi', '*.wmv', '*.mkv', '*.flv', '*.webm']
    video_files = []
    
    for ext in video_extensions:
        pattern = os.path.join(directory, f"**/{ext}")
        video_files.extend(glob.glob(pattern, recursive=True))
        # Also check for uppercase extensions
        pattern = os.path.join(directory, f"**/{ext.upper()}")
        video_files.extend(glob.glob(pattern, recursive=True))
    
    return sorted(list(set(video_files)))  # Remove duplicates and sort


def compress_videos_batch(input_directory, output_directory=None, target_height=480, quality_factor=0.3):
    """
    Compress all videos in a directory.
    
    Args:
        input_directory (str): Directory containing videos to compress
        output_directory (str): Directory to save compressed videos (None = same as input)
        target_height (int): Target height for compression
        quality_factor (float): Quality factor (0.1-1.0)
    """
    if not os.path.exists(input_directory):
        print(f"Error: Input directory not found: {input_directory}")
        return False
    
    # Find all video files
    video_files = find_video_files(input_directory)
    
    if not video_files:
        print(f"No video files found in: {input_directory}")
        return False
    
    print(f"Found {len(video_files)} video files to compress")
    print("=" * 60)
    
    # Create output directory if specified
    if output_directory:
        os.makedirs(output_directory, exist_ok=True)
        print(f"Output directory: {output_directory}")
    else:
        print("Output: Same directory as input (with '_compressed_480p' suffix)")
    
    print(f"Target resolution: {target_height}p")
    print(f"Quality factor: {quality_factor}")
    print("=" * 60)
    
    # Initialize compressor
    compressor = VideoCompressor(target_height=target_height, quality_factor=quality_factor)
    
    # Track statistics
    total_original_size = 0
    total_compressed_size = 0
    successful_compressions = 0
    failed_compressions = 0
    start_time = time.time()
    
    # Process each video
    for i, video_path in enumerate(video_files, 1):
        print(f"\n[{i}/{len(video_files)}] Processing: {os.path.basename(video_path)}")
        
        try:
            # Get original file size
            original_size = os.path.getsize(video_path)
            total_original_size += original_size
            
            # Determine output path
            if output_directory:
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_path = os.path.join(output_directory, f"{base_name}_compressed_480p.mp4")
            else:
                # Same directory with suffix
                base_path = os.path.splitext(video_path)[0]
                output_path = f"{base_path}_compressed_480p.mp4"
            
            # Skip if output already exists
            if os.path.exists(output_path):
                print(f"  ⚠ Skipping - output already exists: {os.path.basename(output_path)}")
                continue
            
            # Compress video
            file_start_time = time.time()
            compressed_path = compressor.compress_video(
                input_path=video_path,
                output_path=output_path,
                max_duration_seconds=None  # Keep full duration for batch processing
            )
            
            compression_time = time.time() - file_start_time
            compressed_size = os.path.getsize(compressed_path)
            total_compressed_size += compressed_size
            
            compression_ratio = compressed_size / original_size if original_size > 0 else 0
            
            print(f"  ✓ Completed in {compression_time:.1f}s")
            print(f"    Size: {format_file_size(original_size)} → {format_file_size(compressed_size)} ({compression_ratio:.1%})")
            
            successful_compressions += 1
            
        except Exception as e:
            print(f"  ✗ Failed: {e}")
            failed_compressions += 1
    
    # Print summary
    total_time = time.time() - start_time
    overall_compression_ratio = total_compressed_size / total_original_size if total_original_size > 0 else 0
    
    print("\n" + "=" * 60)
    print("BATCH COMPRESSION SUMMARY")
    print("=" * 60)
    print(f"Total files processed: {len(video_files)}")
    print(f"Successful compressions: {successful_compressions}")
    print(f"Failed compressions: {failed_compressions}")
    print(f"Total time: {total_time / 60:.1f} minutes")
    print(f"Average time per file: {total_time / len(video_files):.1f} seconds")
    print(f"Total original size: {format_file_size(total_original_size)}")
    print(f"Total compressed size: {format_file_size(total_compressed_size)}")
    print(f"Overall compression ratio: {overall_compression_ratio:.1%}")
    print(f"Total space saved: {format_file_size(total_original_size - total_compressed_size)}")
    
    if successful_compressions > 0:
        processing_speed_improvement = 1 / overall_compression_ratio if overall_compression_ratio > 0 else 1
        print(f"Estimated AI processing speed improvement: {processing_speed_improvement:.1f}x faster")
    
    return successful_compressions > 0


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python batch_video_compress.py <input_directory> [output_directory] [target_height] [quality_factor]")
        print("\nArguments:")
        print("  input_directory   - Directory containing videos to compress")
        print("  output_directory  - Directory to save compressed videos (optional, default: same as input)")
        print("  target_height     - Target height in pixels (optional, default: 480)")
        print("  quality_factor    - Quality factor 0.1-1.0 (optional, default: 0.3)")
        print("\nExamples:")
        print("  python batch_video_compress.py C:\\Videos")
        print("  python batch_video_compress.py C:\\Videos C:\\CompressedVideos")
        print("  python batch_video_compress.py C:\\Videos C:\\CompressedVideos 720 0.5")
        return 1
    
    input_directory = sys.argv[1]
    output_directory = sys.argv[2] if len(sys.argv) > 2 else None
    target_height = int(sys.argv[3]) if len(sys.argv) > 3 else 480
    quality_factor = float(sys.argv[4]) if len(sys.argv) > 4 else 0.3
    
    # Validate arguments
    if not os.path.exists(input_directory):
        print(f"Error: Input directory not found: {input_directory}")
        return 1
    
    if target_height < 240 or target_height > 2160:
        print(f"Error: Target height must be between 240 and 2160 pixels")
        return 1
    
    if quality_factor < 0.1 or quality_factor > 1.0:
        print(f"Error: Quality factor must be between 0.1 and 1.0")
        return 1
    
    # Run batch compression
    success = compress_videos_batch(
        input_directory=input_directory,
        output_directory=output_directory,
        target_height=target_height,
        quality_factor=quality_factor
    )
    
    if success:
        print("\n✓ Batch compression completed successfully!")
        return 0
    else:
        print("\n✗ Batch compression failed or no files processed.")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 