import logging
import os
from typing import Optional, <PERSON>ple
import pyexiv2
import cv2
from contextlib import contextmanager

@contextmanager
def exif_handler(file_path: str) -> Optional[pyexiv2.Image]:
    """Context manager for safe EXIF handling."""
    image = None
    try:
        image = pyexiv2.Image(file_path)
        yield image
    except Exception as e:
        logging.error(f"EXIF error for {file_path}: {e}")
        yield None
    finally:
        if image:
            try:
                image.close()
            except Exception as e:
                logging.error(f"Error closing EXIF handler: {e}")

def eta(total, processed):
    remaining = total - processed
    estimated_time = remaining * 10  # Adjust as needed per file
    if estimated_time > 80:
        return f"{int(estimated_time / 60)} min"
    return f"{int(estimated_time)} sec"

def update_exif(file_path: str, comment: str) -> bool:
    """Update image EXIF metadata with comment."""
    if not os.path.exists(file_path):
        logging.error(f"File not found: {file_path}")
        return False

    with exif_handler(file_path) as image:
        if not image:
            return False
        try:
            exif_data = {'Exif.Image.XPComment': str(comment)}
            image.modify_exif(exif_data)
            return True
        except Exception as e:
            logging.error(f"Failed to modify EXIF: {e}")
            return False

def read_comment(path: str) -> Optional[str]:
    """Read XPComment from image EXIF data."""
    with exif_handler(path) as img:
        if not img:
            return None
        try:
            raw_comment = img.read_exif().get('Exif.Image.XPComment', '')
            if isinstance(raw_comment, bytes):
                return raw_comment.decode('utf-16le', errors='replace')
            return str(raw_comment)
        except Exception as e:
            logging.error(f"Failed to read comment: {e}")
            return None

def mark_as_done(path: str, name: str) -> bool:
    """Mark item as done in EXIF comment."""
    if not os.path.exists(path):
        logging.error(f"File not found: {path}")
        return False

    current_comment = read_comment(path)
    if current_comment is None:
        return False

    new_comment = current_comment.replace(f"❌ {name}", f"✅ {name}")
    return update_exif(path, new_comment)

def check_for_matching_video(image_path: str) -> Tuple[bool, Optional[str]]:
    """
    Checks if an image file has a corresponding video file with the same name.

    Args:
        image_path (str): Path to the image file

    Returns:
        Tuple[bool, Optional[str]]: (has_matching_video, video_path)
            - has_matching_video: True if a matching video file exists
            - video_path: Path to the video file if found, None otherwise
    """
    # Skip check if the file is already a video
    file_extension = os.path.splitext(image_path)[1].lower()
    if file_extension in ('.mp4', '.mov', '.avi', '.wmv'):
        return False, None

    # Get the base name and directory of the image
    base_name, _ = os.path.splitext(os.path.basename(image_path))
    dir_name = os.path.dirname(image_path)

    # Define common video extensions
    video_extensions = ('.mp4', '.mov', '.avi', '.wmv')

    # Check for matching video files
    for ext in video_extensions:
        potential_video = os.path.join(dir_name, base_name + ext)
        if os.path.exists(potential_video):
            # Verify it's a valid video file using OpenCV
            try:
                cap = cv2.VideoCapture(potential_video)
                if cap.isOpened():
                    cap.release()
                    logging.info(f"Found matching video for {os.path.basename(image_path)}: {os.path.basename(potential_video)}")
                    return True, potential_video
                cap.release()
            except Exception as e:
                logging.warning(f"Error checking video file {potential_video}: {e}")

    return False, None