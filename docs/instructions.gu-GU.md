# How to use Tkinter Designer
___

## અનુક્રમણિકા

1. **પ્રારંભ કરી રહ્યા છીએ**
   1. પાયથોન ઇન્સ્ટોલ કરો
   2. ટીકીટર ડિઝાઇનર ઇન્સ્ટોલ કરો
   3. ફિગ્મા એકાઉન્ટ બનાવો

2. **તમારી ફિગ્મા ડિઝાઇન ફોર્મેટિંગ**
   1. સંદર્ભ
   2. એલિમેન્ટ ગાઇડ

3. **ટિંકટર ડિઝાઇનરનો ઉપયોગ કરીને**
   1. વ્યક્તિગત પ્રવેશ ટોકન
   2. ફાઇલ URL
   3. તમારા જનરેટેડ કોડની કસોટી કરો

4. **મુશ્કેલીનિવારણ**
<br> <br>

# પ્રારંભ <small> [[અનુક્રમણિકા]](#અનુક્રમણિકા)</small>

<a id="getting-stated-1"></a>

## 1. પાયથોન ઇન્સ્ટોલ કરો

ટિંકટર ડિઝાઇનરનો ઉપયોગ કરતા પહેલા, તમારે પાયથોન ઇન્સ્ટોલ કરવાની જરૂર છે.
- [અહીં Python ડાઉનલોડ્સ Page ની એક લિંક છે.](https://www.python.org/downloads)
- [વિવિધ operating સિસ્ટમો પર પાયથોન install કરવા માટે અહીં સહાયક માર્ગદર્શિકા છે.](https://wiki.python.org/moin/BeginnersGuide/Download)

- પછી આ માર્ગદર્શિકામાં, તમે Python(pip) માટે પેકેજ ઇન્સ્ટોલરનો ઉપયોગ કરશો, જેના માટે તમારે તમારી સિસ્ટમ પાથમાં પાયથોન ઉમેરવાની જરૂર પડી શકે છે. *

___
<br>

<a id="getting-started-2"> </a>

## 2. ટિંકટર ડિઝાઇનર ઇન્સ્ટોલ કરો

એકવાર તમે પાયથોન ઇન્સ્ટોલ કર્યા પછી, તમે Tkinter ડિઝાઇનર [Official Repository](https://github.com/ParthJadhav/Tkinter-Designer) પરથી ડાઉનલોડ કરી શકો છો.

જમણી બાજુની સાઇડબારમાં, નવીનતમ પ્રકાશનને ક્લિક કરો અને **Assets** હેઠળ, `tkinter_designer.exe` પસંદ કરો. એક્ઝેક્યુટેબલ ડાઉનલોડ થયા પછી, તમે પ્રોગ્રામ ચલાવવા માટે તૈયાર છો!

સ્રોત કોડમાંથી ટિંક્ટર ડિઝાઇનર ચલાવવા માટે, નીચે આપેલા સૂચનોને અનુસરો.

1. સ્રોત ફાઇલોને ટિંકટર ડિઝાઇનર માટે જાતે ડાઉનલોડ કરીને અથવા ઉપયોગ કરીને ડાઉનલોડ કરો git :-

`git clone https://github.com/ParthJadhav/Tkinter-Designer.git`

2. તમારી કાર્યકારી ડિરેક્ટરીને ટીકીટર ડિઝાઇનર પર બદલો

`cd Tkinter-Designer`

3. ચલાવીને જરૂરી પરાધીનતા સ્થાપિત કરો

`pip install -r requirements.txt` ચલાવીને આવશ્યક અવલંબન સ્થાપિત કરો

- જે સ્થિતિમાં PIP કામ કરતું નથી, નીચેના આદેશો પણ અજમાવો:
  - `pip3 install -r requirements.txt`
  - `python -m pip install -r requirements.txt`
  - `python3 -m pip install -r requirements.txt`
- જો આ હજી પણ કામ કરતું નથી, તો ખાતરી કરો કે પાયથોન PATH માં ઉમેરવામાં આવ્યો છે.
  
આ બધી આવશ્યકતાઓ અને ટિંકટર ડિઝાઇનર ઇન્સ્ટોલ કરશે. તમે ટિંકટર ડિઝાઇનરનો ઉપયોગ કરો તે પહેલાં તમારે નીચેની સૂચનાઓ સાથે ફિગ્મા ફાઇલ બનાવવાની જરૂર છે.

જો તમે પહેલેથી જ ફાઇલ બનાવી છે, તો પછી [** ટીકીટર ડિઝાઇનરનો ઉપયોગ કરીને **] (# ઉપયોગ કરીને-ટિંકિટર-ડિઝાઇનર) વિભાગ પર જાઓ.

___
<br>

<a id="getting-started-3"> </a>

## 3. ફિગ્મા એકાઉન્ટ બનાવો

1. વેબ બ્રાઉઝરમાં, [figma.com](https://www.figma.com/) પર નેવિગેટ કરો અને 'સાઇન અપ' ક્લિક કરો.
2. તમારી માહિતી દાખલ કરો, પછી તમારું ઇમેઇલ ચકાસો
3. નવી ફિગ્મા ડિઝાઇન ફાઇલ બનાવો
4. તમારી જીયુઆઈ બનાવવાનું પ્રારંભ કરો
   - આગલા વિભાગમાં ટિંકટર ડિઝાઇનર ઇનપુટ માટે આવશ્યક ફોર્મેટિંગ આવરી લે છે.
     - [નવા નિશાળીયા માટે ફિગ્મા ટ્યુટોરિયલ શ્રેણી અહીં છે.](https://www.youtube.com/watch?v=Cx2dkpBxst8&list=PLXDU_eVOJTx7QHLShNqIXL1Cgbxj7HlN4)
     - [અહીં ફિગ્મા યુટ્યુબ ચેનલની officialફિશિયલ ચેનલ છે.](https://www.youtube.com/c/Figmadesign/featured)
     - [અહીં ફિગ્મા સહાય કેન્દ્ર છે.](https://help.figma.com/hc/en-us)

<br> <br>

<a id="formatting-1"> </a>

# તમારી ફિગ્મા ડિઝાઇનને ફોર્મેટ કરી રહ્યું છે

## 1. સંદર્ભ

<br>

### નામકરણ મહત્વપૂર્ણ છે

| ફિગ્મા Element નામ | Tkinter |
| --- | --- |
| Button | Button |
| Line | Line |
| Rectangle | Rectangle |
| TextArea | Text Area |
| TextBox | Entry |
| Image | Canvas.Image() |

<br>

Tkinter ડિઝાઇનર દ્વારા જનરેટ થયેલ કોડ તમારી ફિગ્મા ડિઝાઇનના તત્વોના નામ પર આધારિત છે અને, જેમ કે, તમારે તે મુજબ તમારા તત્વોનું નામ લેવાની જરૂર છે. ફિગ્મામાં, તમારા તત્વોને સ્તર પેનલમાં ડબલ-ક્લિક કરીને નામ બદલો.

___
<br>

<a id="formatting-2"> </a>

## 2. એલિમેન્ટ માર્ગદર્શિકા

<br>

1. **પ્રથમ, એક ફ્રેમ બનાવો જે તમારી ટિંકટર વિંડો તરીકે કામ કરશે.**
<br> <br>

2. **છબીઓ ઉમેરવી**
    - છબીઓ આકાર અને / અથવા છબીઓનો ઉપયોગ કરીને બનાવી શકાય છે
    - જો તમે બહુવિધ આકારો / છબીઓનો ઉપયોગ કરો છો, તો તમારે બધાને પસંદ કરીને અને <kbd> CTRL / & # 8984 ને દબાવવાથી તેમને એક સાથે જૂથ બનાવવું જોઈએ; + G </kbd>
    - તે નામ પછી તત્વ અથવા જૂથને "છબી" તરીકે નામ આપો.
<br> <br>

*. **ટેક્સ્ટ (સામાન્ય ટેક્સ્ટ)**

- ટેક્સ્ટ ટૂલને સક્રિય કરવા માટે <kbd> T </kbd> કીનો ઉપયોગ કરો, પછી ઇચ્છિત લખાણ ઉમેરો
   ટિંકટર ડિઝાઇનરના ઉપયોગ માટે ટેક્સ્ટનું નામ બદલવું જરૂરી નથી
   આગલી લાઇન પર જવા માટે સ્પષ્ટ રીતે <kbd> Return </kbd> અથવા <kbd> Enter </kbd>કી દબાવો.
<br> <br>

- **એન્ટ્રી (સિંગલ લાઇન યુઝર ઇનપુટ)**
  - <kbd> R </kbd> સાથે લંબચોરસ ટૂલને સક્રિય કરો
  - તમારી પસંદગી પ્રમાણે લંબચોરસને સમાયોજિત કરો
  - ખાતરી કરો કે લંબચોરસનું નામ "TextBox" છે
<br> <br>

- **ટેક્સ્ટ ક્ષેત્ર (મલ્ટિ-લાઇન યુઝર ઇનપુટ)**
  - <kbd> R </kbd> સાથે લંબચોરસ ટૂલને સક્રિય કરો
  - તમારી પસંદગી પ્રમાણે લંબચોરસને સમાયોજિત કરો
  - ખાતરી કરો કે લંબચોરસનું નામ "TextArea" છે

6. **લંબચોરસ**
   <kbd> R </kbd> સાથે લંબચોરસ ટૂલને સક્રિય કરો
   - તમારી રુચિ પ્રમાણે લંબચોરસને સમાયોજિત કરો
   - ખાતરી કરો કે લંબચોરસનું નામ "Rectangle" રાખવામાં આવ્યું છે
<br> <br>

7. **સામાન્ય બટન**
   - તમારા GUI માં બટન તરીકે સેવા આપવા માટે લંબચોરસ ઉમેરો
     - વૈકલ્પિક: બટન માટે ટેક્સ્ટ ઉમેરો
   - બટન (લંબચોરસ) અને કોઈપણ લખાણ, પસંદ કરો પછી તેને <kbd> Ctrl/&#8984; + G</kbd> સાથે જૂથ બનાવો
   - જૂથનું નામ "Button"

#### જો તમને કોઈ મુશ્કેલીનો સામનો કરવો પડે તો [આ વિડિઓ](https://youtu.be/Qd-jJjduWeQ) નો સંદર્ભ લો

<br> <br>

8. **ગોળાકાર બટન**
   - તમારા GUI માં બટન તરીકે સેવા આપવા માટે લંબચોરસ ઉમેરો
     - વૈકલ્પિક: બટન માટે ટેક્સ્ટ ઉમેરો
   - લંબચોરસ પસંદ કરીને અને ખૂણે ત્રિજ્યાને જમણી બાજુથી ઉમેરીને તેને ગોળાકાર બનાવો. [તેના પર વધુ વાંચો](https://help.figma.com/hc/en-us/articles/360050986854-Adjust-corner-radius-and-smoothing)
   - તમારા બટનના સમાન કદ સાથે એક લંબચોરસ બનાવો. તેને ગોળાકાર ન બનાવો.
   પૃષ્ઠભૂમિ સાથે મેળ કરવા માટે લંબચોરસનો રંગ બદલો
   - હવે નવા બનાવેલા લંબચોરસને મુખ્ય બટન (લંબચોરસ) ની નીચે ખસેડો.
   - બટન, લંબચોરસ અને કોઈપણ વૈકલ્પિક ટેક્સ્ટ પસંદ કરો, પછી તેમને <kbd> Ctrl / &#8984; + G </kbd> સાથે જૂથ બનાવો
   - જૂથનું નામ "Button"

#### જો તમને કોઈ મુશ્કેલીનો સામનો કરવો પડે તો [આ વિડિઓ](https://youtu.be/Qd-jJjduWeQ) નો સંદર્ભ લો

<br> <br>

### ટિંકિટર ડિઝાઇનરને નીચેના પગલાઓ કરતા પહેલા ખોલો

<br>

<a id="used-1"> </a>

## 1. પર્સનલ એક્સેસ ટોકન

1. તમારા ફિગ્મા એકાઉન્ટમાં લ .ગ ઇન કરો
2. સેટિંગ્સ પર નેવિગેટ કરો
The. **એકાઉન્ટ** ટ tabબમાં, **વ્યક્તિગત પ્રવેશ ટોકન્સ** પર નીચે સ્ક્રોલ કરો
The. પ્રવેશ ફોર્મમાં તમારી toક્સેસ ટોકનનું નામ દાખલ કરો અને <kbd> Enter </kbd> દબાવો
5. તમારી Personal Access Token બનાવવામાં આવશે.
   - આ ટોકન Copy કરો અને તેને ક્યાંક સુરક્ષિત રાખો.
   - **તમને આ ટોકન copy કરવાની બીજી તક નહીં મળે.**
6. તમારી Private Access Token Tkinter ડિઝાઇનરમાં **Token Id** ફોર્મમાં પેસ્ટ કરો

___
<br>

<a id="used-2"> </a>

## 2. ફાઇલ URL

1. તમારી ફિગ્મા ડિઝાઇન ફાઇલમાં, ટોચની પટ્ટીમાં **Share** બટનને ક્લિક કરો, પછી **&#x1f517;** પર ક્લિક કરો; link Copy કરો
2. Tkinter ડિઝાઇનરમાં **File URL** ફોર્મમાં લિંક પેસ્ટ કરો

___
<br>

## ની મદદથી CLI

CLI નો ઉપયોગ એ પેકેજ ઇન્સ્ટોલ કરવા અને CLI ટૂલ ચલાવવા જેટલું સરળ છે.

તમે પેદા ફિગ્મા પર્સનલ Accessક્સેસ ટોકન દ્વારા $ YOUR_FIGMA_TOKEN ને બદલીને તમે પરીક્ષણ તરીકે નીચેનો આદેશ વાપરી શકો છો.

```bash
# Example data
$ python -m tkdesigner.cli https://www.figma.com/file/WVLnulVsI177tvnxSdqOUZ/Untitled?node-id=0%3A1 $YOUR_FIGMA_TOKEN -f

# ક્લાઈટનો ઉપયોગ કેવી રીતે કરવો તે વિશે વધુ જાણવા માટે --help ફ્લેગ પસાર કરો

python -m tkdesigner.cli --help

```

ડિફ defaultલ્ટ રૂપે, GUI કોડ build / gui.py પર લખવામાં આવશે.
જનરેટ કરેલી GUI, સીડી ચલાવવા માટે તમે તેને બનાવેલ ડિરેક્ટરીમાં (દા.ત. build / -) બનાવો અને તમે ચલાવો તે જ રીતે ચલાવો-ટિંકટર GUI.

```bash
cd build
$ python3 gui.py
```

## GUI નો ઉપયોગ કરીને

1. દ્વારા "Tkinter Designer GUI" ખોલો

```
cd Tkinter-Designer
cd gui
python3 gui.py
```

2. તમારી *Personal Access Token* ટીકીટર ડિઝાઇનરમાં **Token ID** ફોર્મમાં પેસ્ટ કરો
3. ટિંકિટર ડિઝાઇનરમાં **File URL** ફોર્મમાં લિંક પેસ્ટ કરો
4. ફાઇલ બ્રાઉઝર ખોલવા માટે **Select Path** ફોર્મને ક્લિક કરો
5. આઉટપુટ પાથ પસંદ કરો અને **ફોલ્ડર પસંદ કરો** ક્લિક કરો.
6. દબાવો **Generate**

ટિંકટર ડિઝાઇનરની આઉટપુટ ફાઇલો તમારી પસંદ કરેલી ડિરેક્ટરીમાં **build** નામના નવા ફોલ્ડરમાં મૂકવામાં આવશે. અભિનંદન, તમે હવે Tkinter ડીઝાઈનરનો ઉપયોગ કરીને તમારું Tkinter GUI બનાવ્યું છે!

<br> <br>

# મુશ્કેલીનિવારણ

- તત્વો દૃશ્યમાન નથી? ખોટી રીતે?
  - કૃપા કરીને ખાતરી કરો કે તમારી ફિગ્મા ફાઇલમાં તે તત્વો યોગ્ય નામવાળી છે.

- બટનની અકારણ ગ્રે પૃષ્ઠભૂમિ છે?
  - ખાતરી કરો કે તમે તમારા બટન તત્વની પાછળ એક લંબચોરસ ઉમેર્યો છે, અને તેનો ભરો રંગ પૃષ્ઠભૂમિ જેવો જ છે

- ખોટા તત્વો?
  - ખાતરી કરો કે તમે ફિગ્મામાં તમારા તત્વોનું નામ યોગ્ય રાખ્યું છે
    - જુઓ [તમારી ફિગ્મા ડિઝાઇન ફોર્મેટિંગ, અને સંપ્રદાય; 1](#formatting-1)

- વિંડો સ્ક્રીન કરતા મોટી છે?
  - ફિગ્મામાં તમારા તત્વોનું કદ ઘટાડવું

- ફાઇલો ઉત્પન્ન થતી નથી?
  ટિંકટર ડિઝાઇનરને ફરીથી પ્રારંભ કરો
  - Token અને URL ને બે વાર તપાસો
  - ખાતરી કરો કે તમારી ડિઝાઇનમાં ફ્રેમ છે

- કંઈક બીજું?
  - [અહીં GitHub પર સૂચિબદ્ધ નથી તેવા મુદ્દાઓની જાણ કરો](https://github.com/ParthJadhav/Tkinter-Designer/issues/new)
