<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Automate Tkinter GUI Creation</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>


## 💡 Introducción

Tkinter Designer fue creado para agilizar el desarrolo de GUIs en Python. Éste usa un ya conocido Software de Diseño[Figma](https://www.figma.com/) para crear bonitas GUIs de Tkinter en Python como un pedazo de pastel 🍰.

Tkinter Designer usa el API de Figma para analizar un archivo de diseño y crear el respectivo código y archivos necesarios para el GUI. Incluso el GUI de Tkinter Designer fue creado usando Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️  Ventajas de Tkinter Designer

1. Interfaces de tipo Arrastra y Suélta
2. Significativamente más rapido que crear código manualmente
3. Habilidad de crear interfaces más bonitas.

## ⚡️ Lee las instruciones aquí -> [Documentación](/docs/instructions.md)

Las instruciones contienen toda la informacion sobre la instalación y el uso de Tkinter Designer, junto con información para resolver los problemas y reportar problemas.

## 🦋 Dar apoyo a Tkinter Designer

Si tú o tu compañía se han beneficiado de Tkinter Designer, considera apoyar el desarrollo del projecto a través de donaciones. Ésto va a impulsar el desarrollo de Tkinter Designer! Es tan fácil como preparar un Cafe; Por favor, haz uno para mi ;)

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

## 🔵 Únete al servidor oficial de Discord de Tkinter Designer

Da click en el siguiente botón para unirte al servidor de discord y tomar parte en las discusiones.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

## 📐 Cómo funciona

La única cosa que el usuario debe de hacer es diseñar una interfaz con Figma, y luego pegar el URL de Figma junto con el Token de API en Tkinter Designer.

Tkinter Designer automaticamente generará todo el código e imágenes requeridas para crear la GUI en Tkinter.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 Ejemplos

Las posibilidades son infinitas con Tkinter Designer, pero aquí hay algunas GUIs que pueden ser perfectamente replicadas en Tkinter.

<sup>Las siguientes no son mis creaciones.</sup>

### HotinGo  [(Más información)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### Registration Page

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### Branding Page

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 Casos de Éxito

Si tu app fue creada con Tkinter Designer, hazmelo saber. Ésto será de ayuda para otros al ver más ejemplos! 
(Ve: [Contáctame](#-contact-me)) o usa las secciones [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell).

## 📄 Licencia
<!--- Si no estás seguro de que licencia abierta usar, mira https://choosealicense.com/--->

Tkinter Designer está bajo la licencia de BSD 3-Clause "New" o "Revised".  
[Ver Aquí.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Permisos | Restricciones | Condiciones
| --- | --- | ---
&check; Uso Comercial | &times; Responsabilidad | &#x1f6c8; Licencia y Notificación de Copyright
&check; Modificación   | &times; Garantía
&check; Distribución  
&check; Uso Privado

