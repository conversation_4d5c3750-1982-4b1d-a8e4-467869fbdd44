<div dir="rtl">

<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;" dir="rtl">إنشاء واجهات المستخدم Tkinter آليًا</h5>
</p>

<p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
</p>

<br>

___

## مقدمة

تم إنشاء Tkinter Designer لتسريع عملية تطوير واجهات المستخدم GUI في Python. يستخدم هذا المشروع برنامج التصميم المعروف [Figma](https://www.figma.com/) لتسهيل علمية تطوير واجهات المستخدم الجميلة GUI.

يستخدم المشروع Figma API لتحليل ملف تصميم وإنشاء الكود والملفات المطلوبة لواجهة المستخدم GUI. حتى واجهة المستخدم هذا المشروع تم إنشاؤها باستخدام Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️ مزايا Tkinter Designer

1. واجهات drag and drop
2. أسرع بكثير من تطوير الواجهات يديويًا
3. القدرة على إنشاء واجهات أكثر جمالاً

___

## ⚡️ تثبيت واستعمال Tkinter Designer

تحتوي التعليمات على كل المعلومات حول تثبيت واستعمال Tkinter Designer، بالإضافة إلى troubleshooting والإبلاغ عن المشاكل. يوجد أيضًا مقطع فيديو.

### [إقرأ التعليمات](/docs/instructions.ar-DZ.md)

## 🦋 دعم Tkinter Designer

الحياة بدون قهوة، كشيءٍ بلا شيء... آسف، لم أشرب أي قهوة بعد.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

___
<br>

## 🔵 إنضم للDiscord server الرسمي لTkinter Designer

إضغط على الزر أدناه للانضمام الى الdiscord server للمشاركة في النقاشات.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

___
<br>

## 📐 كيف يعمل

الشيء الوحيد الذي يحتاجه المستخدم هو تصميم الواجهة باستعمال Figma ثم نسخ URL الملف والtoken إلى Tkinter Designer.

سيقوم Tkinter Designer تلقائيًا بإنشاء الكود والصور المطلوبة لإنشاء واجهة المستخدم في Tkinter.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 أمثلة

لا حصر لما يمكنك فعله مع Tkinter Designer، ولكن ستجد أدناه بعض الواجهات التي يمكن نسخها بشكل مثالي إلى Tkinter.

<sup>الأمثلة الموالية ليست من إنشائي</sup>

### صفحة تسجيل

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### صفحة علامة تجارية

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### مسجل إطارات [(المزيد من المعلومات)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(المزيد من المعلومات)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 عرض

إذا تم إنشاء تطبيقك باستخدام Tkinter Designer، أخبرني بذلك. رؤية المزيد من الأمثلة سيكون مفيدًا لآخرين!

(أنظر إلى: [إتصل بي](#-contact-me)) أو استعمل قسم [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) في الDiscussions.

___
<br>

## 📝 إتصل بي

 إذا أردت الإتصال بي، يمكنك التواصل معي على <EMAIL>

___
<br>

## 📄 الرخصة
<!--- If you're not sure which open license to use see https://choosealicense.com/--->
<!-- إن كنت غير متأكد أي رخصة مفتوحة تستعملها،  أنظر إلى https://choosealicense.com/ -->

تم ترخيص Tkinter Designer بموجب الرخصة BSD 3-Clause "New" or "Revised"
[إنظر هنا.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| الإذن | القيود | الشروط
| --- | --- | ---
&check; الاستخدام التجاري | &times; المسؤولية القانونية | &#x1f6c8; إشعار الترخيص وحقوق النشر
&check; التعديل   | &times; الضمان
&check; التوزيع
&check; الاستخدام الشخصي

</div>
