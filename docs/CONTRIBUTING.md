# Welcome to Tkinter-Designer contributing guide

Thank you for investing your time in contributing to our project!

Read our [Code of Conduct](../CODE_OF_CONDUCT.md) to keep our community approachable and respectable.

In this guide you will get an overview of the contribution workflow from opening an issue, creating a PR, reviewing, and merging the PR.

## New contributor guide

To get an overview of the project, read the [README](../README.md). Here are some resources to help you get started with open source contributions:

- [Finding ways to contribute to open source on GitHub](https://docs.github.com/en/get-started/exploring-projects-on-github/finding-ways-to-contribute-to-open-source-on-github)
- [Set up Git](https://docs.github.com/en/get-started/quickstart/set-up-git)
- [GitHub flow](https://docs.github.com/en/get-started/quickstart/github-flow)
- [Collaborating with pull requests](https://docs.github.com/en/github/collaborating-with-pull-requests)

## Getting started

If you find something that can be improved, solved or changed you are welcome to do so. 

#### Create a new issue

If you spot a problem with the Tkinter-Designer, [search if an issue already exists](https://github.com/ParthJadhav/Tkinter-Designer/issues). If a related issue doesn't exist, you can open a new issue using a relevant [issue form](https://github.com/ParthJadhav/Tkinter-Designer/issues/new/choose).

#### Solve an issue

Scan through our [existing issues](https://github.com/ParthJadhav/Tkinter-Designer/issues) to find one that interests you. You can narrow down the search using `labels` as filters.

### Make Changes

Once you find the thing you want to work on.

1. Fork the repository.
2. Create a branch for your changes.
3. Clone and checkout that branch locally.
4. Install dependencies by `pip install -r requirements.txt`
5. Make sure it works as expected.
6. Make the changes.
7. Test the changes.
8. Commit and Push the changes.
9. Create a Pull Request.

### Pull Request

The created Pull request should have a brief description on why and how you made the changes.

### Your PR is merged

Congratulations :tada::tada: Thank you :sparkles:.

Once your PR is merged, your contributions will be publicly visible on the [Tkinter-Designer](https://github.com/ParthJadhav/Tkinter-Designer/).
