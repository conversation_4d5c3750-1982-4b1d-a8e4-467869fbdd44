<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h4 align="center" style="margin: 0 auto 0 auto;">Drag & Drop GUI Creator</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

<p align="center">
<a href="https://www.producthunt.com/posts/tkinter-designer?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-tkinter&#0045;designer" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=312995&theme=neutral" alt="Tkinter&#0032;Designer - No&#0045;code&#0032;solution&#0032;for&#0032;Python&#0032;GUI&#0039;s | Product Hunt" style="width: 250px; height: 54px;" width="250" height="54" /></a>
</p>
  <br>

___

## 💡 소개

Tkinter Designer는 Python에서 GUI 개발 프로세스를 가속화하기 위해 만들어졌습니다. 잘 알려진 디자인 소프트웨어 [Figma](https://www.figma.com/) 를 사용하여 Python에서 아름다운 Tkinter GUI를 만드는 것을 식은 죽 먹기 🥣로 만들었습니다.

Tkinter Designer는 Figma API를 사용하여 설계 파일을 분석하고 GUI에 필요한 코드와 파일을 만듭니다. 심지어 Tkinter Designer의 GUI도 Tkinter Designer를 사용하여 만듭니다.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## 📢 알립니다!
### 🎉 멀티 프레임이 지원됩니다! 🎉

이제 단일 설계 파일에 여러 프레임을 작성할 수 있으며 Tkinter Designer는 각 프레임에 대해 각각의 코드와 파일을 작성합니다. 이것은 Tkinter Designer에게 큰 한 걸음입니다. 여러분들이 무엇을 만드는지 보게 되어 정말 기쁩니다.

[Discord](https://discord.gg/QfE5jMXxJv) 에서 커뮤니티와 자유롭게 창작물을 공유할 수 있습니다.

버그가 발생하거나 제안사항이 있을 경우 이슈 [here](https://github.com/ParthJadhav/Tkinter-Designer)를 생성해주시기 바랍니다.
## ☄️  Tkinter Designer의 장점

1. 드래그 앤 드롭 인터페이스입니다.
2. 손으로 코드를 작성하는 것보다 훨씬 빠릅니다.
3. 더 멋진 인터페이스 제작할 수 있습니다.

## ⚡️ 여기에서 사용 설명서를 보실 수 있습니다.

YouTube 동영상을 보거나 아래 사용 설명서을 읽을 수 있습니다.

<a href="/docs/instructions.md" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041925-64f81f75-8bee-42ac-a234-a93339bc8cdc.png" alt="Instructions" width="180px" ></a>
<a href="https://www.youtube.com/watch?v=Qd-jJjduWeQ" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041927-c94c1a94-c708-44a4-9c81-df83bac686d4.png" alt="Youtube Tutorial" width="180px" ></a>

## 🦋 Tkinter Designer 후원

Tkinter Designer 프로젝트에서 혜택을 받은 경우 후원을 고려해 보세요. 이것은 Tkinter Designer의 개발을 가속화할 것입니다! 커피를 만드는 것은 간단합니다. 기꺼이 즐기겠습니다.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="180" ></a>
<a href="https://paypal.me/parthJadhav22?country.x=IN&locale.x=en_GB" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196043185-ebd61195-44ee-480f-9b76-f5eb7cfcaf55.png" alt="Paypal" width="180" ></a>


## 🔵 Discord server & Linkedin

아래 버튼을 클릭하여 디스코드 서버 또는 Linkedin을 확인할 수 있습니다.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="180px" ></a>
<a href="https://www.linkedin.com/in/parthjadhav04" target="_blank"><img src="https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin" alt="Connect on Linkedin" width="180px" height="58"></a>


## 📐 작동 방식

사용자가 해야 할 일은 Figma와 인터페이스를 설계한 다음 Figma 파일 URL과 API 토큰을 Tkinter Designer에 붙여넣기만 하면 됩니다.

Tkinter Designer는 Tkinter에서 GUI를 만드는 데 필요한 모든 코드와 이미지를 자동으로 생성합니다.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___

## 🎯 예제

Tkinter Designer를 사용하면 가능성이 무한하지만 Tkinter에서 완벽하게 복제할 수 있는 GUI가 몇 가지 있습니다.

<sup>예시는 저의 창작물이 아닙니다.</sup>

### HotinGo  [(More Info)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### CodTubify  [(More Info)](https://github.com/iamDyeus/CodTubify)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/181297276-fc8c4106-c988-4b1a-89d2-5e833a574aab.png">

### BeAnonymous [(More Info)](https://github.com/MambaCodes/BeAnonymous)

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/208241685-a3c51f59-746d-4e00-aaeb-c2c8357efb89.png">

### Frame Recorder [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

### Atarbals-Modern-Antivirus [(More Info)](https://github.com/HarshscGithub/Atarbals-Modern-Antivirus)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/205285178-74fb46c7-0c36-4fc5-983d-afbaaedb7cb9.png">

## 🔥 쇼 케이스

당신의 앱을 만들기 위해 Tkinter Designer가 사용되었는지 알려주시기 바랍니다. 더 많은 삽화가 다른 사람들에게 이로울 것입니다!

(See: [연락처](#-contact-me)) 또는 [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell)을 사용해 주세요.

## 📄 라이센스
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer는 BSD 3-clause "New" 또는 "Reved" 라이센스에 따라 라이센스가 부여됩니다.
[View Here](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| 권한 | 제한사항 | 조건
| --- | --- | ---
&check; 상업적 용도 | &times; 책임 | &#x1f6c8; 라이센스 및 저작권 고지
&check; 수정   | &times; 정당한 이유|
&check; 분배  |
&check; 개인 사용 |

## 컨트리뷰트

오픈 소스 커뮤니티, 개인 및 파트너의 모든 기여를 환영합니다. 우리의 성과는 당신의 적극적인 참여의 결과입니다.

[Contributing guidelines](docs/CONTRIBUTING.md)

[Code of conduct](CODE_OF_CONDUCT.md)

[LEARN.md](LEARN.md)

## 📝 연락처

만약 당신이 나에게 연락하고 싶다면
<EMAIL> 으로 연락 주세요

Connect with me on [![Linkedin](https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin)](https://www.linkedin.com/in/parthjadhav04/)
