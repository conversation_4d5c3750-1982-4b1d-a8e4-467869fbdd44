<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Automatizza la creazione di interfaccia grafica per Tkinter</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

## Translations

- [简体中文](/docs/README.zh-CN.md)
- [Français](/docs/README.fr-FR.md)
- [ગુજરાતી](/docs/README.gu-GU.md)
- [हिन्दी](/docs/README.hin-HIN.md)
- [Italiano](/docs/README.it-IT.md)
- [عربية](/docs/README.ar-DZ.md)
- [Turkish](/docs/README.tr-TR.md)
- [Brazil](/docs/README.pt-BR.md)

___

## 💡 Introduzione

Tkinter Designer è stato creato per velocizzare la fase di sviluppo dell'interfaccia grafica in Python. Viene utilizzato il ben noto software di prototipazione grafica [Figma](https://www.figma.com/) per creare stupende interfacce grafiche di Tkinter in Python in un batter d'occhio 👁️.

Tkinter Designer utilizza le API di Figma per analizzare il file di design e crearne dunque il rispettivo codice necessario per la GUI. Persino l'interfaccia di Tkinter Designer è stata creata utilizzando Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️  Vantaggi di Tkinter Designer

1. Interfaccia drag and drop
2. Significativamente più veloce rispetto scrivere il codice manualmente
3. Facilita la creazione di interfacce ancora più belle 

## ⚡️ Leggi le istruzioni qui -> [Documentazione](/docs/instructions.it-IT.md.md)

Le istruzioni contengono tutte le informazioni utili ad installare ed utilizzare Tkinter Designer, insieme alle informazioni utili alla risoluzione e segnalazione di problemi.

## 🦋 Supporta Tkinter Designer

Se tu o la tua azienda avete beneficiato di Tkinter Designer, considera a supportare lo sviluppo del progetto tramite una donazione. Questo ne aiuterebbe lo sviluppo! È tanto facile quanto preparare un caffè; preparamene uno per me ;)

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

## 🔵 Entra nel server Discord ufficiale di Tkinter Designer

Clicca sul pulsante sottostante per entrare nel server Discord ufficiale e prender parte alla discussione.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

## 📐 Come funziona

L'unica cosa che l'utente deve fare è disegnare un'interfaccia con Figma, di seguito copiare ed incollare l'URL del file di Figma e il token API in Tkinter Designer.

Tkinter Designer genererà automaticamente tutto il codice e le immagini necessarie per creare l'interfaccia grafica in Tkinter.


<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 Esempi

Le possibilità sono infinite con Tkinter Designer, ma questi sono alcune delle interfacce grafiche che possono essere perfettamente replicate in Tkinter.


<sup>Le seguenti creazioni non sono mie.</sup>

### HotinGo  [(Ulteriori informazioni)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### Pagina di registrazione

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### Pagina di branding

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(Ulteriori informazioni)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(Ulteriori informazioni)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 Vetrina

Se la tua app è stata fatta con Tkinter Designer, fammelo sapere. Sarebbe d'aiuto mostrare agli altri ulteriori esempi!

(Guarda: [Contattami](#📝-contattami)) o utilizza la sezione [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) nella pagina Discussions di GitHub.

## 📄 Licenza
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer è protetto da licenza BSD 3-Clause "New" or "Revised".

[Leggi qui.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Permessi | Restrizioni | Condizioni
| --- | --- | ---
&check; Uso commerciale | &times; Responsabilità | &#x1f6c8; Avviso di licenza e Copyright
&check; Modifica   | &times; Garanzia
&check; Distribuzione  
&check; Uso personale

## Contribuisci

Accettiamo qualsiasi contribuzione dalla comunità open-source, individuale o collettiva. Dobbiamo il nostro successo al vostro coinvolgimento attivo.

[Linee guida per il contributo](docs/CONTRIBUTING.md)

[Codice di comportamento](CODE_OF_CONDUCT.md)

[Scopri di più](LEARN.md)

## 📝 Contattami

Se vuoi contattarmi, puoi mandare una mail
 a <EMAIL>