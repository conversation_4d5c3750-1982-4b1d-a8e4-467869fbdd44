<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter डिजाइनर</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Tkinter GUI निर्माण को स्वचालित करें</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## 💡 प्रस्तावना

Tkinter डिजाइनर का उपयोग Python में GUI विकास प्रक्रिया को गति देने के लिए  बनाया गया था। यह Python में सुंदर Tkinter GUI बनाने के लिए प्रसिद्ध डिजाइन सॉफ्टवेयर [Figma](https://www.figma.com/) का उपयोग करता है।

Tkinter डिज़ाइनर एक डिज़ाइन फ़ाइल का विश्लेषण करने और GUI के लिए आवश्यक संबंधित code और फ़ाइलें बनाने के लिए Figma API का उपयोग करता है। यहां तक ​​​​कि Tkinter डिजाइनर का GUI भी Tkinter डिजाइनर का उपयोग करके बनाया गया है।

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️ Tkinter डिजाइनर के लाभ

1. इंटरफेस खींचें(means drag) और छोड़ें(means drop)
2. मैन्युअल रूप से कोड बनाने की तुलना में महत्वपूर्ण रूप से तेज़ है
3. अधिक सुंदर इंटरफेस बनाने की क्षमता

___

## 🦋 Tkinter डिजाइनर का समर्थन

दो ही तो शौक़ हैं मेरे, कॉफी और coding. … मुझे समर्थन दें, मेरे लिए एक कॉफी खरीदें.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

## ⚡️ Tkinter डिजाइनर को install करे और उसका उपयोग करना

निर्देशों में समस्या निवारण और रिपोर्टिंग समस्याओं की जानकारी के साथ-साथ Tkinter Designer को स्थापित करने और उपयोग करने के बारे में सभी जानकारी शामिल है। एक वीडियो भी है।

### [निर्देश पढ़ें](/docs/instructions.md)

### [वह वीडियो देखें](https://youtu.be/mFjE2-rbpm8)  

___
<br>

## 🔵  आधिकारिक(official) Tkinter डिज़ाइनर के डिस्कॉर्ड सर्वर से जुड़ें

डिसॉर्डर(Discord) सर्वर(server) से जुड़ने और चर्चाओं में भाग लेने के लिए नीचे दिए गए बटन पर क्लिक करें।

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

## ✅  नामकरण(Naming) का महत्व ([वीडियो](https://youtu.be/mFjE2-rbpm8) और[निर्देशों](/docs/instructions.md) में उल्लेख किया गया है)

Tkinter Designer इसे कोड में बदलने के लिए तत्वों के नामों पर बहुत अधिक निर्भर करता है। नामकरण मार्गदर्शिका(naming guide) देखें [यहां](/docs/instructions.md#2-element-guide).
___
<br>

## 📐 यह काम किस प्रकार करता है

केवल एक चीज जो उपयोगकर्ता को करने की आवश्यकता है वह है Figma के साथ एक इंटरफ़ेस डिज़ाइन करना, और फिर Figma फ़ाइल URL और API टोकन को Tkinter डिजाइनर में पेस्ट करना है।

Tkinter डिज़ाइनर स्वचालित रूप से Tkinter में GUI बनाने के लिए आवश्यक सभी कोड और छवियों(images) को उत्पन्न करेगा।
<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 उदाहरण

Tkinter डिजाइनर के साथ संभावनाएं अनंत हैं, लेकिन यहां कुछ GUI हैं जिन्हें Tkinter में पूरी तरह से दोहराया(replicated) जा सकता है।

<sup>निम्नलिखित मेरी रचनाएँ नहीं हैं।</sup>

### पंजीकरण(Registration) पेज

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### ब्रांडिंग पेज

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(और जानकारी)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(और जानकारी)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 शो-कैस

अगर आपका ऐप Tkinter  डिज़ाइनर के साथ बनाया गया था, तो मुझे बताएं। वो दूसरों के लिए उदाहरण देखना मददगार होगा!
(See: [मुझसे संपर्क करो](#-contact-me)) या उपयोग करें [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) section in Discussions.

___
<br>

## 📝 मुझसे संपर्क करो

अगर आप मुझसे संपर्क करना चाहते हैं, तो आप मुझ तक पहुंच सकते हैं - <EMAIL>

___
<br>

## 📄 लाइसेंस
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer को BSD 3-क्लॉज "New" या "Revised" लाइसेंस के तहत लाइसेंस प्राप्त है।
[View Here.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| अनुमतियां | प्रतिबंध | शर्तेँ
| --- | --- | ---
&check; वाणिज्य उपयोग | &times;  लाय्बिलिटी  | &#x1f6c8; लाइसेंस और कॉपीराइट नोटिस
&check; परिवर्तन(Modification) | &times; गारंटी(Warranty)
&check; वितरण(Distribution)
&check; निजी उपयोग
