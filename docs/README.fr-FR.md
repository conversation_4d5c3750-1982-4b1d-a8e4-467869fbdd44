<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Automatisez la création d'interfaces Tkinter</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## 💡 Introduction

Tkinter Designer a été créé pour accélérer le processus de développement des interfaces en Python. Il utilise le logiciel de design bien connu [Figma](https://www.figma.com/) afin de rendre la création de magnifiques interfaces Tkinter en Python du gâteau 🍰.

Tkinter Designer utilise l'API de Figma pour analyser les fichiers de design afin de créer le code et les fichiers respectifs nécessaires à l'interface. Même l'interface de Tkinter Designer à été créée avec Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️ Avantages de Tkinter Designer

1. Interfaces en drag and drop
2. Significativement plus rapide que créer le code manuellement
3. Capacité de créer des interface encore plus belles

___

## ⚡️ Installation et utilisation de Tkinter Designer

Les instructions contiennent toutes les informations à propos de l'installation et de l'utilisation de Tkinter Designer, ainsi que les informations pour diagnostiquer et signaler les problèmes.

### [Lire les instructions](instructions.fr-FR.md)

## 🦋 Soutenir Tkinter Designer

La vie sans café est quelque chose sans quelque chose… désolé, je n'ai pas encore eu mon café.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

___
<br>

## 📐 Comment ça marche

La seule chose que l'utilisateur a besoin de faire est de designer l'interface avec Figma, puis coller le lien du fichier Figma et de sa clé d'API dans  Tkinter Designer.

Tkinter Designer génèrera automatiquement tout le code et les images requises à créer l'interface avec Tkinter.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 Exemples

Les possibilités sont infinies avec Tkinter Designer, mais voici quelques interfaces pouvant être parfaitement répliquées au sein de Tkinter.

<sup>Les créations suivantes ne sont pas les miennes.</sup>

### Page d'inscription

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### Page de marque

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Enregistreur d'images [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 Vitrine

Si votre application a été créée avec Tkinter Designer, faites le moi savoir. Il sera utile pour d'autres de voir plus d'exemples !
(Se référer à la rubrique [Me contacter](#-contact-me)) ou utilisez la section [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) dans les Discussions.

___
<br>

## 📝 Me contacter

Si vous souhaitez me contacter, vous pouvez me joindre à <EMAIL>

___
<br>

## 📄 Licence
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer est mis à disposition sous licence BSD 3-Clause "Nouvelle" ou "Révisée".
[Trouvez-la ici.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Permissions | Restrictions | Conditions
| --- | --- | ---
&check; Utilisation commerciale | &times; Responsabilité | &#x1f6c8; License et Copyright
&check; Modification   | &times; Garrantie
&check; Distribution  
&check; Utilisation privée
