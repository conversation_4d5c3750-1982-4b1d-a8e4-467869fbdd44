<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Otomatik Tkinter GUI Oluşturucu</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

## 💡 Tanıtım

Tkinter Designer Python'da GUI hazırlama sürecini hızlandırmak için yapılmıştır. Tkinter Designer bu tasarımı kolayca yapabilmeniz için [Figma](https://www.figma.com/) yazılımını kullanır.

Tkinter Designer Figma API'sini kullanarak bir tasarım dosyasını analiz eder ve ardından GUI için ilgili kodu ve dosyaları sizin için oluşturur. Tkinter Designer'ın GUI tasarımı bile Tkinter Designer kullanarak yapılmıştır.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️ Tkinter Designer'ın Avantajları

1. Sürükle-bırak arayüzler
2. Manuel olarak kod ile oluşturmaktan çok daha hızlı
3. Daha güzel arayüzler oluşturmak için bir fırsat

---

## ⚡️ Tkinter Designer'ı Kurmak ve Kullanmak

Yönergeler sorun giderme ve hata bildirme ile birlikte Tkinter Designer'ı kurmak ve kullanmak için gereken tüm bilgileri içeriyor.

### [Yönergeleri Okuyun](/docs/instructions.tr-TR.md)

---

## 🦋 Tkinter Designer'ı Destekleme

Hayat sanki kahvesiz daha … pardon, şu ana kadar hiç kahvem olmadı.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

<br>

## 🔵 Tkinter Designer'ın Resmi Discord Sunucusu

Aşağıdaki butona tıklayarak Discord sunucumuza katılın ve tartışmalarda yer alın.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

<br>

## 📐 Nasıl Çalışır

Kullanıcıların yapması gereken tek şey Figma ile bir arayüz oluşturmaktır, ardından Figma URL'sini ve API tokenini Tkinter Designer'a yapıştırın.

Tkinter Designer otomatik olarak Tkinter'da GUI için gereken tüm kodu ve resimleri otomatik olarak oluşturacaktır.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

---

<br>

## 🎯 Örnekler

Tkinter Designer ile olasılıklar sonsuzdur, ama burada mükemmel bir şekilde kopyalanabilen birkaç GUI var.

<sup>Aşağıdakiler benim kreasyonlarım değil.</sup>

### Kayıt Sayfası

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### Marka Sayfası

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(Daha Fazla Bilgi)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk [(Daha Fazla Bilgi)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 Vitrin

Eğer Tkinter Designer ile tasarlanmış bir uygulamanız var ise lütfen bana haber verin. Başkalarının daha çok örnek görmesi için yararlı olacaktır!

(Ayrıca: [Bana Ulaşın](#-contact-me)) ya da tartışmalardan [Göster ve Anlat](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) bölümünü kullanın.

---

<br>

## 📝 İletişim

Eğer benimle iletişime geçmek istiyorsanı<NAME_EMAIL> mail adresinden ulaşabilirsiniz (İngilizce).

---

<br>

## 📄 Lisans

<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer BSD 3-Clause "Yeni" ya da "Revize Edilmiş" Lisansı ile lisanslanmıştır.  
[Burdan](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE) ulaşabilirsiniz.

| İzinler                 | Kısıtlamalar        | Şartlar                                   |
| ------------------------| --------------------| ------------------------------------------|
| &check; Ticari Kullanım | &times; Sorumluluk  | &#x1f6c8; Lisans ve Telif Hakkı Bildirimi |
| &check; Modifikasyon    | &times; Garanti     |
| &check; Dağıtım         |
| &check; Özel Kullanım   |
