<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">自动化 Tkinter GUI 创建</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://tokei.rs/b1/github/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## 💡 介绍

Tkinter Designer 旨在加速 Python 中的 GUI 开发过程。 它使用著名的设计软件 [Figma](https://www.figma.com/) 使在 Python 中创建漂亮的 Tkinter GUI 变得轻而易举。

Tkinter Designer 使用 Figma API 来分析设计文件并创建 GUI 所需的相应代码和文件。 甚至 Tkinter Designer 的 GUI 也是使用 Tkinter Designer 创建的。

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️  Tkinter 设计器的优势

1. 拖放界面
2. 比手动创建代码快得多。
3. 能够创建更漂亮的界面。

___

## ⚡️ 安装和使用 Tkinter Designer

这些说明包含有关安装和使用 Tkinter Designer 的所有信息，以及用于故障排除和报告问题的信息.

[阅读说明书](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/docs/instructions.zh-CN.md)

## 🦋 支持 Tkinter Designer

没有咖啡的生活是艰难的……对不起，我还没有喝过咖啡。

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

___
<br>

## 📐 这个怎么运作

用户唯一需要做的就是用 Figma 设计一个界面，然后将 Figma 文件 URL 和 API 令牌粘贴到 Tkinter Designer 中。

Tkinter Designer 将自动生成在 Tkinter 中创建 GUI 所需的所有代码和图像。

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 Examples

Tkinter Designer 的可能性是无限的，但这里有几个可以在 Tkinter 中完美复制的 GUI。

<small>以下不是我的创作。</small>

### 注册页面

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### 品牌页面

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

## 🔥 展示柜

如果您的应用是使用 Tkinter Designer 制作的，请告诉我。 其他人看到更多的例子会很有帮助！
（参见：[Contact Me](#-contact-me)）或使用[Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) 部分讨论 .

___
<br>

## 📝 联络我

如果你想联系我 - <EMAIL>

___
<br>

## 📄 执照
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer 根据 BSD 3 条款“新”或“修订”许可获得许可。
[View Here.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| 权限 | 限制 | 使适应
| --- | --- | ---
&check; 商业用途 | &times; 责任 | &#x1f6c8; 许可和版权声明
&check; 修改   | &times; 保修单
&check; 分配  
&check; 私人使用
