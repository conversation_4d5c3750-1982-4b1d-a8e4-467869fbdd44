Here's the translation of the provided text in Vietnamese:

<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h4 align="center" style="margin: 0 auto 0 auto;">Trình tạo GUI kéo và thả</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>
 
<p align="center">
<a href="https://www.producthunt.com/posts/tkinter-designer?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-tkinter&#0045;designer" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=312995&theme=neutral" alt="Tkinter&#0032;Designer - Giải pháp không cần code cho GUI Python | Product Hunt" style="width: 250px; height: 54px;" width="250" height="54" /></a>
</p>
  <br>

## Các bản dịch

- [简体中文](/docs/README.zh-CN.md)
- [Français](/docs/README.fr-FR.md)
- [ગુજરાતી](/docs/README.gu-GU.md)
- [हिन्दी](/docs/README.hin-HIN.md)
- [Italiano](/docs/README.it-IT.md)
- [عربية](/docs/README.ar-DZ.md)
- [Turkish](/docs/README.tr-TR.md)
- [Brazil](/docs/README.pt-BR.md)
- [Spanish](/docs/README.spa-SPA.md)
- [मराठी](/docs/README.mr-MR.md)
- [Korean](/docs/README.kr-KR.md)
- [Tiếng Việt](/docs/README.vi-VN.md)

___

## 💡 Giới thiệu

Tkinter Designer được tạo ra để tăng tốc quá trình phát triển GUI trong Python. Nó sử dụng phần mềm thiết kế nổi tiếng [Figma](https://www.figma.com/) để tạo ra giao diện Tkinter GUI đẹp mắt trong Python một cách dễ dàng như ăn bánh.

Tkinter Designer sử dụng API Figma để phân tích một tệp thiết kế và tạo ra mã và các tệp tương ứng cần thiết cho GUI. Ngay cả giao diện Tkinter Designer cũng được tạo bằng Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## 📢 Thông báo
### 🎉 Hỗ trợ nhiều frame đã có! 🎉

Bạn có thể tạo nhiều frame trong một tệp thiết kế duy nhất và Tkinter Designer sẽ tạo ra mã và các tệp tương ứng cho mỗi frame. Điều này là một bước quan trọng cho Tkinter Designer và tôi thực sự háo hức xem các bạn tạo ra những gì với nó.

Hãy tự do chia sẻ những tác phẩm của bạn với cộng đồng trên [Discord](https://discord.gg/QfE5jMXxJv).

Nếu bạn gặp bất kỳ lỗi nào hoặc có bất kỳ đề xuất nào, hãy tạo một issue [tại đây](https://github.com/ParthJadhav/Tkinter-Designer).
## ☄️  Ưu điểm của Tkinter Designer

1. Giao diện với chức năng kéo và thả.
2. Nhanh hơn nhiều so với việc viết mã bằng tay.
3. Tạo ra các giao diện đẹp hơn.

## ⚡️ Đọc hướng dẫn ở đây

Xem video trên YouTube hoặc đọc hướng dẫn bên dưới.

<a href="/docs/instructions.md" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041925-64f81f75-8bee-42ac-a234-a93339bc8cdc.png" alt="Hướng dẫn" width="180px" ></a>
<a href="https://www.youtube.com/watch?v=Qd-jJjduWeQ" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041927-c94c1a94-c708-44a4-9c81-df83bac686d4.png" alt="Hướng dẫn trên Youtube" width="180px" ></a>

## 🦋 Hỗ trợ Tkinter Designer

Hãy xem xét quyên góp cho dự án Tkinter Designer nếu bạn hoặc doanh nghiệp của bạn đã được hưởng lợi từ nó. Điều này sẽ giúp gia tăng quá trình phát triển Tkinter Designer! Hãy mua cho tôi một tách cafe; Tôi sẽ rất vui khi được thưởng thức nó.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Mua tôi một tách cà phê" width="180" ></a>
<a href="https://paypal.me/parthJadhav22?country.x=IN&locale.x=en_GB" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196043185-ebd61195-44ee-480f-9b76-f5eb7cfcaf55.png" alt="Paypal" width="180" ></a>


## 🔵 Máy chủ Discord và Linkedin

Nhấp vào nút dưới đây để tham gia máy chủ Discord hoặc Linkedin.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Tham gia máy chủ Discord" width="180px" ></a>
<a href="https://www.linkedin.com/in/parthjadhav04" target="_blank"><img src="https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin" alt="Kết nối trên Linkedin" width="180px" height="58"></a>


## 📐 Cách hoạt động

Chỉ cần thiết kế một giao diện bằng Figma, sau đó dán đường dẫn tệp Figma và mã thông báo API vào Tkinter Designer.

Tkinter Designer sẽ tự động tạo tất cả mã và hình ảnh cần thiết để tạo GUI trong Tkinter.

<img width="467" alt="Cách hoạt động" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___

## 🎯 Ví dụ

Có vô số khả năng với Tkinter Designer, nhưng đây là một số giao diện người dùng có thể được sao chép hoàn hảo trong Tkinter.

<sup>Các tác phẩm sau không phải do tôi tạo ra.</sup>

### HotinGo  [(Thông tin chi tiết)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### CodTubify  [(Thông tin chi tiết)](https://github.com/iamDyeus/CodTubify)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/181297276-fc8c4106-c988-4b1a-89d2-5e833a574aab.png">

### BeAnonymous [(Thông tin chi tiết)](https://github.com/MambaCodes/BeAnonymous)

<img width="467" alt="Ví dụ 1" src="https://user-images.githubusercontent.com/42001064/208241685-a3c51f59-746d-4e00-aaeb-c2c8357ef

b9d.png">

### MarkMe [(Thông tin chi tiết)](https://github.com/Ayan-thecodeking/MarkMe)

<img width="467" alt="Ví dụ 2" src="https://user-images.githubusercontent.com/42001064/21342913-16130f5c-c6ed-11e6-9077-d0f6a6cfdbcd.png">

## 🎓 Hướng dẫn sử dụng

Để biết cách sử dụng Tkinter Designer, hãy xem hướng dẫn chi tiết trong tệp [README.md](https://github.com/ParthJadhav/Tkinter-Designer/blob/main/README.md) trên GitHub.

Nếu bạn có bất kỳ câu hỏi hoặc cần sự trợ giúp, đừng ngần ngại đặt câu hỏi. Tôi sẽ cố gắng trả lời bạn càng nhanh càng tốt.

Tôi hy vọng bạn tận hưởng việc sử dụng Tkinter Designer và nó mang lại hiệu quả và sự tiện lợi cho quá trình phát triển ứng dụng GUI Python của bạn. Nếu bạn cần thêm thông tin hoặc hỗ trợ, xin vui lòng cho tôi biết.

___
## 🎯 Ví dụ

Có vô số khả năng với Tkinter Designer, nhưng đây là một số giao diện người dùng có thể được sao chép hoàn hảo trong Tkinter.

<sup>Những giao diện dưới đây không phải do tôi tạo ra.</sup>

### HotinGo  [(Xem thêm)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### CodTubify  [(Xem thêm)](https://github.com/iamDyeus/CodTubify)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/181297276-fc8c4106-c988-4b1a-89d2-5e833a574aab.png">

### BeAnonymous [(Xem thêm)](https://github.com/MambaCodes/BeAnonymous)

<img width="467" alt="Ví dụ 1" src="https://user-images.githubusercontent.com/42001064/208241685-a3c51f59-746d-4e00-aaeb-c2c8357efb89.png">

### Frame Recorder [(Xem thêm)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(Xem thêm)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

### Atarbals-Modern-Antivirus [(Xem thêm)](https://github.com/HarshscGithub/Atarbals-Modern-Antivirus)

<img width="467" alt="Ví dụ 3" src="https://user-images.githubusercontent.com/42001064/205285178-74fb46c7-0c36-4fc5-983d-afbaaedb7cb9.png">

## 🔥 Triển lãm

Hãy cho tôi biết nếu Tkinter Designer đã được sử dụng để tạo ứng dụng của bạn. Nhiều ví dụ sẽ hữu ích cho những người khác!

(Xem: [Liên hệ](#-contact-me)) hoặc sử dụng phần [Show and Tell](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) trong phần Thảo luận.

## 📄 Giấy phép
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter Designer được cấp giấy phép theo

 Giấy phép BSD 3-Clause "New" hoặc "Revised".
[Xem tại đây.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Quyền hạn | Hạn chế | Điều kiện
| --- | --- | ---
&check; Sử dụng thương mại | &times; Trách nhiệm | &#x1f6c8; Giấy phép và thông báo bản quyền
&check; Sửa đổi   | &times; Bảo hành
&check; Phân phối  
&check; Sử dụng cá nhân

## Đóng góp

Tất cả các đóng góp từ cộng đồng mã nguồn mở, cá nhân và đối tác đều được hoan nghênh. Thành tựu của chúng tôi là kết quả của sự tham gia tích cực của bạn.

[Hướng dẫn đóng góp](docs/CONTRIBUTING.md)

[Quy tắc ứng xử](CODE_OF_CONDUCT.md)

[LEARN.md](LEARN.md)

## 📝 Liên hệ

Nếu bạn muốn liên hệ với tôi, bạn có thể
liên hệ tô<NAME_EMAIL>

Kết nối với tôi trên [![Linkedin](https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin)](https://www.linkedin.com/in/parthjadhav04/