<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter डिझायनर</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">स्वयंचलित Tkinter GUI निर्मिती</h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## 💡 परिचय

Tkinter डिझायनर Python मध्ये GUI विकास प्रक्रियेला गती देण्यासाठी तयार केले गेले. हे सुप्रसिद्ध डिझाइन सॉफ्टवेअर [Figma](https://www.figma.com/) वापरून Python मध्ये सुंदर Tkinter GUI तयार करण्यासाठी केकचा एक तुकडा बनवते 🍰.

डिझाईन फाइलचे विश्लेषण करण्यासाठी आणि GUI साठी आवश्यक संबंधित कोड आणि फाइल्स तयार करण्यासाठी Tkinter Designer Figma API वापरतो. Tkinter Designer चे GUI देखील Tkinter Designer वापरून तयार केले आहे.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## 📢 घोषणा
### 🎉 मल्टी फ्रेम समर्थन येथे आहे! 🎉

तुम्ही आता एकाच डिझाईन फाइलमध्ये अनेक फ्रेम तयार करू शकता आणि Tkinter Designer प्रत्येक फ्रेमसाठी संबंधित कोड आणि फाइल्स तयार करेल. Tkinter डिझायनरसाठी ही एक मोठी पायरी आहे आणि तुम्ही याच्या सहाय्याने काय तयार करता हे पाहण्यासाठी मला खूप आनंद झाला आहे.

[Discord](https://discord.gg/QfE5jMXxJv) वर समुदायासोबत तुमची निर्मिती मोकळ्या मनाने शेअर करा.

तुम्हाला काही बग आढळल्यास किंवा काही सूचना असल्यास, कृपया [येथे] (https://github.com/ParthJadhav/Tkinter-Designer) एक समस्या तयार करा.
## ☄️  Tkinter डिझायनरचे फायदे


1. ड्रॅग आणि ड्रॉप सह इंटरफेस.
2. हाताने कोड लिहिण्यापेक्षा खूप जलद
3. अधिक भव्य इंटरफेस तयार करा

## ⚡️ येथे सूचना वाचा


YouTube व्हिडिओ पहा किंवा खालील सूचना वाचा.
<a href="/docs/instructions.md" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041925-64f81f75-8bee-42ac-a234-a93339bc8cdc.png" alt="Instructions" width="180px" ></a>
<a href="https://www.youtube.com/watch?v=Qd-jJjduWeQ" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196041927-c94c1a94-c708-44a4-9c81-df83bac686d4.png" alt="Youtube Tutorial" width="180px" ></a>

## 🦋 समर्थन Tkinter डिझायनर

तुम्‍हाला किंवा तुमच्‍या व्‍यवसायाला याचा फायदा झाला असल्‍यास Tkinter Designer प्रॉजेक्टला देणगी देण्याचा विचार करा. हे Tkinter डिझायनरच्या विकासास गती देईल! कॉफी बनवणे सोपे आहे; मला आनंद होईल.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="180" ></a>
<a href="https://paypal.me/parthJadhav22?country.x=IN&locale.x=en_GB" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/196043185-ebd61195-44ee-480f-9b76-f5eb7cfcaf55.png" alt="Paypal" width="180" ></a>


## 🔵 Discord सर्व्हर आणि LinkedIn


Discord सर्व्हर किंवा LinkedIn सामील होण्यासाठी खालील बटणावर क्लिक करा
<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="180px" ></a>
<a href="https://www.linkedin.com/in/parthjadhav04" target="_blank"><img src="https://img.shields.io/badge/Linkedin-blue?style=flat-square&logo=linkedin" alt="Connect on Linkedin" width="180px" height="58"></a>


## 📐 हे कसे कार्य करते

वापरकर्त्याला फक्त एक गोष्ट करणे आवश्यक आहे ते म्हणजे Figma सह इंटरफेस डिझाइन करा आणि नंतर Figma फाइल URL आणि API टोकन Tkinter Designer मध्ये पेस्ट करा.

Tkinter डिझायनर Tkinter मध्ये GUI तयार करण्यासाठी आवश्यक असलेले सर्व कोड आणि प्रतिमा स्वयंचलितपणे व्युत्पन्न करेल.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___

## 🎯 उदाहरणे


Tkinter डिझायनरमध्ये शक्यता अंतहीन आहेत, परंतु येथे काही GUI आहेत ज्या Tkinter मध्ये उत्तम प्रकारे तयार केल्या जाऊ शकतात.
<sup>खालील माझी निर्मिती नाही.</sup>

### HotinGo  [(More Info)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### CodTubify  [(More Info)](https://github.com/iamDyeus/CodTubify)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/181297276-fc8c4106-c988-4b1a-89d2-5e833a574aab.png">

### नोंदणी पृष्ठ

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### ब्रँडिंग पृष्ठ

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### फ्रेम रेकॉर्डर [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk  [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 शोकेस

कृपया मला कळवा की Tkinter Designer तुमचा अॅप तयार करण्यासाठी वापरला गेला होता. आणखी चित्रे असतील
इतर लोकांसाठी फायदेशीर!

(पहा: [माझ्याशी संपर्क साधा](#-contact-me)) किंवा चर्चांमध्ये [शो आणि सांगा](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) विभाग वापरा .

## 📄 परवाना
<!--- If you're not sure which open license to use see https://choosealicense.com/--->

Tkinter डिझायनर BSD 3-क्लॉज "नवीन" किंवा "सुधारित" परवान्याअंतर्गत परवानाकृत आहे.
[येथे पहा.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| परवानग्या | निर्बंध | परिस्थिती
| --- | --- | ---
&check; व्यावसायिक वापर| &times; दायित्व | &#x1f6c8; परवाना आणि कॉपीराइट सूचना
&check; फेरफार   | &times; हमी
&check; वितरण  
&check; खाजगी वापर


