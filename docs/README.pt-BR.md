<p align="center">
  <img width="200" src="https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" alt="logo">
  <h1 align="center" style="margin: 0 auto 0 auto;">Tkinter Designer</h1>
  <h5 align="center" style="margin: 0 auto 0 auto;">Automação de interfaces gráficas(gui) com Tkinter/h5>
  </p>

  <p align="center">
    <img src="https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src="https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src="https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## 💡 Introdução

Tkinter Designer foi criado para facilitar o desenvolvimento de interfaces gráficas - gui(graphical user interface) no Python. Isso funciona muito bem com o software de design [Figma](https://www.figma.com/) para criar interfaces gráficas belas com o Tkinter no Python como um pedaço de bolo 🍰.

Tkinter Designer utiliza API do Figma para analisar o arquivo de design e criar o respectivo código e os arquivos necessários para a interface gráfica. Sempre as interfaces do Tkinter Designer's é criado usando o Tkinter Designer.

<img width="500" alt="Tkinter Designer GUI" src="https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## ☄️  Vantagens do Tkinter Designer

1. Arrastar e soltar and interfaces
2. Significamente rapido para criar do que seria criando código manual
3. É possível criar interfaces gráficas bonitas

## ⚡️ Leia as instruções aqui -> [Documentation](/docs/instructions.pt-BR.md)

As intruções contém todas as informações sobre a instalação e utilização do Tkinter Designer, através dessas intformações podemos revolver problemas ou reportar problemas.

## 🦋 Suporte ao Tkinter Designer

Se você ou sua empresa conhecem os benefícios do Tkinter Designer, considerem dar um suporte ao desenvolvimento do projeto através de doações. Isso pode ajudar muito o desenvolvimento do Tkinter Designer! É muito fácil - pague-me um Café; Por favor faça isso por mim ;)

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" alt="Buy Me A Coffee" width="217px" ></a>

## 🔵 Inscreva-se no servidor Discord Oficial do Tkinter Designer's

Click no botão abaixo para increver-se no servidor Discord e para começar a fazer parte das discussões.

<a href="https://discord.gg/QfE5jMXxJv" target="_blank"><img src="https://user-images.githubusercontent.com/42001064/126635148-9a736436-5a6d-4298-8d8e-acda11aec74c.png" alt="Join Discord Server" width="217px" ></a>

## 📐 Como isso funciona

Apenas uma coisa que o usuário precisa é um design, uma interface com o Figma, e após isso colar a URL do Figma file URL e a URL da API token do Figma dentro Tkinter Designer.

Tkinter Designer pode automaticamente gerar um código e imagens requeridas para criar interface gráfica(gui) no Tkinter.

<img width="467" alt="How it Works" src="https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 Exemplos

As possibilidades são muitas com Tkinter Designer, mas aqui estão algumas interfaces gráficas(guis) que podem ser replicadas no Tkinter.

<sup>Nota: Essas interfaces gráficas não são minhas criações.</sup>

### HotinGo [(More Info)](https://github.com/Just-Moh-it/HotinGo)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/153225081-01a50bfb-5e1c-477d-9b1c-e786498db6d0.png">

### Página de Registro

<img width="467" alt="Example 1" src="https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### Página de Marca

<img width="467" alt="Example 2" src="https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### Frame Recorder [(More Info)](https://github.com/mehmet-mert/FrameRecorder)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk [(More Info)](https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link)

<img width="467" alt="Example 3" src="https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 Cases de sucesso

Se você tiver um app que foi feito com Tkinter Designer, contate-me. Isso pode ajudar outras pessoas, pessoas podem ficar interessadas ao ver esses exemplos!  
(Veja: [Contate-me](#-contate-me)) ou utilize [Mostrar e Contar](https://github.com/ParthJadhav/Tkinter-Designer/discussions/categories/show-and-tell) sessão na Discussions.

## 📝 Contate-me

Se você quiser ter meu contato , você pode contatar-<NAME_EMAIL>

___
<br>

## 📄 Licença
<!--- Se você não sabe muito sobre essa licença de código-aberto saiba aqui: https://choosealicense.com/--->

Tkinter Designer é licenciado através da licença BSD 3-Clause "New" ou "Revised".  
[Visualize Aqui.](https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| Permissões | Restrições | Condições
| --- | --- | ---
&check; Uso Comercial | &times; Liability | &#x1f6c8; Licença e Notificação de Copyright
&check; Modificação   | &times; Warranty
&check; Distribuição  
&check; Uso Privado
