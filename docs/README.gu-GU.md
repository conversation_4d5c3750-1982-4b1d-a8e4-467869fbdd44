<p align="center">
  <img width="200" src = "https://user-images.githubusercontent.com/42001064/120057695-b1f6c680-c062-11eb-96d5-2c43d05f9018.png" Alt = "لوگو">
  <h1 align="center" style="margin: 0 auto 0 auto;"> Tkinter ડિઝાઇનર </ h1>
  <h5 align="center" style="margin: 0 auto 0 auto"> Tkinter GUI બનાવટને સ્વચાલિત કરો.</ h5>
  </p>

  <p align="center">
    <img src = "https://img.shields.io/github/last-commit/ParthJadhav/Tkinter-Designer">
    <img src = "https://img.shields.io/github/contributors/ParthJadhav/Tkinter-Designer">
    <img src = "https://img.shields.io/github/issues/ParthJadhav/Tkinter-Designer?label=issues">
    <img src = "https://img.shields.io/github/stars/ParthJadhav/Tkinter-Designer">
  </p>

  <br>

___

## પરિચય

Tkinter ડિઝાઇનરને પાયથોનમાં GUI વિકાસ પ્રક્રિયા ઝડપી બનાવવા માટે બનાવવામાં આવી હતી. તે Python માં સુંદર Tkinter GUI બનાવવા માટે જાણીતા ડિઝાઇન Software [Figma](https://www.figma.com/) નો ઉપયોગ કરે છે.

Tkinter ડિઝાઇનર ડિઝાઇન ફાઇલનું વિશ્લેષણ કરવા અને GUI માટે જરૂરી કોડ અને ફાઇલો બનાવવા માટે ફિગ્મા APIનો ઉપયોગ કરે છે. ટિંકટર ડીઝાઈનરની જીયુઆઈ પણ ટિંકટર ડિઝાઇનરની મદદથી બનાવવામાં આવી છે.

<img width="500" alt="ટિંકટર ડિઝાઇનર GUI" src = "https://user-images.githubusercontent.com/42001064/119863796-92af4a80-bf37-11eb-9f6c-61b1ab99b039.png">

## Tkinter ડિઝાઇનરના ફાયદા

1. ઇન્ટરફેસો ખેંચો અને છોડો
2. મેન્યુઅલી કોડ બનાવવા કરતાં નોંધપાત્ર રીતે ઝડપી
3. વધુ સુંદર ઇન્ટરફેસો બનાવવાની ક્ષમતા

___

## ઇન્સ્ટોલ અને ઉપયોગ કરી રહ્યું છે Tkinter Designer

સૂચનોમાં મુશ્કેલીનિવારણ અને રિપોર્ટિંગ મુદ્દાઓની માહિતી સાથે, ટિંકિટર ડિઝાઇનર ઇન્સ્ટોલ કરવા અને વાપરવા વિશેની તમામ માહિતી શામેલ છે.

### [સૂચનો વાંચો](/docs/instructions.gu-GU.md)

## Tkinter સહાયક ટિંકિટર ડિઝાઇનર

Coffee :coffee:  વિનાનું જીવન કંઈક વિનાનું છે ... માફ કરશો, મારી પાસે હજી સુધી કોઈ કોફી નથી.

<a href="https://www.buymeacoffee.com/Parthjadhav" target="_blank"><img src = "https://cdn.buymeacoffee.com/buttons/v2/arial-yellow.png" Alt="મને એક કોફી ખરીદો" width= "217px"></a>

___
<br>

## નામકરણનું મહત્વ ([વિડિઓ](https://youtu.be/mFjE2-rbpm8) માં ઉલ્લેખિત અને [સૂચનો](/docs/instructions.gu-GU.md))

Tkinter ડિઝાઇનર તેને કોડમાં કન્વર્ટ કરવા તત્વોના નામ પર ખૂબ આધાર રાખે છે. નામકરણ માર્ગદર્શિકા જુઓ [અહીં](/docs/instructions.gu-GU.md).

<br>

## 📐 તે કેવી રીતે કાર્ય કરે છે

વપરાશકર્તાને Figma સાથેના ઇન્ટરફેસની ડિઝાઈન કરવાની જરૂર છે, અને તે પછી Figma ફાઇલ URL અને API ટોકનને Tkinter ડિઝાઇનરમાં પેસ્ટ કરો.

Tkinter ડિઝાઇનર, Tkinter માં GUI બનાવવા માટે જરૂરી બધા કોડ અને છબીઓ(images) આપમેળે પેદા કરશે.

<img width="467" alt="તે કેવી રીતે કાર્ય કરે છે" src = "https://user-images.githubusercontent.com/42001064/119832620-fb88c980-bf1b-11eb-8e9b-4affe7b92ba2.jpg">

___
<br>

## 🎯 ઉદાહરણો

શક્યતાઓ Tkinter ડિઝાઇનર સાથે અનંત છે, પરંતુ અહીં GUIs ની એક દંપતી છે જે સંપૂર્ણ રીતે Tkinter માં નકલ કરી શકાય છે.

<sup> નીચેની રચનાઓ મારી નથી. </sup>

### નોંધણી પૃષ્ઠ

<img width="467" alt="ઉદાહરણ 1" src = "https://user-images.githubusercontent.com/42001064/119250338-1f1adf80-bbbd-11eb-8ee1-72028a4e7a7f.png">

### બ્રાંડિંગ પૃષ્ઠ

<img width="467" alt = "ઉદાહરણ 2" src = "https://user-images.githubusercontent.com/42001064/119250668-496d9c80-bbbf-11eb-886b-cb1e75da18df.png">

### ફ્રેમ રેકોર્ડર [(વધુ માહિતી)] (<https://github.com/mehmet-mert/FrameRecorder>)

<img width="467" alt = "ઉદાહરણ 3" src = "https://user-images.githubusercontent.com/42001064/119834287-71d9fb80-bf1d-11eb-9acf-e7bfc8cc4d9e.png">

### WhatBulk [(વધુ માહિતી)] (<https://www.instagram.com/p/CQUmKckFBbT/?utm_medium=copy_link>)

<img width="467" alt = "ઉદાહરણ 3" src = "https://user-images.githubusercontent.com/42001064/122562284-87e06500-d060-11eb-8257-55f3b9dbecf0.PNG">

## 🔥 શોકેસ

જો તમારી એપ્લિકેશન Tkinter ડિઝાઇનર સાથે બનાવવામાં આવી હતી, તો મને જણાવો. અન્ય લોકો વધુ ઉદાહરણો જોવા માટે મદદરૂપ થશે!
અથવા ચર્ચાઓ [બતાવો અને કહો](https://github.com/ParthJadhav/Tkinter-Designer/discussion/categories/show-and-tell) વિભાગનો ઉપયોગ કરો.

___
<br>

## મારો સંપર્ક કરો

જો તમે મારો સંપર્ક કરવા માંગતા હો, તો તમે મને <EMAIL> પર પહોંચી શકો છો

___
<br>

## 📄 લાઇસન્સ

Tkinter ડિઝાઇનર BSD 3-કલમ "નવું" અથવા "સુધારેલ" લાઇસન્સ હેઠળ લાઇસન્સ પ્રાપ્ત છે.
[અહીં જુઓ.](Https://github.com/ParthJadhav/Tkinter-Designer/blob/master/LICENSE)

| અનુમતિઓ | પ્રતિબંધો | શરતો
| --- | --- | ---
&check; વ્યાપારી ઉપયોગ | &times; જવાબદારી | &#x1f6c8; લાઇસન્સ અને Copyright સૂચના
&check; ફેરફાર | &times; વોરંટી
&check; વિતરણ
&check; ખાનગી ઉપયોગ
