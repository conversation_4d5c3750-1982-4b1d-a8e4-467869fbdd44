print('Hello World!')
print('AI-Keyworder V1.1')
import customtkinter as ctk
import threading
import time
import os
import re
import asyncio

async def loadclip(PIL=None):
    global ci, done, webdriver, tqdm, By, Options, messagebox, simpledialog, filedialog, sys, Image, pyexiv2, clear_output, display, cv2, exiftool
    done = False
    # Clip Interrogator variables
    import sys
    import time
    import cv2
    from PIL import Image
    from tkinter import filedialog, messagebox, simpledialog
    from tqdm import tqdm
    import pyexiv2
    from IPython.display import clear_output, display
    from clip_interrogator import Config, Interrogator
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    caption_model_name = 'blip-base'
    clip_model_name = 'ViT-L-14/openai'
    config = Config()
    config.clip_model_name = clip_model_name
    config.caption_model_name = caption_model_name
    ci = Interrogator(config)
    done = True

def loadci():
    asyncio.run(loadclip())
# Load screen
done = False

def loadscreen():
    global done
    load = ctk.CTk()
    load.eval('tk::PlaceWindow . center')
    load.geometry('360x100')
    load.iconbitmap("logo.ico")
    load.title('Stocks AI-Keyworder')
    title_label = ctk.CTkLabel(load, text='Loading...', font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    load.resizable(width=False, height=False)
    ci = threading.Thread(target=loadci, args=(), kwargs={})
    ci.start()
    while not done:
        time.sleep(0.2)
        load.update()
    if done:
        load.destroy()

#Re-generate the title
async def improvename(prompt):
    prompt = "Make a description for the image (no longer than 20 words) using the text. The description must be in quotes. YOU MUST MENTION THE PLACE IF IT IS IN THE TEXT! IF THERE 2 DIFFERENT PLACES GIVEN IN THE TEXT, MENTION ONLY THAT PLACE WHICH IS WRITTEN IN BRIEF DESCRIPTION. RELY MORE ON THE INFORMATION FROM BRIEF DESCRIPTION(IF GIVEN). UNDER ANY CIRCUMSTANCES DO NOT CREDIT ANY PERSON OR WORK(e.g. DO NOT SAY: 'inspired by...' or smth like this). UNDER ANY CIRCUMSTANCES DO NOT WRITE ANYTHING ELSE, BESIDES THE DESCRIPTION! THE TEXT: " + prompt
    options = Options()
    
    driver = webdriver.Chrome(options=options)
    driver.get("https://deepinfra.com/chat")
    driver.set_window_size(1280, 720)
    driver.find_element(By.XPATH, "/html/body/div/div/div[5]/div/div/div[2]/form/div/div[1]/textarea[1]").send_keys(prompt)
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/div/div[5]/div/div/div[2]/form/div/button').click()
            clicked = True
        except:
            continue

    clicked = False
    newprompt = ''
    while not clicked:
        try:
            while True:
                element = driver.find_element(By.XPATH, '/html/body/div/div/div[3]/div/div/div/div[3]/div[2]/div/p')
                newprompt = driver.execute_script("return arguments[0].childNodes[0].textContent", element)
                whitelist = set('"')
                lennew = ''.join([c for c in newprompt if c in whitelist])
                if (len(lennew) % 2) == 0:
                    clicked = True
                    newprompt = newprompt.replace('"', '')
                    print(newprompt)
                    return newprompt
        except:
            continue

# Description to Keywords
async def get_keywords(prompt, keys_amount, userkeys):
    options = Options()
    driver = webdriver.Chrome(options=options)
    driver.get("https://imstocker.com/en/keyworder")
    driver.set_window_size(1280, 720)
    driver.find_element(By.NAME, "q").send_keys(prompt)
    driver.find_element(By.XPATH, '/html/body/div/div/div/div[3]/div[1]/div[1]/div[2]/div/div/div/form/button').click()
    print('WAIT')
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/div/div/div[3]/div[1]/div[2]/div[1]/button').click()
            clicked = True
        except:
            continue
    print('WAIT')
    clicked = False
    waittime = 50
    passed = 0
    while not clicked:
        print(passed)
        try:
            driver.find_element(By.XPATH,
                                '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[1]/div/div/div['
                                '1]/div/div/div[1]/span[2]/span/span[2]/a').click()
            clicked = True
        except:
            if passed > waittime:
                while not clicked:
                    try:
                        driver.find_element(By.XPATH,
                                            '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[1]/div/div/div['
                                            '1]/div/div/div[1]/span[2]/span/span/a').click()
                        clicked = True
                    except:
                        if passed > (waittime * 1.5):
                            symbam = int(len(prompt) / 4)
                            return []
                        passed += 1
                        continue
            passed += 1
            continue
    driver.find_element(By.XPATH, '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[2]').click()
    print('WAIT')
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH,
                                '/html/body/div/div/div/div[3]/div[1]/div[2]/div[3]/div[1]/div/div['
                                '1]/div/div/div/div[2]/div/button[1]').click()
            clicked = True
        except:
            continue
    if userkeys != "":
        keywords = userkeys.split(", ")
    else:
        keywords = []
    for i in range(100):
        if i % 2 == 1:
            try:
                path = '/html/body/div/div/div/div[3]/div[1]/div[2]/div[3]/div[1]/div/div[2]/div/div/div/div/div/div/div[' \
                       '1]/div[2]/div[1]/div[1]/div/div[1]/div/div/div/span/span[' + str(
                    i) + ']'
                element = driver.find_element(By.XPATH, path)
            except:
                symbam = int(len(prompt) / 4)
                return []
            word = driver.execute_script("return arguments[0].childNodes[0].textContent", element)
            word = word.replace("- ", '')
            if word not in keywords:
                keywords.append(word)
            print('got:', word)
    if len(keywords) > 50:
        del keywords[keys_amount:len(keywords)]
    if 'photography' in keywords:
        keywords.remove('photography')
        keywords.append('photography')
    if 'horizontal' in keywords:
        keywords.remove('horizontal')
        keywords.append('horizontal')
    if 'color image' in keywords:
        keywords.remove('color image')
        keywords.append('color image')
    print(len(keywords), keywords)
    return keywords

def try_again(prompt, keys_amount):
    asyncio.run(get_keywords(prompt, keys_amount))

def extract_thumbnail(video_path, thumbnail_path, frame_number=0):
    cap = cv2.VideoCapture(video_path)

    # Check if the video file is opened successfully
    if not cap.isOpened():
        print("Error: Could not open video file.")
        return

    # Set frame position to the desired frame number
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

    # Read the frame
    ret, frame = cap.read()

    # Check if the frame is read successfully
    if not ret:
        print("Error: Could not read frame.")
        return

    # Save the frame as an image
    cv2.imwrite(thumbnail_path, frame)

    # Release the video capture object
    cap.release()

    print(f"Thumbnail extracted and saved as {thumbnail_path}")

def image_recognition(path, isfiles, userprompt, userkeys):
    global imgs
    processed = 0
    total = 0
    ws = ctk.CTk()
    ws.eval('tk::PlaceWindow . center')
    ws.geometry('360x100')
    ws.iconbitmap("logo.ico")
    ws.title('Stocks AI-Keyworder')
    txt = 'processed: ' + str(processed) + '/' + str(total)
    title_label = ctk.CTkLabel(ws, text=txt, font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    eta = ctk.CTkLabel(ws, text=txt, font=ctk.CTkFont(size=20, weight='bold'))
    eta.pack(padx=10, pady=(40, 10))
    ws.resizable(width=False, height=False)
    ws.update()

    def image_to_prompt(image, mode):
        ci.config.chunk_size = 2048 if ci.config.clip_model_name == "ViT-L-14/openai" else 1024
        ci.config.flavor_intermediate_count = 2048 if ci.config.clip_model_name == "ViT-L-14/openai" else 1024
        image = image.convert('RGB')
        if mode == 'best':
            return ci.interrogate(image)
        elif mode == 'classic':
            return ci.interrogate_classic(image)
        elif mode == 'fast':
            return ci.interrogate_fast(image)

    # @title Image to prompt! 🖼️ -> 📝
    folder_path = path  # @param {type:"string"}
    prompt_mode = 'fast'  # @param ["best","fast","classic","negative"]
    if isfiles:
        total = len(path)
        vids = []
        imgs = []
        for i in range(len(path)):
            if str(path[i]).endswith('.mp4') or str(path[i]).endswith('.MP4') or str(path[i]).endswith('.avi') or str(path[i]).endswith('.MOV') or str(path[i]).endswith('.mov') or str(path[i]).endswith('.mkv'):
                vids.append(path[i])
            else:
                imgs.append(path[i])

    ci.config.quiet = True
    if not isfiles:
        files = [f for f in os.listdir(folder_path) if f.endswith('.jpg') or f.endswith('.JPG') or f.endswith('.png')] if os.path.exists(
            folder_path) else []
        total = len(files)
    else:
        files = imgs
    txt = 'processed: ' + str(processed) + '/' + str(total)
    title_label.configure(text=txt)
    ws.update()
    print(files)
    for idx, file in enumerate(tqdm(files, desc='Generating prompts')):
        if idx > 0 and idx % 100 == 0:
            clear_output(wait=True)
        if not isfiles:
            file = os.path.join(folder_path, file)
        print(file)
        image = Image.open(file).convert('RGB')
        imageexif = pyexiv2.Image(file)

        prompt = image_to_prompt(image, prompt_mode)
        prompt = re.sub(r"by .*?, ", "", prompt)
        prompt = re.sub(r"featured on .*?, ", "", prompt)
        prompt = prompt.replace('shutterstock', '')
        prompt = prompt.replace('inspired', '')
        prompt = prompt.replace('unsplash', '')
        if userprompt != '':
            prompt = "Brief image description: '" + userprompt + "', " + prompt
        print(prompt)
        name = asyncio.run(improvename(prompt))
        keywords = asyncio.run(get_keywords(name, 50, userkeys))
        thumb = image.copy()
        thumb.thumbnail([256, 256])
        display(thumb)

        pyexiv2.set_log_level(4)
        imageexif.clear_exif()
        imageexif.clear_comment()
        time.sleep(0.02)
        imageexif.modify_exif({'Exif.Image.XPKeywords': ';'.join(keywords)})
        imageexif.modify_exif({'Exif.Image.XPTitle': name})
        imageexif.modify_exif({'Exif.Image.XPSubject': name})
        imageexif.close()
        print('image completed')
        processed += 1
        txt = 'processed: ' + str(processed) + '/' + str(total)
        title_label.configure(text=txt)
        ws.update()

    print('Image files were finished')

    if False:
        if not isfiles:
            files = [f for f in os.listdir(folder_path) if f.endswith('.mp4') or f.endswith('.MP4') or f.endswith('.MOV') or f.endswith('.avi') or f.endswith('.mkv') or f.endswith('.mov')] if os.path.exists(
            folder_path) else []
            total = len(files)
        else:
            files = vids
        txt = 'processed: ' + str(processed) + '/' + str(total)
        title_label.configure(text=txt)
        ws.update()
        for idx, file in enumerate(tqdm(files, desc='Generating prompts')):
            if idx > 0 and idx % 100 == 0:
                clear_output(wait=True)
            if not isfiles:
                file = os.path.join(folder_path, file)
            print(file)
            thumb = str(file.split('.', 1)[0] + '.jpg')
            print(thumb, file)
            extract_thumbnail(file, thumb)
            time.sleep(0.1)
            image = Image.open(thumb).convert('RGB')
            imageexif = pyexiv2.Image(thumb)

            prompt = image_to_prompt(image, prompt_mode)
            prompt = re.sub(r"by .*?, ", "", prompt)
            prompt = re.sub(r"featured on .*?, ", "", prompt)
            prompt = prompt.replace('shutterstock', '')
            prompt = prompt.replace('inspired', '')
            prompt = prompt.replace('unsplash', '')
            if userprompt != '':
                prompt = "Brief image description: '" + userprompt + "', " + prompt
            print(prompt)
            name = asyncio.run(improvename(prompt))
            keywords = asyncio.run(get_keywords(name, 50))
            thumb = image.copy()
            thumb.thumbnail([256, 256])
            display(thumb)

            pyexiv2.set_log_level(4)
            imageexif.clear_exif()
            imageexif.clear_comment()
            time.sleep(0.02)
            imageexif.modify_exif({'Exif.Image.XPKeywords': ';'.join(keywords)})
            imageexif.modify_exif({'Exif.Image.XPTitle': name})
            imageexif.modify_exif({'Exif.Image.XPSubject': name})
            imageexif.close()
            print('image completed')
            processed += 1
            txt = 'processed: ' + str(processed) + '/' + str(total)
            title_label.configure(text=txt)
            ws.update()
            print('Video files were finished')

    messagebox.showinfo("AI-keyworder", "Process is finished")
    ws.destroy()


def folder():
    path = filedialog.askdirectory()
    ftype = simpledialog.askstring('FILE TYPE', 'Enter File Type (Photo, Video, Timelapse)', parent=root)
    userprompt = simpledialog.askstring('CUSTOM PROMPT', 'Enter Custom Prompt (Leave blank to generate the prompt)', parent=root)
    userkeys = simpledialog.askstring('CUSTOM KEYWORDS', 'Enter Custom Keywords', parent=root)
    root.lift()
    userprompt = ftype + ' of ' + userprompt
    global clear_output, display, os, Image, pyexiv2
    image_recognition(path, False, userprompt, userkeys)

def files():
    path = filedialog.askopenfilenames()
    ftype = simpledialog.askstring('FILE TYPE', 'Enter File Type (Photo, Video, Timelapse)', parent=root)
    userprompt = simpledialog.askstring('CUSTOM PROMPT', 'Enter Custom Prompt (Leave blank to generate the prompt)', parent=root)
    userkeys = simpledialog.askstring('CUSTOM KEYWORDS', 'Enter Custom Keywords', parent=root)
    root.lift()
    userprompt = ftype + ' of ' + userprompt
    global clear_output, display, os, Image, pyexiv2
    image_recognition(path, True, userprompt, userkeys)


def finish():
    sys.exit()


def mainscreen():
    global done, root
    root = ctk.CTk()
    root.eval('tk::PlaceWindow . center')
    root.geometry('680x320')
    root.iconbitmap("logo.ico")
    root.title('Stocks AI-Keyworder')
    title_label = ctk.CTkLabel(root, text='Stocks AI-Keyworder', font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    sfb = ctk.CTkButton(root, text='Select Folder', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=folder)
    sfb.pack(pady=5)
    sfib = ctk.CTkButton(root, text='Select Files', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=files)
    sfib.pack(pady=5)
    bfb = ctk.CTkButton(root, text='Exit', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=finish)
    bfb.pack(pady=5)
    root.resizable(width=False, height=False)
    root.mainloop()


time.sleep(0.2)
loadscreen()
mainscreen()
