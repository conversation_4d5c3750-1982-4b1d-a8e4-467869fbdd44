import sys
import time
import os
import re
import io
import json
import logging
from PIL import Image
import pyexiv2
import requests
import base64
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import threading

# Remove existing log file if it exists
if os.path.exists('ai_keyworder.log'):
    os.remove('ai_keyworder.log')

# Configure logging
logging.basicConfig(
    filename='ai_keyworder.log',
    filemode='a',
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.ERROR
)

# Global Variables
glob_keys = []
glob_titles = []
errs = 0
errs_files = []

# Constants
API_KEY = '********************************************************'  # Consider using environment variables for security
API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4o-mini"  # Ensure this is the correct model name

# GUI Initialization
def show_initial_message():
    messagebox.showinfo(
        title='AI-Keyworder',
        message='Please do not use Russian\ncharacters in folder and file names!'
    )

show_initial_message()

def ai_keywording(file_path, filetype, custom_prompt):
    global glob_titles, glob_keys, errs, errs_files

    try:
        # Prepare filetype and custom prompt strings
        filetype_str = f" File type is {filetype.upper()}." if filetype else ""
        custom_prompt_str = f" YOU MUST WRITE ABOUT: {custom_prompt}." if custom_prompt else ""

        # Open and resize the image
        with Image.open(file_path) as img:
            # Increase image resolution by reducing the scaling factor
            scaling_factor = 2  # Adjust as needed to control resolution
            new_size = (img.width // scaling_factor, img.height // scaling_factor)
            img = img.resize(new_size, Image.LANCZOS)

            # Encode the image to base64
            buffered = io.BytesIO()
            img.save(buffered, format="JPEG")
            base64_image = base64.b64encode(buffered.getvalue()).decode('utf-8')

        # Prepare the payload for the API request
        payload = {
            "model": MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": (
                                f"Analyze the uploaded image, describe it in 15-30 words starting with the main subject, emphasizing people (include details or state 'no people' if none), and list 50 relevant stock-related keywords with the most important ones first. Include the confirmed location within the description. Complete the request fully. {filetype_str} {custom_prompt_str} FOLLOW THIS TEMPLATE: Description: *. Keywords: 1. keyword, 2. keyword... 50. keyword."
                            )
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 350
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }

        # Send request to OpenAI API
        response = requests.post(API_URL, headers=headers, json=payload)

        if response.status_code != 200:
            raise ValueError(f"API Error {response.status_code}: {response.text}")

        response_data = response.json()

        # Extract description and keywords
        content = response_data['choices'][0]['message']['content']
        description_match = re.search(r'Description:\s*(.*?)\s*Keywords:', content, re.DOTALL)
        keywords_match = re.search(r'Keywords:\s*(.*)', content, re.DOTALL)

        if not description_match or not keywords_match:
            raise ValueError("Failed to parse the API response.")

        description = description_match.group(1).strip()
        keywords_raw = keywords_match.group(1).strip()
        keywords = [re.sub(r'^\d+\.\s*', '', kw).lower() for kw in keywords_raw.split(',')]

        # Update global lists
        glob_titles.append(description)
        glob_keys.append(keywords)

        print(f"Title: {description}")
        print(f"Keywords: {keywords}")

        return keywords, description

    except Exception as e:
        logging.error(f"Error processing file {file_path}: {e}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None

def eta(total, processed):
    remaining = total - processed
    estimated_time = remaining * 10  # Assuming 10 seconds per file; adjust as needed
    if estimated_time > 80:
        return f"{int(estimated_time / 60)} min"
    return f"{int(estimated_time)} sec"

def update_progress(processed, total):
    txt = f'Processed: {processed}/{total}, ETA: {eta(total, processed)}'
    progress_label.configure(text=txt)
    # Ensure the GUI updates
    progress_label.update_idletasks()

def keywording(is_files, path, filetype_var, custom_prompt_var):
    global errs, errs_files

    filetype = filetype_var.get()
    custom_prompt = custom_prompt_var.get()
    files = []

    if is_files:
        files = list(path)
    else:
        folder_path = path
        if not os.path.exists(folder_path):
            messagebox.showerror("Error", "Selected folder does not exist.")
            return
        files = [
            os.path.join(folder_path, f)
            for f in os.listdir(folder_path)
            if f.lower().endswith(('.jpg', '.jpeg', '.png', '.raw'))
        ]

    total = len(files)
    processed = 0
    update_progress(processed, total)

    for idx, file_path in enumerate(files, start=1):
        print(f'Processing file: {file_path}')
        keywords, title = ai_keywording(file_path, filetype, custom_prompt)

        if keywords and title:
            try:
                image_exif = pyexiv2.Image(file_path)
                pyexiv2.set_log_level(4)
                keywords = [item.split('. ', 1)[-1] for item in keywords]
                keywords[-1] = keywords[-1].replace(".", "")
                image_exif.modify_exif({
                    'Exif.Image.XPKeywords': ';'.join(keywords),
                    'Exif.Image.XPTitle': title,
                    'Exif.Image.XPSubject': title
                })

                image_exif.close()
                print('Image metadata updated successfully.')
            except Exception as e:
                logging.error(f"Failed to update EXIF for {file_path}: {e}")
                errs += 1
                errs_files.append(os.path.basename(file_path))
        else:
            logging.error(f"Keywording failed for {file_path}")

        processed += 1
        update_progress(processed, total)

    progress_label.configure(text='Finished')
    root.update_idletasks()

    message = f"Process is finished\nErrors: {errs}"
    messagebox.showinfo("AI-Keyworder V2.0", message)

    if errs > 0:
        error_files = "\n".join(errs_files)
        messagebox.showwarning(
            "AI-Keyworder V2.0",
            f"Не удалось обработать следующие файлы:\n{error_files}\nПопробуй про-ключевать файлы снова"
        )

    progress_label.configure(text='Select files to begin')
    root.update_idletasks()

def start_keywording(is_files, path, filetype_var, custom_prompt_var):
    # Run the keywording process in a separate thread
    threading.Thread(target=keywording, args=(is_files, path, filetype_var, custom_prompt_var), daemon=True).start()

def select_files():
    def start_processing():
        master.destroy()
        start_keywording(is_files=True, path=selected_files, filetype_var=filetype_var, custom_prompt_var=custom_prompt_var)

    selected_files = filedialog.askopenfilenames(
        parent=root,
        filetypes=(
            ("Image files", "*.jpg;*.jpeg;*.png;*.JPG;*.JPEG;*.PNG;*.RAW"),
            ("All files", "*.*")
        )
    )
    if not selected_files:
        return

    master = tk.Toplevel(root)
    master.title('AI-Keyworder - File Selection')
    master.geometry('250x135')
    master.iconbitmap("logo.ico")

    tk.Label(master, text="FILE TYPE").grid(row=0, column=0, padx=10, pady=10)
    tk.Label(master, text="PROMPT").grid(row=1, column=0, padx=10, pady=10)

    filetype_var = tk.StringVar()
    custom_prompt_var = tk.StringVar()

    tk.Entry(master, textvariable=filetype_var).grid(row=0, column=1, padx=10, pady=10)
    tk.Entry(master, textvariable=custom_prompt_var).grid(row=1, column=1, padx=10, pady=10)

    tk.Button(
        master,
        text="Start",
        command=start_processing,
        bg='green',
        fg='white'
    ).grid(row=2, column=1, pady=20)

    master.mainloop()

def select_folder():
    def start_processing():
        master.destroy()
        start_keywording(is_files=False, path=selected_folder, filetype_var=filetype_var, custom_prompt_var=custom_prompt_var)

    selected_folder = filedialog.askdirectory(parent=root)
    if not selected_folder:
        return

    master = tk.Toplevel(root)
    master.title('AI-Keyworder - Folder Selection')
    master.geometry('250x135')
    master.iconbitmap("logo.ico")

    tk.Label(master, text="FILE TYPE").grid(row=0, column=0, padx=10, pady=10)
    tk.Label(master, text="PROMPT").grid(row=1, column=0, padx=10, pady=10)

    filetype_var = tk.StringVar()
    custom_prompt_var = tk.StringVar()

    tk.Entry(master, textvariable=filetype_var).grid(row=0, column=1, padx=10, pady=10)
    tk.Entry(master, textvariable=custom_prompt_var).grid(row=1, column=1, padx=10, pady=10)

    tk.Button(
        master,
        text="Start",
        command=start_processing,
        bg='#FF6600',
        fg='white'
    ).grid(row=2, column=1, pady=20)

    master.mainloop()

def exit_application():
    sys.exit()

# Main GUI Setup
root = ctk.CTk()
root.eval('tk::PlaceWindow . center')
root.geometry('680x360')
root.iconbitmap("logo.ico")
root.title('AI-Keyworder V2.0')

# Title Label
title_label = ctk.CTkLabel(
    root,
    text='AI-Keyworder V2.0',
    font=ctk.CTkFont(size=30, weight='bold')
)
title_label.pack(padx=20, pady=(40, 20))

# Progress Label
progress_label = ctk.CTkLabel(
    root,
    text='Select files to begin',
    font=ctk.CTkFont(size=15, weight='bold')
)
progress_label.pack(padx=10, pady=5)

# Buttons
select_folder_btn = ctk.CTkButton(
    root,
    text='Select Folder',
    font=ctk.CTkFont(size=20, weight='bold'),
    width=400,
    height=50,
    fg_color='#FF6600',
    hover_color='#b54800',
    command=select_folder
)
select_folder_btn.pack(pady=10)

select_files_btn = ctk.CTkButton(
    root,
    text='Select Files',
    font=ctk.CTkFont(size=20, weight='bold'),
    width=400,
    height=50,
    fg_color='#FF6600',
    hover_color='#b54800',
    command=select_files
)
select_files_btn.pack(pady=10)

exit_btn = ctk.CTkButton(
    root,
    text='Exit',
    font=ctk.CTkFont(size=20, weight='bold'),
    width=400,
    height=50,
    fg_color='#FF6600',
    hover_color='#b54800',
    command=exit_application
)
exit_btn.pack(pady=10)

root.resizable(width=False, height=False)
root.mainloop()
