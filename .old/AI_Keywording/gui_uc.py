print('Hello World!')
print('AI-Keyworder V1.27')
import customtkinter as ctk
import threading
import time
import os
import re
import asyncio
from collections import Counter
import undetected_chromedriver as uc

async def loadclip(PIL=None):
    global ci, done, webdriver, tqdm, By, Options, messagebox, simpledialog, filedialog, sys, Image, pyexiv2, clear_output, display, cv2, exiftool
    done = False
    # Clip Interrogator variables
    import sys
    import time
    import cv2
    from PIL import Image
    from tkinter import filedialog, messagebox, simpledialog
    from tqdm import tqdm
    import pyexiv2
    from IPython.display import clear_output, display
    from clip_interrogator import Config, Interrogator
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    caption_model_name = 'blip-base'
    clip_model_name = 'ViT-L-14/openai'
    config = Config()
    config.clip_model_name = clip_model_name
    config.caption_model_name = caption_model_name
    ci = Interrogator(config)
    done = True

def loadci():
    asyncio.run(loadclip())
# Load screen
done = False

def loadscreen():
    global done
    load = ctk.CTk()
    load.eval('tk::PlaceWindow . center')
    load.geometry('360x100')
    load.iconbitmap("logo.ico")
    load.title('Stocks AI-Keyworder')
    title_label = ctk.CTkLabel(load, text='Loading...', font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    load.resizable(width=False, height=False)
    ci = threading.Thread(target=loadci, args=(), kwargs={})
    ci.start()
    while not done:
        time.sleep(0.2)
        load.update()
    if done:
        load.destroy()

#Generate the title
async def improvename(prompt, origprompt, mode, instruct):
    if instruct != '':
        if instruct == 'place' or 'p':
            instruct = 'VERY IMPORTANT: IF INDONESIA, BRASIL, ARGENTINA, PHILLIPINES OR THAILAND ARE MENTIONED AS A PHOTO LOCATION IN DESCRIPTION, THEN YOU MUST MAKE UP ANOTHER PLACE WITH THE SAME SCENERY AS DESCRIBED IN THE PICTURE AND USE THIS NEW PLACE AS A PHOTO LOCATION!!'
        else:
            instruct = 'VERY IMPORTANT: ' + instruct
    else:
        instruct = ''
    if mode != 'bank':
        global sysprompt, instr
        sysprompt = origprompt
        instr = instruct
        t = threading.Thread(target=genkeys)
        t.start()
    prompt = "Make a description for the image (no longer than 20 words) using the text. The description must be in quotes. Description should clearly describe the content of a picture. YOU MUST MENTION THE PLACE IF IT IS IN THE TEXT! IF THERE 2 DIFFERENT PLACES GIVEN IN THE TEXT, MENTION ONLY THAT PLACE WHICH IS WRITTEN IN BRIEF DESCRIPTION. RELY MORE ON THE INFORMATION FROM BRIEF DESCRIPTION(IF GIVEN). UNDER ANY CIRCUMSTANCES DO NOT CREDIT ANY PERSON OR WORK(e.g. DO NOT SAY: 'inspired by...' or smth like this). UNDER ANY CIRCUMSTANCES DO NOT WRITE ANYTHING ELSE, BESIDES THE DESCRIPTION! " + instruct + " THE TEXT: " + prompt
    options = uc.ChromeOptions()
    options.add_argument("start-maximized")
    options.add_argument('--headless=new')
    driver = uc.Chrome(options=options, use_subprocess=True)
    driver.get("https://labs.perplexity.ai/")
    # Open model selection menu
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[1]/div/div[2]/div/div/div/div/select').click()
            clicked = True
        except:
            driver.get_screenshot_as_file('google.png')
            driver.save_screenshot('google.png')
            continue
    clicked = False
    # Select a model
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[1]/div/div[2]/div/div/div/div/select/option[14]').click()
            clicked = True
        except:
            continue
    # Paste the message
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, "/html/body/div/main/div/div/div[2]/div[2]/div/div/div/div/div/textarea").send_keys(prompt)
            clicked = True
        except:
            continue
    clicked = False
    # Click Send
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[2]/div/div/div/div/div/div[2]/button').click()
            clicked = True
        except:
            continue

    clicked = False
    newprompt = ''
    # Extract the answer
    while not clicked:
        try:
            while True:
                element = driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[1]/div/div[2]/div/div/div/div[3]/div/div/div[1]/div[2]/div')
                newprompt = driver.execute_script("return arguments[0].childNodes[0].textContent", element)
                whitelist = set('"')
                lennew = ''.join([c for c in newprompt if c in whitelist])
                if (len(lennew) % 2) == 0:
                    clicked = True
                    newprompt = newprompt.replace('"', '')
                    print(newprompt)
                    print('ret title')
                    return newprompt
        except:
            continue

# Generate some keywords
def genkeys():
    global sysprompt, gen_keys, compl, instr
    compl = False
    sysprompt = "You should create 50 unique keywords that describes a picture. Keywords should clearly describe the content of a picture. I GIVE YOU A DESCRIPTION OF SOME PICTURE AND YOU SHOULD CREATE KEYWORDS FROM THAT DESCRIPTION. Keywords should consist only of a single word. Every keyword should be separated by a comma. PLEASE DO NOT MAKE KEYWORDS OF TRIPLE WORDS! DO NOT WRITE ANYTHING, BUT ONLY 50 KEYWORDS. THE NUMBER OF KEYWORDS SHOULD BE 50 (FIFTY)! REMEMBER: FIRST 2 KEYWORDS MUST BE ABOUT THE LOCATION OF THE PICTURE (IF GIVEN), THEN THE FOLLOWING KEYWORDS MUST BE ABOUT PEOPLE (HOW MANY, GENDER, ETC (IF GIVEN)). EVERYTHING MUST BE IN QUOTES! UNDER ANY CIRCUMSTANCES DO NOT WRITE ANY COMMENTS, YOUR OUTPUT MUST ONLY BE KEYWORDS TEXT IN QUOTES! " + instr + " Picture description: " + sysprompt
    options = uc.ChromeOptions()
    options.add_argument("start-maximized")
    options.add_argument('--headless=new')
    driver = uc.Chrome(options=options, use_subprocess=True)
    driver.get("https://labs.perplexity.ai/")
    # Open model selection menu
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[1]/div/div[2]/div/div/div/div/select').click()
            clicked = True
        except:
            continue
    clicked = False
    # Select a model
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[1]/div/div[2]/div/div/div/div/select/option[14]').click()
            clicked = True
        except:
            continue
    # Paste the message
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, "/html/body/div/main/div/div/div[2]/div[2]/div/div/div/div/div/textarea").send_keys(sysprompt)
            clicked = True
        except:
            continue
    clicked = False
    # Click Send
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[2]/div[2]/div/div/div/div/div/div[2]/button').click()
            clicked = True
        except:
            continue

    clicked = False
    gen_keys = ''
    # Extract the answer
    while not clicked:
        try:
            while True:
                element = driver.find_element(By.XPATH, '/html/body/div/main/div/div/div[1]/div/div[2]/div/div/div/div[3]/div/div/div[1]/div[2]/div')
                gen_keys = driver.execute_script("return arguments[0].childNodes[0].textContent", element).lower()
                compl = False
                whitelist = set('"')
                lennew = ''.join([c for c in gen_keys if c in whitelist])
                if (len(lennew) % 2) == 0:
                    clicked = True
                    gen_keys = gen_keys.replace('"', '')
                    gen_keys = gen_keys.replace('.', '')
                    gen_keys = gen_keys.split(", ")
                    print(gen_keys)
                    compl = True
                    print('ret keys')
                    return gen_keys
        except:
            continue

# Description to Keywords
def get_keywords():
    global bankprompt, ukeys, workmode, complete
    if workmode == 'ai':
        global gen_keys
        if len(gen_keys) >= 50:
            complete = True
            return
        tm = 0
        while len(gen_keys) == 0:
            time.sleep(0.1)
            tm += 1
            if tm > 100:
                break
            else:
                continue
        if len(gen_keys) >= 50:
            complete = True
            return
    options = uc.ChromeOptions()
    options.add_argument("start-maximized")
    options.add_argument('--headless=new')
    driver = uc.Chrome(options=options, use_subprocess=True)
    driver.get("https://imstocker.com/en/keyworder")
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, "/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[2]").click()
            clicked = True
        except:
            continue
    driver.find_element(By.NAME, "q").send_keys(bankprompt)
    driver.find_element(By.XPATH, '/html/body/div/div/div/div[3]/div[1]/div[1]/div[2]/div/div/div/form/button').click()
    print('WAIT')
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH, '/html/body/div/div/div/div[3]/div[1]/div[2]/div[1]/button').click()
            clicked = True
        except:
            continue
    print('WAIT')
    clicked = False
    waittime = 50
    passed = 0
    while not clicked:
        print(passed)
        try:
            driver.find_element(By.XPATH,
                                '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[1]/div/div/div['
                                '1]/div/div/div[1]/span[2]/span/span[2]/a').click()
            clicked = True
        except:
            if passed > waittime:
                while not clicked:
                    try:
                        driver.find_element(By.XPATH,
                                            '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[1]/div/div/div['
                                            '1]/div/div/div[1]/span[2]/span/span/a').click()
                        clicked = True
                    except:
                        if passed > (waittime * 1.5):
                            symbam = int(len(bankprompt) / 4)
                            complete = True
                            return []
                        passed += 1
                        continue
            passed += 1
            continue
    driver.find_element(By.XPATH, '/html/body/div/div/div/div[4]/div[2]/div/div/div/div/div[2]').click()
    print('WAIT')
    clicked = False
    while not clicked:
        try:
            driver.find_element(By.XPATH,
                                '/html/body/div/div/div/div[3]/div[1]/div[2]/div[3]/div[1]/div/div['
                                '1]/div/div/div/div[2]/div/button[1]').click()
            clicked = True
        except:
            continue
    if ukeys != "":
        keywords = ukeys.split(", ")
    else:
        keywords = []
    for i in range(100):
        if i % 2 == 1:
            try:
                path = '/html/body/div/div/div/div[3]/div[1]/div[2]/div[3]/div[1]/div/div[2]/div/div/div/div/div/div/div[' \
                       '1]/div[2]/div[1]/div[1]/div/div[1]/div/div/div/span/span[' + str(
                    i) + ']'
                element = driver.find_element(By.XPATH, path)
            except:
                symbam = int(len(bankprompt) / 4)
                complete = True
                return []
            word = driver.execute_script("return arguments[0].childNodes[0].textContent", element)
            word = word.replace("- ", '')
            if word not in keywords:
                keywords.append(word)
            print('got:', word)
    if len(keywords) > 50:
        del keywords[50:len(keywords)]
    if 'photography' in keywords:
        keywords.remove('photography')
        keywords.append('photography')
    if 'horizontal' in keywords:
        keywords.remove('horizontal')
        keywords.append('horizontal')
    if 'color image' in keywords:
        keywords.remove('color image')
        keywords.append('color image')
    print(len(keywords), keywords)
    complete = True
    return keywords

def try_again(prompt, keys_amount):
    asyncio.run(get_keywords(prompt, keys_amount))

def extract_thumbnail(video_path, thumbnail_path, frame_number=0):
    cap = cv2.VideoCapture(video_path)

    # Check if the video file is opened successfully
    if not cap.isOpened():
        print("Error: Could not open video file.")
        return

    # Set frame position to the desired frame number
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

    # Read the frame
    ret, frame = cap.read()

    # Check if the frame is read successfully
    if not ret:
        print("Error: Could not read frame.")
        return

    # Save the frame as an image
    cv2.imwrite(thumbnail_path, frame)

    # Release the video capture object
    cap.release()

    print(f"Thumbnail extracted and saved as {thumbnail_path}")

def image_recognition(path, isfiles, userprompt, userkeys, mode, instruct):
    global imgs, gen_keys, compl, ukeys, workmode, bankprompt, keywords, complete
    processed = 0
    total = 0
    ws = ctk.CTk()
    ws.eval('tk::PlaceWindow . center')
    ws.geometry('360x100')
    ws.iconbitmap("logo.ico")
    ws.title('Stocks AI-Keyworder')
    txt = 'processed: ' + str(processed) + '/' + str(total)
    title_label = ctk.CTkLabel(ws, text=txt, font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    eta = ctk.CTkLabel(ws, text=txt, font=ctk.CTkFont(size=20, weight='bold'))
    eta.pack(padx=10, pady=(40, 10))
    ws.resizable(width=False, height=False)
    ws.update()

    def image_to_prompt(image, mod):
        ci.config.chunk_size = 2048 if ci.config.clip_model_name == "ViT-L-14/openai" else 1024
        ci.config.flavor_intermediate_count = 2048 if ci.config.clip_model_name == "ViT-L-14/openai" else 1024
        image = image.convert('RGB')
        if mod == 'best':
            return ci.interrogate(image)
        elif mod == 'classic':
            return ci.interrogate_classic(image)
        elif mod == 'fast':
            return ci.interrogate_fast(image)

    # @title Image to prompt! 🖼️ -> 📝
    folder_path = path  # @param {type:"string"}
    prompt_mode = 'fast'  # @param ["best","fast","classic","negative"]
    if isfiles:
        total = len(path)
        vids = []
        imgs = []
        for i in range(len(path)):
            if str(path[i]).endswith('.mp4') or str(path[i]).endswith('.MP4') or str(path[i]).endswith('.avi') or str(path[i]).endswith('.MOV') or str(path[i]).endswith('.mov') or str(path[i]).endswith('.mkv'):
                vids.append(path[i])
            else:
                imgs.append(path[i])

    ci.config.quiet = True
    if not isfiles:
        files = [f for f in os.listdir(folder_path) if f.endswith('.jpg') or f.endswith('.JPG') or f.endswith('.png')] if os.path.exists(
            folder_path) else []
        total = len(files)
    else:
        files = imgs
    txt = 'processed: ' + str(processed) + '/' + str(total)
    title_label.configure(text=txt)
    ws.update()
    print(files)
    for idx, file in enumerate(tqdm(files, desc='Generating prompts')):
        if idx > 0 and idx % 100 == 0:
            clear_output(wait=True)
        if not isfiles:
            file = os.path.join(folder_path, file)
        print(file)
        image = Image.open(file).convert('RGB')
        imageexif = pyexiv2.Image(file)

        prompt = image_to_prompt(image, prompt_mode)
        prompt = re.sub(r"by .*?, ", "", prompt)
        prompt = re.sub(r"featured on .*?, ", "", prompt)
        prompt = prompt.replace('shutterstock', '')
        prompt = prompt.replace('inspired', '')
        prompt = prompt.replace('unsplash', '')
        origprompt = prompt
        bankprompt = prompt
        compl = False
        complete = False
        if userprompt != '':
            prompt = "Brief image description: '" + userprompt + "', " + prompt
        print(prompt)
        ukeys = userkeys
        workmode = mode
        t = threading.Thread(target=get_keywords)
        t.start()
        name = asyncio.run(improvename(prompt, origprompt, mode, instruct))
        global keywords
        print('next')
        if mode == 'ai':
            while not compl:
                continue
            print(len(gen_keys))
            if len(gen_keys) < 50:
                for i in range(50 - len(gen_keys)):
                    if keywords[i] not in gen_keys:
                        gen_keys.append(keywords[i])
            if userkeys != "":
                uk = userkeys.split(", ")
                gen_keys = uk + gen_keys
            gen_keys = gen_keys[:50]
            print(gen_keys)
            print(len(gen_keys))
            keywords = gen_keys
        elif mode == 'combined':
            while not compl:
                continue
            while not complete:
                continue
            print(len(gen_keys))
            print(len(keywords))
            gk = gen_keys[:5]
            gen_keys = gen_keys[5:]
            gen_keys += keywords
            keywords = []
            word_counter = Counter(gen_keys)
            sorted_words_desc = sorted(word_counter.items(), key=lambda item: item[1], reverse=True)
            for word, count in sorted_words_desc:
                print(f"{word}: {count}")
                if word not in keywords:
                    keywords.append(word)
            keywords = gk + keywords
            if userkeys != "":
                uk = userkeys.split(", ")
                keywords = uk + keywords
            keywords = keywords[:50]
            print(keywords)

        thumb = image.copy()
        thumb.thumbnail([256, 256])
        display(thumb)

        pyexiv2.set_log_level(4)
        imageexif.clear_exif()
        imageexif.clear_comment()
        time.sleep(0.02)
        imageexif.modify_exif({'Exif.Image.XPKeywords': ';'.join(keywords)})
        imageexif.modify_exif({'Exif.Image.XPTitle': name})
        imageexif.modify_exif({'Exif.Image.XPSubject': name})
        imageexif.close()
        print('image completed')
        processed += 1
        txt = 'processed: ' + str(processed) + '/' + str(total)
        title_label.configure(text=txt)
        ws.update()

    print('Image files were finished')

    if False:
        if not isfiles:
            files = [f for f in os.listdir(folder_path) if f.endswith('.mp4') or f.endswith('.MP4') or f.endswith('.MOV') or f.endswith('.avi') or f.endswith('.mkv') or f.endswith('.mov')] if os.path.exists(
            folder_path) else []
            total = len(files)
        else:
            files = vids
        txt = 'processed: ' + str(processed) + '/' + str(total)
        title_label.configure(text=txt)
        ws.update()
        for idx, file in enumerate(tqdm(files, desc='Generating prompts')):
            if idx > 0 and idx % 100 == 0:
                clear_output(wait=True)
            if not isfiles:
                file = os.path.join(folder_path, file)
            print(file)
            thumb = str(file.split('.', 1)[0] + '.jpg')
            print(thumb, file)
            extract_thumbnail(file, thumb)
            time.sleep(0.1)
            image = Image.open(thumb).convert('RGB')
            imageexif = pyexiv2.Image(thumb)

            prompt = image_to_prompt(image, prompt_mode)
            prompt = re.sub(r"by .*?, ", "", prompt)
            prompt = re.sub(r"featured on .*?, ", "", prompt)
            prompt = prompt.replace('shutterstock', '')
            prompt = prompt.replace('inspired', '')
            prompt = prompt.replace('unsplash', '')
            if userprompt != '':
                prompt = "Brief image description: '" + userprompt + "', " + prompt
            print(prompt)
            name = asyncio.run(improvename(prompt))
            keywords = asyncio.run(get_keywords(name, 50))
            thumb = image.copy()
            thumb.thumbnail([256, 256])
            display(thumb)

            pyexiv2.set_log_level(4)
            imageexif.clear_exif()
            imageexif.clear_comment()
            time.sleep(0.02)
            imageexif.modify_exif({'Exif.Image.XPKeywords': ';'.join(keywords)})
            imageexif.modify_exif({'Exif.Image.XPTitle': name})
            imageexif.modify_exif({'Exif.Image.XPSubject': name})
            imageexif.close()
            print('image completed')
            processed += 1
            txt = 'processed: ' + str(processed) + '/' + str(total)
            title_label.configure(text=txt)
            ws.update()
            print('Video files were finished')

    messagebox.showinfo("AI-keyworder", "Process is finished")
    ws.destroy()


def folder():
    path = filedialog.askdirectory()
    ftype = simpledialog.askstring('FILE TYPE', 'Enter File Type (Photo, Video, Timelapse)', parent=root)
    userprompt = simpledialog.askstring('CUSTOM PROMPT', 'Enter Custom Prompt (Leave blank to generate the prompt)', parent=root)
    userkeys = simpledialog.askstring('CUSTOM KEYWORDS', 'Enter Custom Keywords', parent=root)
    mode = simpledialog.askstring('MODE', 'Bank, AI, Combined', parent=root)
    if mode == '':
        mode = 'combined'
    mode = mode.lower()
    instruct = simpledialog.askstring('AI INSTRUCTIONS', 'Write your instructions for AI here', parent=root)
    instruct = instruct.upper()
    root.lift()
    userprompt = ftype + ' of ' + userprompt
    global clear_output, display, os, Image, pyexiv2
    image_recognition(path, False, userprompt, userkeys, mode, instruct)

def files():
    path = filedialog.askopenfilenames()
    ftype = simpledialog.askstring('FILE TYPE', 'Enter File Type (Photo, Video, Timelapse)', parent=root)
    userprompt = simpledialog.askstring('CUSTOM PROMPT', 'Enter Custom Prompt (Leave blank to generate the prompt)', parent=root)
    userkeys = simpledialog.askstring('CUSTOM KEYWORDS', 'Enter Custom Keywords', parent=root)
    mode = simpledialog.askstring('MODE', 'Bank, AI, Combined', parent=root)
    if mode == '':
        mode = 'combined'
    mode = mode.lower()
    instruct = simpledialog.askstring('AI INSTRUCTIONS', 'Write your instructions for AI here', parent=root)
    instruct = instruct.upper()
    root.lift()
    userprompt = ftype + ' of ' + userprompt
    global clear_output, display, os, Image, pyexiv2
    image_recognition(path, True, userprompt, userkeys, mode, instruct)


def finish():
    sys.exit()


def mainscreen():
    global done, root
    root = ctk.CTk()
    root.eval('tk::PlaceWindow . center')
    root.geometry('680x320')
    root.iconbitmap("logo.ico")
    root.title('Stocks AI-Keyworder')
    title_label = ctk.CTkLabel(root, text='Stocks AI-Keyworder', font=ctk.CTkFont(size=30, weight='bold'))
    title_label.pack(padx=10, pady=(40, 20))
    sfb = ctk.CTkButton(root, text='Select Folder', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=folder)
    sfb.pack(pady=5)
    sfib = ctk.CTkButton(root, text='Select Files', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=files)
    sfib.pack(pady=5)
    bfb = ctk.CTkButton(root, text='Exit', font=ctk.CTkFont(size=20, weight='bold'), width=400, height=50,
                        fg_color='#FF6600', hover_color='#b54800', command=finish)
    bfb.pack(pady=5)
    root.resizable(width=False, height=False)
    root.mainloop()


time.sleep(0.2)
loadscreen()
mainscreen()
