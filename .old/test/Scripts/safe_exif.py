"""
Ultra-safe EXIF metadata handling module.
This module provides a robust way to update EXIF metadata that works across different
systems, locales, and encodings.
"""
import os
import sys
import traceback
import logging
import time
import platform
import locale

# Try to import pyexiv2, but don't crash if it fails
try:
    import pyexiv2
    HAVE_PYEXIV2 = True
except ImportError:
    HAVE_PYEXIV2 = False
    print("WARNING: pyexiv2 not available, using fallback mechanism")

# Emergency logging function
def write_emergency_log(message):
    """Write to emergency log file in case everything else fails."""
    try:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, "safe_exif_emergency.txt")
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
    except Exception as e:
        print(f"Failed to write to emergency log: {e}")
        # Last resort - try to write to current directory
        try:
            with open("safe_exif_emergency.txt", "a") as f:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        except Exception:
            pass  # Nothing more we can do

# Log system information
def log_system_info():
    """Log system information to help diagnose issues."""
    try:
        system_info = {
            'python_version': sys.version,
            'platform': platform.platform(),
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'default_encoding': sys.getdefaultencoding(),
            'filesystem_encoding': sys.getfilesystemencoding(),
            'locale': locale.getdefaultlocale(),
            'preferred_encoding': locale.getpreferredencoding()
        }
        
        write_emergency_log("=== System Information ===")
        for key, value in system_info.items():
            write_emergency_log(f"{key}: {value}")
        write_emergency_log("==========================")
        
        return system_info
    except Exception as e:
        write_emergency_log(f"Error logging system information: {e}")
        return {}

# Log system info when module is imported
log_system_info()

def safe_update_exif(file_path, keywords, title, description=None):
    """
    Ultra-safe function to update EXIF metadata.
    
    Args:
        file_path (str): Path to the image file
        keywords (list): List of keywords
        title (str): Title for the image
        description (str, optional): Description for the image
        
    Returns:
        bool: True if successful, False otherwise
    """
    write_emergency_log(f"Starting safe_update_exif for {os.path.basename(file_path)}")
    
    # Validate inputs
    if not os.path.exists(file_path):
        write_emergency_log(f"File does not exist: {file_path}")
        return False
    
    # Sanitize inputs to avoid encoding issues
    try:
        # Ensure keywords is a list
        if not isinstance(keywords, list):
            keywords = []
        
        # Sanitize keywords
        safe_keywords = []
        for kw in keywords:
            try:
                # Remove any non-printable characters
                safe_kw = ''.join(c for c in kw if c.isprintable())
                # Limit length
                if len(safe_kw) > 100:
                    safe_kw = safe_kw[:100]
                safe_keywords.append(safe_kw)
            except Exception as kw_err:
                write_emergency_log(f"Error sanitizing keyword: {kw_err}")
        
        # Sanitize title
        if title:
            try:
                # Remove any non-printable characters
                safe_title = ''.join(c for c in title if c.isprintable())
                # Limit length
                if len(safe_title) > 200:
                    safe_title = safe_title[:200]
            except Exception as title_err:
                write_emergency_log(f"Error sanitizing title: {title_err}")
                safe_title = ""
        else:
            safe_title = ""
        
        # Sanitize description
        if description:
            try:
                # Remove any non-printable characters
                safe_description = ''.join(c for c in description if c.isprintable())
                # Limit length
                if len(safe_description) > 500:
                    safe_description = safe_description[:500]
            except Exception as desc_err:
                write_emergency_log(f"Error sanitizing description: {desc_err}")
                safe_description = ""
        else:
            safe_description = ""
    except Exception as sanitize_err:
        write_emergency_log(f"Error sanitizing inputs: {sanitize_err}")
        return False
    
    # Try to update EXIF using pyexiv2
    if HAVE_PYEXIV2:
        try:
            return _update_with_pyexiv2(file_path, safe_keywords, safe_title, safe_description)
        except Exception as pyexiv2_err:
            write_emergency_log(f"Error using pyexiv2: {pyexiv2_err}")
            write_emergency_log(traceback.format_exc())
            # Fall through to fallback mechanism
    
    # Fallback mechanism if pyexiv2 fails or is not available
    try:
        return _fallback_update_exif(file_path, safe_keywords, safe_title, safe_description)
    except Exception as fallback_err:
        write_emergency_log(f"Error in fallback mechanism: {fallback_err}")
        write_emergency_log(traceback.format_exc())
        return False

def _update_with_pyexiv2(file_path, keywords, title, description=None):
    """Update EXIF metadata using pyexiv2."""
    write_emergency_log(f"Using pyexiv2 for {os.path.basename(file_path)}")
    
    image_exif = None
    success = False
    
    try:
        # Create a backup of the file first
        try:
            backup_path = file_path + ".bak"
            import shutil
            shutil.copy2(file_path, backup_path)
            write_emergency_log(f"Created backup at {os.path.basename(backup_path)}")
        except Exception as backup_err:
            write_emergency_log(f"Failed to create backup: {backup_err}")
        
        # Open the image
        image_exif = pyexiv2.Image(file_path)
        write_emergency_log(f"Successfully opened image with pyexiv2")
        
        # Prepare EXIF data
        exif_data = {}
        
        # Add keywords
        if keywords:
            try:
                keywords_str = ';'.join(keywords)
                exif_data['Exif.Image.XPKeywords'] = keywords_str
                write_emergency_log(f"Added {len(keywords)} keywords")
            except Exception as kw_err:
                write_emergency_log(f"Error adding keywords: {kw_err}")
        
        # Add title
        if title:
            try:
                exif_data['Exif.Image.XPTitle'] = title
                exif_data['Exif.Image.XPSubject'] = title
                write_emergency_log(f"Added title: {title[:30]}...")
            except Exception as title_err:
                write_emergency_log(f"Error adding title: {title_err}")
        
        # Add description
        if description:
            try:
                exif_data['Exif.Image.ImageDescription'] = description
                exif_data['Exif.Photo.UserComment'] = description
                write_emergency_log(f"Added description: {description[:30]}...")
            except Exception as desc_err:
                write_emergency_log(f"Error adding description: {desc_err}")
        
        # Modify EXIF
        try:
            write_emergency_log("Calling modify_exif")
            image_exif.modify_exif(exif_data)
            write_emergency_log("modify_exif completed successfully")
            success = True
        except Exception as modify_err:
            write_emergency_log(f"Error in modify_exif: {modify_err}")
            write_emergency_log(traceback.format_exc())
            success = False
    
    except Exception as e:
        write_emergency_log(f"Error in _update_with_pyexiv2: {e}")
        write_emergency_log(traceback.format_exc())
        success = False
    
    finally:
        # Close the image
        if image_exif:
            try:
                write_emergency_log("Closing image")
                image_exif.close()
                write_emergency_log("Image closed successfully")
            except Exception as close_err:
                write_emergency_log(f"Error closing image: {close_err}")
                success = False
    
    # If successful, remove backup
    if success:
        try:
            backup_path = file_path + ".bak"
            if os.path.exists(backup_path):
                os.remove(backup_path)
                write_emergency_log("Removed backup file")
        except Exception as rm_err:
            write_emergency_log(f"Error removing backup: {rm_err}")
    
    return success

def _fallback_update_exif(file_path, keywords, title, description=None):
    """Fallback mechanism to update EXIF metadata."""
    write_emergency_log(f"Using fallback mechanism for {os.path.basename(file_path)}")
    
    # Try using exiftool if available
    try:
        import subprocess
        
        # Check if exiftool is available
        try:
            subprocess.run(['exiftool', '-ver'], capture_output=True, check=True)
            have_exiftool = True
            write_emergency_log("exiftool is available")
        except (subprocess.SubprocessError, FileNotFoundError):
            have_exiftool = False
            write_emergency_log("exiftool is not available")
        
        if have_exiftool:
            # Build command
            cmd = ['exiftool']
            
            # Add keywords
            if keywords:
                keywords_str = ', '.join(keywords)
                cmd.extend(['-Keywords=' + keywords_str])
            
            # Add title
            if title:
                cmd.extend(['-Title=' + title])
            
            # Add description
            if description:
                cmd.extend(['-Description=' + description])
            
            # Add file path
            cmd.append(file_path)
            
            # Run command
            write_emergency_log(f"Running exiftool command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                write_emergency_log("exiftool completed successfully")
                return True
            else:
                write_emergency_log(f"exiftool failed: {result.stderr}")
                return False
    
    except Exception as exiftool_err:
        write_emergency_log(f"Error using exiftool: {exiftool_err}")
    
    # If we get here, all methods have failed
    write_emergency_log("All metadata update methods failed")
    return False
