import logging
import os
import re
from os import path
from logging import debug, warning, error
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
import cv2
from PIL import Image
import pyexiv2
from config import API_KEY, MODEL
from Scripts.utils import check_for_matching_video

# Global variables to store processing results and errors
glob_keys = []
glob_titles = []
errs = 0
errs_files = []

def ai_keywording(file_path, filetype, custom_prompt):
    from Scripts.error_logger import log_error, log_warning, log_info
    global glob_titles, glob_keys, errs, errs_files

    # Log the start of processing
    log_info(f"Starting AI keywording for {file_path}")

    try:
        genai.configure(api_key=API_KEY)

        is_video = False
        video_path_for_frames = None # Use a different variable to avoid confusion with file_path if it's an image
        file_extension = os.path.splitext(file_path)[1].lower()

        if file_extension in ('.mp4', '.mov', '.avi', '.wmv'):
            is_video = True
            video_path_for_frames = file_path
            log_info(f"Processing video file: {file_path}")
        else:
            has_matching_video, matching_video = check_for_matching_video(file_path)
            if has_matching_video and matching_video:
                is_video = True # We'll use frames from the video even if the primary input is an image
                video_path_for_frames = matching_video
                log_info(f"Processing image with matching video: {file_path} (video: {matching_video})")
            else:
                log_info(f"Processing image file: {file_path}")

        gemini_parts_list = []
        text_prompt = (
            f"""You are an AI expert stock media keyworder. Your goal is to generate a description and EXACTLY 50 unique, commercially valuable keywords a buyer would use. Emulate the style of top-performing stock media.

**REFERENCE EXAMPLE - KEYWORD STYLE & CONTENT (Aerial Amazon Shot):**
(Keywords like: amazon, forest, brazil, rainforest, amazonia, america, river, nature, south, landscape, jungle, brasil, peru, aerial, manaus, latin, rain, tree, water, ecuador, top, valley, sunset, above, drone, scenery, venezuela, sunrise, travel, national, overview, green, tropical, natural, dusk, latino, exotic, outdoor, amazonas, anavilhanas, rio negro, island, flooded forest, archipelago, canopy, environment, wilderness, remote, park...)
*Notice the mix of specific subjects, the primary location (Brazil), and related relevant countries (Peru, Ecuador, Venezuela), plus regional terms (Amazonia, South America).*

**YOUR TASK:**
Analyze the provided {filetype if filetype else "asset"}. {f"Asset context: {custom_prompt}" if custom_prompt else ""}
For videos, consider start/end frames for progression.

**Description Guidelines (15-30 words):**
*   Factual, concise: Main subject(s), key action/scene, specific visible named location(s) (e.g., "Anavilhanas, Rio Negro, Brazil").
*   AVOID: Subjective adjectives (stunning, beautiful, rich), fluff (pristine, scenic, paradise, tranquility), technical/photography terms (latitude, longitude, exposure, perspective), academic jargon (biodiversity, ecosystem, topography, habitat, flora, fauna, seasonal), and filler words (photo, image, showing).

**Keyword Guidelines (Exactly 50, numbered 1-50):**
1.  **CONTENT & PRIORITY:**
    *   **Core (Top 15-20):**
        *   Primary visual subjects (e.g., forest, river, building, person).
        *   Specific visible named locations (e.g., Anavilhanas, Rio Negro, Eiffel Tower).
        *   Primary country/city of the asset.
        *   Broader, highly relevant geographical terms (e.g., Amazonia, South America, Europe). These should be high in the list.
    *   **Supporting:** Other distinct visible elements, composition (aerial, drone), general scene descriptors (jungle, urban, travel, nature - if truly applicable and concrete).
    *   **Geographic Expansion (to help reach 50 valuable keywords):** If the asset is from a well-known region (like the Amazon, Alps, Sahara, etc.), include other commonly associated and searched-for countries or specific sub-regions as keywords. Refer to the Reference Example's inclusion of multiple countries for an Amazon shot.
2.  **STYLE:**
    *   Primarily single, impactful, concrete words.
    *   Common 2-word phrases for locations or very common terms (e.g., "Rio Negro", "South America", "rain forest"). DO NOT MERGE WORDS.
3.  **CRITICALLY AVOID AS KEYWORDS:**
    *   The specific words/categories listed in the Description's AVOID section (biodiversity, latitude, longitude, exposure, perspective, seasonal, rich, pristine, paradise, tranquility, flora, fauna, ecosystem, etc.).
    *   Vague or unsearchable phrases.
    *   Duplicates.

**Output Format (Follow EXACTLY):**
Description: [Your 15-30 word description here]
Keywords: 1. keyword, 2. keyword, ..., 50. keyword
"""
        )
        gemini_parts_list.append(text_prompt)

        pil_images_to_send = []

        if is_video and video_path_for_frames:
            logging.info(f"Processing video frames from: {video_path_for_frames}")
            cap = cv2.VideoCapture(video_path_for_frames)
            if not cap.isOpened():
                raise ValueError(f"Could not open video file: {video_path_for_frames}")

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0: fps = 30

            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            ret_first, first_frame_cv = cap.read()
            if not ret_first: raise ValueError(f"Could not read first frame from video: {video_path_for_frames}")

            last_frame_pos = max(0, min(total_frames - 1, int(total_frames - fps * 1))) # Frame from last second
            cap.set(cv2.CAP_PROP_POS_FRAMES, last_frame_pos)
            ret_last, last_frame_cv = cap.read()
            if not ret_last: raise ValueError(f"Could not read last frame from video: {video_path_for_frames}")
            cap.release()

            first_frame_rgb = cv2.cvtColor(first_frame_cv, cv2.COLOR_BGR2RGB)
            last_frame_rgb = cv2.cvtColor(last_frame_cv, cv2.COLOR_BGR2RGB)

            first_img_pil = Image.fromarray(first_frame_rgb)
            last_img_pil = Image.fromarray(last_frame_rgb)

            scaling_factor = 2
            first_img_pil = first_img_pil.resize((first_img_pil.width // scaling_factor, first_img_pil.height // scaling_factor), Image.LANCZOS)
            last_img_pil = last_img_pil.resize((last_img_pil.width // scaling_factor, last_img_pil.height // scaling_factor), Image.LANCZOS)

            pil_images_to_send.extend([first_img_pil, last_img_pil])

        # If the original file_path is an image (and not a video itself), add it.
        # This handles the case where an image has matching video (frames added above)
        # AND the image itself should also be sent. Or if it's just an image with no video.
        if file_extension not in ('.mp4', '.mov', '.avi', '.wmv'): # It's an image file
            if os.path.exists(file_path):
                try:
                    logging.info(f"Processing image file: {file_path}")
                    img = Image.open(file_path)
                    # Ensure image is in RGB mode if it's RGBA or P (palette) for consistency
                    if img.mode == 'RGBA' or img.mode == 'P':
                        img = img.convert('RGB')

                    scaling_factor = 2
                    new_size = (img.width // scaling_factor, img.height // scaling_factor)
                    img = img.resize(new_size, Image.LANCZOS)
                    pil_images_to_send.append(img)
                except FileNotFoundError:
                    logging.warning(f"Image file not found: {file_path}. Proceeding without this specific image.")
                except Exception as e:
                    logging.error(f"Error processing image file {file_path}: {e}. Proceeding without this image.")
            else:
                logging.warning(f"Image file {file_path} does not exist.")

        if not pil_images_to_send:
            logging.warning(f"No images or video frames could be prepared for {file_path}. Proceeding with text prompt only.")
        else:
            for pil_image in pil_images_to_send:
                 gemini_parts_list.append(pil_image)


        model = genai.GenerativeModel(MODEL)
        generation_config = genai.types.GenerationConfig(
            temperature=0.8, # Adjust creativity
            max_output_tokens=2048 # Increased to ensure full response
        )

        logging.info(f"Sending request to Gemini for {file_path} with {len(pil_images_to_send)} image(s) and model {MODEL}.")
        response = model.generate_content(
            contents=gemini_parts_list,
            generation_config=generation_config
        )

        if not response.parts and hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
            raise ValueError(f"Gemini API request blocked for {file_path}. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}")
        if not response.text: # Check if response.text is empty
             raise ValueError(f"Gemini API response for {file_path} is empty or malformed. Full response: {response}")


        content = response.text
        description_match = re.search(r'Description:\s*(.*?)\s*Keywords:', content, re.DOTALL | re.IGNORECASE)
        keywords_match = re.search(r'Keywords:\s*(.*)', content, re.DOTALL | re.IGNORECASE)

        if not description_match or not keywords_match:
            logging.error(f"Failed to parse Gemini API response for {file_path}. Raw response: {content}")
            raise ValueError("Failed to parse the API response for description and keywords.")

        description = description_match.group(1).strip()
        keywords_raw = keywords_match.group(1).strip()
        keywords_list = re.split(r'\s*,\s*|\s*\n\s*', keywords_raw)
        keywords = [re.sub(r'^\d+\.\s*', '', kw).strip().lower() for kw in keywords_list if kw.strip()]
        keywords = [kw for kw in keywords if kw]

        glob_titles.append(description)
        glob_keys.append(keywords)

        # Handle printing with proper encoding to avoid character encoding errors
        try:
            print(f"--- Successfully processed: {os.path.basename(file_path)} ---")
            print(f"Description: {description}")
            print(f"Keywords ({len(keywords)}): {', '.join(keywords)}")
            print("------------------------------------")
        except UnicodeEncodeError as e:
            # If we can't print to console due to encoding issues, just log it without the problematic text
            print(f"--- Successfully processed: {os.path.basename(file_path)} (text contains special characters) ---")
            print(f"Keywords count: {len(keywords)}")
            print("------------------------------------")
            # Log the encoding error but don't let it crash the application
            logging.warning(f"Console encoding issue with {file_path}: {e}")

        return keywords, description

    except google_exceptions.NotFound as e: # Specific for model not found
        try:
            error_msg = f"Gemini API Model Error (e.g., model not found) processing file {file_path} with model {MODEL}: {e}"
            logging.error(error_msg)
            from Scripts.error_logger import log_error
            log_error(error_msg, e, file_path)
        except UnicodeEncodeError:
            # Handle encoding errors in the error message itself
            error_msg = f"Gemini API Model Error processing file {os.path.basename(file_path)} with model {MODEL} (encoding error in message)"
            logging.error(error_msg)
            try:
                from Scripts.error_logger import log_error
                log_error(error_msg, e, file_path)
            except Exception as log_err:
                logging.error(f"Error logging to file: {log_err}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    except google_exceptions.GoogleAPIError as e:
        try:
            error_msg = f"Gemini API Error processing file {file_path}: {e}"
            logging.error(error_msg)
            from Scripts.error_logger import log_error
            log_error(error_msg, e, file_path)
        except UnicodeEncodeError:
            # Handle encoding errors in the error message itself
            error_msg = f"Gemini API Error processing file {os.path.basename(file_path)} (encoding error in message)"
            logging.error(error_msg)
            try:
                from Scripts.error_logger import log_error
                log_error(error_msg, e, file_path)
            except Exception as log_err:
                logging.error(f"Error logging to file: {log_err}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    except ValueError as e:
        try:
            error_msg = f"ValueError processing file {file_path}: {e}"
            logging.error(error_msg)
            from Scripts.error_logger import log_error
            log_error(error_msg, e, file_path)
        except UnicodeEncodeError:
            # Handle encoding errors in the error message itself
            error_msg = f"ValueError processing file {os.path.basename(file_path)} (encoding error in message)"
            logging.error(error_msg)
            try:
                from Scripts.error_logger import log_error
                log_error(error_msg, e, file_path)
            except Exception as log_err:
                logging.error(f"Error logging to file: {log_err}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    except Exception as e:
        try:
            error_msg = f"Generic error processing file {file_path}: {e}"
            logging.error(error_msg, exc_info=True)
            from Scripts.error_logger import log_error
            log_error(error_msg, e, file_path)
        except UnicodeEncodeError:
            # Handle encoding errors in the error message itself
            error_msg = f"Generic error processing file {os.path.basename(file_path)} (encoding error in message)"
            logging.error(error_msg)
            try:
                from Scripts.error_logger import log_error
                log_error(error_msg, e, file_path)
            except Exception as log_err:
                logging.error(f"Error logging to file: {log_err}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None

def update_exif(file_path, keywords, title):
    """
    Update image metadata (EXIF only) with the given keywords and title.
    This is a simplified, more robust implementation based on the working version.
    Returns True on success, False on failure.
    """
    # Import necessary modules
    import logging
    import traceback
    import os.path
    import sys
    import shutil  # For creating backup files

    # Try to import the error logger, but don't crash if it fails
    try:
        from Scripts.error_logger import log_error, log_warning, log_info, log_system_info
        has_error_logger = True
        # Log system information on first call to help diagnose machine-specific issues
        if not hasattr(update_exif, "_system_info_logged"):
            log_system_info()
            update_exif._system_info_logged = True
    except ImportError:
        has_error_logger = False
        print("Warning: Could not import error_logger module. Falling back to standard logging.")

    # Helper functions to handle logging with or without error_logger
    def log_info_safe(message):
        try:
            print(message)
            if has_error_logger:
                log_info(message)
            else:
                logging.info(message)
        except Exception as e:
            print(f"Error in log_info_safe: {e}")

    def log_error_safe(message, exception=None, file_path=None):
        try:
            print(message)
            if exception:
                print(traceback.format_exc())
            if has_error_logger:
                log_error(message, exception, file_path)
            else:
                logging.error(message)
        except Exception as e:
            print(f"Error in log_error_safe: {e}")

    def log_warning_safe(message, file_path=None):
        try:
            print(message)
            if has_error_logger:
                log_warning(message, file_path)
            else:
                logging.warning(message)
        except Exception as e:
            print(f"Error in log_warning_safe: {e}")

    # Function to safely get the basename of a file path
    def safe_basename(path_str):
        try:
            return os.path.basename(path_str)
        except Exception:
            return "unknown_file"

    image_exif = None
    backup_path = None
    success = False
    file_basename = safe_basename(file_path)

    try:
        # Log the operation start
        log_info_safe(f"Starting EXIF metadata update for {file_path}")

        # Check if file exists before attempting to open it
        if not os.path.exists(file_path):
            error_msg = f"File does not exist: {file_path}"
            log_error_safe(error_msg, None, file_path)
            return False

        # Create a backup of the file first
        try:
            backup_path = file_path + ".bak"
            shutil.copy2(file_path, backup_path)
            log_info_safe(f"Created backup at {os.path.basename(backup_path)}")
        except Exception as backup_err:
            log_warning_safe(f"Failed to create backup: {backup_err}", file_path)
            # Continue anyway - we'll be careful

        # Format keywords - simplified approach
        if keywords:
            # Clean up keywords format
            formatted_keywords = []
            for item in keywords:
                try:
                    if '. ' in item:
                        formatted_keywords.append(item.split('. ', 1)[-1])
                    else:
                        formatted_keywords.append(item)
                except Exception:
                    # Skip problematic items
                    pass

            # Remove trailing period from last keyword if present
            if formatted_keywords and formatted_keywords[-1].endswith('.'):
                formatted_keywords[-1] = formatted_keywords[-1].replace(".", "")

            # Join keywords with semicolons
            keywords_str = ';'.join(formatted_keywords)
        else:
            keywords_str = ''
            formatted_keywords = []

        # Prepare title string
        title_str = str(title) if title else ''

        # Set pyexiv2 log level to reduce verbosity
        pyexiv2.set_log_level(4)

        # Open the image with pyexiv2
        try:
            image_exif = pyexiv2.Image(file_path)
        except Exception as img_err:
            error_msg = f"Failed to open image for EXIF editing: {file_path}. Error: {img_err}"
            log_error_safe(error_msg, img_err, file_path)
            return False

        # Prepare EXIF data - simplified approach
        exif_data = {
            'Exif.Image.XPKeywords': keywords_str,
            'Exif.Image.XPTitle': title_str,
            'Exif.Image.XPSubject': title_str
        }

        # Modify EXIF
        try:
            log_info_safe(f"Modifying EXIF for {file_basename} with {len(formatted_keywords)} keywords")
            image_exif.modify_exif(exif_data)
            log_info_safe(f"EXIF metadata updated successfully for {file_basename}")
            success = True
        except Exception as exif_err:
            error_msg = f"Failed to modify EXIF for {file_basename}: {exif_err}"
            log_error_safe(error_msg, exif_err, file_path)
            success = False

            # Restore from backup if we have one and there was an error
            if backup_path and os.path.exists(backup_path):
                try:
                    # Make sure the image is closed before restoring
                    if image_exif:
                        try:
                            image_exif.close()
                            image_exif = None
                        except:
                            pass

                    # Restore the file
                    shutil.copy2(backup_path, file_path)
                    log_info_safe(f"Restored {file_basename} from backup after EXIF modification error")
                except Exception as restore_err:
                    log_error_safe(f"Error restoring from backup: {restore_err}", restore_err, file_path)

    except Exception as e:
        # Catch any other unexpected errors
        error_msg = f"Unexpected error updating metadata for {file_basename}: {e}"
        log_error_safe(error_msg, e, file_path)
        global errs, errs_files
        errs += 1
        errs_files.append(file_basename)
        success = False

    finally:
        # Ensure the image is closed
        if image_exif:
            try:
                image_exif.close()
                log_info_safe(f"Successfully closed pyexiv2 Image object for {file_basename}")
            except Exception as close_err:
                log_error_safe(f"Error closing pyexiv2 Image object: {close_err}", close_err, file_path)
                success = False  # Closing error likely means write failed

        # Remove backup if successful
        if success and backup_path and os.path.exists(backup_path):
            try:
                os.remove(backup_path)
                log_info_safe(f"Removed backup file for {file_basename}")
            except Exception as rm_err:
                log_warning_safe(f"Could not remove backup file: {rm_err}", file_path)

    return success  # Return overall success status