"""
Robust error logging system for the keywording application.
This module provides functions to log errors to both console and persistent files,
with multiple fallback mechanisms to ensure errors are always logged.
"""
import os
import sys
import platform
import locale
import traceback
import time
from datetime import datetime

# Create logs directory if it doesn't exist
try:
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
    os.makedirs(logs_dir, exist_ok=True)
except Exception as e:
    print(f"Error creating logs directory: {e}")
    # Fallback to current directory
    logs_dir = "."

# Define log file paths
error_log_file = os.path.join(logs_dir, 'robust_error.log')
info_log_file = os.path.join(logs_dir, 'robust_info.log')
warning_log_file = os.path.join(logs_dir, 'robust_warning.log')
emergency_log_file = os.path.join(logs_dir, 'robust_emergency.log')

def write_to_file(file_path, message):
    """Write a message to a file with multiple fallback mechanisms."""
    try:
        # Try with UTF-8 encoding first
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        return True
    except UnicodeEncodeError:
        try:
            # Try with system default encoding
            with open(file_path, 'a') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
            return True
        except Exception:
            try:
                # Try with ASCII encoding, replacing non-ASCII characters
                with open(file_path, 'a', encoding='ascii', errors='replace') as f:
                    f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
                return True
            except Exception as e:
                print(f"Failed to write to log file {file_path}: {e}")
                return False
    except Exception as e:
        print(f"Failed to write to log file {file_path}: {e}")
        return False

def write_emergency_log(message):
    """Write to emergency log file."""
    try:
        # Try to write to the emergency log file
        success = write_to_file(emergency_log_file, message)
        if not success:
            # Try to write to a file in the current directory
            write_to_file("emergency.log", message)
    except Exception as e:
        print(f"Failed to write to emergency log: {e}")

def log_system_info():
    """Log system information to help diagnose issues."""
    try:
        system_info = {
            'python_version': sys.version,
            'platform': platform.platform(),
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'default_encoding': sys.getdefaultencoding(),
            'filesystem_encoding': sys.getfilesystemencoding(),
            'locale': locale.getdefaultlocale(),
            'preferred_encoding': locale.getpreferredencoding()
        }
        
        write_emergency_log("=== System Information ===")
        for key, value in system_info.items():
            write_emergency_log(f"{key}: {value}")
        write_emergency_log("==========================")
        
        return system_info
    except Exception as e:
        write_emergency_log(f"Error logging system information: {e}")
        return {}

def log_error(message, exception=None, file_path=None):
    """Log an error with optional exception info and file path context."""
    try:
        # Format the message
        if file_path:
            try:
                formatted_message = f"[{os.path.basename(file_path)}] {message}"
            except Exception:
                formatted_message = f"[unknown_file] {message}"
        else:
            formatted_message = message
        
        # Print to console
        print(f"ERROR: {formatted_message}")
        
        # Log the exception traceback if provided
        if exception:
            print(traceback.format_exc())
            formatted_message += f"\nTraceback: {traceback.format_exc()}"
        
        # Write to log file
        write_to_file(error_log_file, formatted_message)
        
        return formatted_message
    except Exception as e:
        write_emergency_log(f"Error in log_error: {e}")
        write_emergency_log(f"Original message: {message}")
        return message

def log_warning(message, file_path=None):
    """Log a warning with optional file path context."""
    try:
        # Format the message
        if file_path:
            try:
                formatted_message = f"[{os.path.basename(file_path)}] {message}"
            except Exception:
                formatted_message = f"[unknown_file] {message}"
        else:
            formatted_message = message
        
        # Print to console
        print(f"WARNING: {formatted_message}")
        
        # Write to log file
        write_to_file(warning_log_file, formatted_message)
        
        return formatted_message
    except Exception as e:
        write_emergency_log(f"Error in log_warning: {e}")
        write_emergency_log(f"Original message: {message}")
        return message

def log_info(message):
    """Log an info message."""
    try:
        # Print to console
        print(f"INFO: {message}")
        
        # Write to log file
        write_to_file(info_log_file, message)
        
        return message
    except Exception as e:
        write_emergency_log(f"Error in log_info: {e}")
        write_emergency_log(f"Original message: {message}")
        return message

# Log system info when module is imported
log_system_info()
