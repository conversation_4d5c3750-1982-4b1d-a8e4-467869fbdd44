// START OF FILE web/preview_picker.js

// --- DOM Elements ---
const framePreview = document.getElementById('frame-preview');
const loadingIndicator = document.getElementById('loading-indicator');
const timelineSlider = document.getElementById('timeline-slider');
const currentFrameSpan = document.getElementById('current-frame');
const totalFramesSpan = document.getElementById('total-frames');
const savePreviewBtn = document.getElementById('save-preview-btn');
const statusBar = document.getElementById('status-bar');
const imageFilenameSpan = document.getElementById('image-filename');
const videoFilenameSpan = document.getElementById('video-filename');

let isFetchingFrame = false; // Prevent rapid requests

// --- Functions Called by Python ---

function updateStatus(message, type = 'info') {
    console.log(`Status [${type}]: ${message}`);
    statusBar.textContent = message;
    statusBar.className = 'status-bar'; // Reset
    if (type && type !== 'info') {
        statusBar.classList.add(type);
    }
}

// Called by Python after getting video info
function setVideoInfo(info) {
    console.log("Received video info:", info);
    if (info && info.frameCount > 0) {
        totalFramesSpan.textContent = info.frameCount - 1; // Frame count is 0-based index
        timelineSlider.max = info.frameCount - 1;
        timelineSlider.disabled = false;
        savePreviewBtn.disabled = false; // Enable save now that we have info
        imageFilenameSpan.textContent = info.imageFilename || '?';
        videoFilenameSpan.textContent = info.videoFilename || '?';
        updateStatus(`Video loaded: ${info.frameCount} frames. Ready.`, 'success');
        // Request the first frame
        requestFrame(0);
    } else {
        updateStatus('Error: Could not read video properties or video is empty.', 'error');
        timelineSlider.disabled = true;
        savePreviewBtn.disabled = true;
    }
}

// Called by Python with the base64 image data for a frame
function displayFrame(base64Data) {
    loadingIndicator.style.display = 'none';
    isFetchingFrame = false;
    if (base64Data) {
        framePreview.src = base64Data;
        framePreview.style.display = 'block'; // Ensure it's visible
    } else {
        updateStatus('Error retrieving frame.', 'error');
        framePreview.style.display = 'none'; // Hide image element on error
    }
}

// Called by Python after save attempt
function handleSaveResult(result) {
     savePreviewBtn.disabled = false; // Re-enable button
     savePreviewBtn.textContent = 'Save Preview';
     if (result && result.success) {
         updateStatus(`Preview saved successfully to ${result.filename}! Closing app...`, 'success');
         // Close the window after a short delay
         setTimeout(() => {
             window.close(); // Standard way to close a window opened by script/user action
             // If window.close() doesn't work reliably with pywebview,
             // you might need an API call: pywebview.api.close_window()
         }, 1500);
     } else {
         const errorMsg = result ? result.error : 'Unknown error during save.';
         updateStatus(`Error saving preview: ${errorMsg}`, 'error');
     }
}


// --- Helper Functions ---

// Request a specific frame from Python backend
function requestFrame(frameNumber) {
    if (isFetchingFrame) return; // Don't make multiple requests at once

    isFetchingFrame = true;
    loadingIndicator.style.display = 'block';
    framePreview.style.display = 'none'; // Hide preview while loading
    updateStatus(`Loading frame ${frameNumber}...`, 'info');
    currentFrameSpan.textContent = frameNumber;
    // Call Python API function
    pywebview.api.get_frame_api(parseInt(frameNumber, 10));
}

// --- Event Listeners ---

timelineSlider.addEventListener('input', () => {
    const frameNum = timelineSlider.value;
    currentFrameSpan.textContent = frameNum;
    // Optional: Debounce this call if performance is an issue
    requestFrame(frameNum);
});

savePreviewBtn.addEventListener('click', () => {
    const frameNum = parseInt(timelineSlider.value, 10);
    updateStatus(`Saving frame ${frameNum}...`, 'info');
    savePreviewBtn.disabled = true; // Disable during save
    savePreviewBtn.textContent = 'Saving...';
    // Call Python API function
    pywebview.api.save_frame_api(frameNum);
});

// --- Initialization ---
document.addEventListener('DOMContentLoaded', () => {
    // Just set the initial status here, don't call Python yet.
    updateStatus("Waiting for backend...");
    console.log("DOM Content Loaded. Waiting for pywebviewready.");
});

// **MODIFIED**: Use pywebviewready to ensure API is available
window.addEventListener('pywebviewready', () => {
   console.log("Pywebview API is ready. Initializing video...");
   updateStatus("Initializing Preview Picker..."); // Now update status
   try {
        // Request video info from Python backend
        pywebview.api.initialize_video_api();
   } catch (error) {
       console.error("Error calling initialize_video_api:", error);
       updateStatus(`Error contacting backend: ${error}`, 'error');
       // Disable UI elements if the backend call fails immediately
       timelineSlider.disabled = true;
       savePreviewBtn.disabled = true;
   }
});


console.log("preview_picker.js loaded");

// END OF FILE web/preview_picker.js