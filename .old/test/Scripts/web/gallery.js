// START OF FILE web/gallery.js

// --- DOM Elements ---
const aiKeyworderBtn = document.getElementById('ai-keyworder-btn');
const openFolderBtn = document.getElementById('open-folder-btn');
const stockUploaderBtn = document.getElementById('stock-uploader-btn');
const reloadFolderBtn = document.getElementById('reload-folder-btn');
const statsBtn = document.getElementById('stats-btn');
const galleryGrid = document.getElementById('gallery-grid');
const selectPreviewBtn = document.getElementById('select-preview-btn');
const galleryPlaceholder = document.getElementById('gallery-placeholder');

// Metadata Panel Elements
const titleEntry = document.getElementById('title-entry');
const descEntry = document.getElementById('desc-entry');
const keywordsEntry = document.getElementById('keywords-entry');
const filetypeEntry = document.getElementById('filetype-entry');
const kwCountEntry = document.getElementById('kw-count-entry');
const autoKeywordBtn = document.getElementById('auto-keyword-btn');
const saveChangesBtn = document.getElementById('save-changes-btn');
const selectedImagePathInput = document.getElementById('selected-image-path');

const statusBar = document.getElementById('status-bar');

// --- Event Listeners ---
selectPreviewBtn.addEventListener('click', () => {
    const imagePath = selectedImagePathInput.value;
    if (!imagePath) {
        updateStatus('No image selected.', 'warning');
        return;
    }
    // Python API will handle finding the video and launching
    updateStatus('Opening Preview Picker...', 'info');
    pywebview.api.open_preview_picker_api(imagePath); // <-- New API function call
});
reloadFolderBtn.addEventListener('click', () => {
    if (reloadFolderBtn.disabled) return; // Prevent clicking if disabled

    updateStatus('Requesting folder reload...', 'info');
    reloadFolderBtn.disabled = true; // Disable immediately
    // Ask Python backend to reload its currently known folder
    pywebview.api.reload();
});
function enableReloadButton() {
    reloadFolderBtn.disabled = false;
}

function disableReloadButton() {
    reloadFolderBtn.disabled = true;
}
openFolderBtn.addEventListener('click', () => {
    updateStatus('Requesting folder selection...', 'info');
    pywebview.api.request_folder_dialog();
});

aiKeyworderBtn.addEventListener('click', () => {
    updateStatus('Launching AI Keyworder...', 'info');
    pywebview.api.open_ai_keyworder();
});

stockUploaderBtn.addEventListener('click', () => {
    updateStatus('Launching Stock Uploader...', 'info');
    pywebview.api.open_stock_uploader();
});

saveChangesBtn.addEventListener('click', () => {
    const imagePath = selectedImagePathInput.value;
    if (!imagePath) {
        updateStatus('No image selected to save metadata for.', 'warning');
        return;
    }
    updateStatus('Sending metadata to save...', 'info');
    const metadata = {
        imagePath: imagePath,
        title: titleEntry.value.trim(),
        description: descEntry.value.trim(),
        keywords: keywordsEntry.value.trim() // Send as is, Python will parse
    };
    pywebview.api.save_metadata_api(JSON.stringify(metadata));
});

autoKeywordBtn.addEventListener('click', () => {
    const imagePath = selectedImagePathInput.value;
    if (!imagePath) {
        updateStatus('No image selected for auto keywording.', 'warning');
        return;
    }
    updateStatus(`Starting AI Keywording for ${imagePath.split(/[\\/]/).pop()}...`, 'info');
    autoKeywordBtn.disabled = true; // Disable while running
    pywebview.api.trigger_auto_keyword_api(imagePath);
});

// Use event delegation for gallery item clicks
galleryGrid.addEventListener('click', (event) => {
    const targetItem = event.target.closest('.gallery-item'); // Find the parent gallery item
    if (targetItem && targetItem.dataset.path) {
        handleImageSelection(targetItem);
    }
});


// --- Functions Called by Python API ---

function updateStatus(message, type = 'info') {
    console.log(`Status [${type}]: ${message}`);
    statusBar.textContent = message;
    // Remove previous classes and add the new one
    statusBar.className = 'status-bar'; // Reset to base class
    if (type && type !== 'info') {
        statusBar.classList.add(type); // Add specific class (error, success, warning)
    }
}

function clearGallery() {
    galleryGrid.innerHTML = ''; // Clear existing content
    galleryPlaceholder.style.display = 'block'; // Show placeholder initially
    galleryPlaceholder.textContent = 'Select a folder to view images.';
}

function showNoImagesMessage() {
     clearGallery(); // Ensure it's empty
     galleryPlaceholder.style.display = 'block';
     galleryPlaceholder.textContent = 'No compatible image files found in the selected folder.';
}


function addImagesToGallery(imageBatchData) {
    // Hide placeholder if it's the first batch
    if (galleryPlaceholder.style.display !== 'none') {
        galleryPlaceholder.style.display = 'none';
    }

    const fragment = document.createDocumentFragment(); // More efficient for bulk adds

    imageBatchData.forEach(imgData => {
        const itemDiv = document.createElement('div');
        itemDiv.classList.add('gallery-item');
        itemDiv.dataset.path = imgData.path; // Store full path in data attribute

        // Add Image or Placeholder
        if (imgData.thumbnail) {
            const img = document.createElement('img');
            img.src = imgData.thumbnail; // Base64 data URL
            img.alt = imgData.filename;
            img.loading = 'lazy'; // Lazy load images for performance
            itemDiv.appendChild(img);
        } else {
            // Placeholder if thumbnail failed
            const placeholder = document.createElement('div');
            placeholder.classList.add('placeholder-icon');
            itemDiv.appendChild(placeholder);
        }

        // Add Filename
        const p = document.createElement('p');
        p.textContent = imgData.filename;
        p.title = imgData.filename; // Show full name on hover
        itemDiv.appendChild(p);

        // *** NEW: Add Info Icon and Tooltip if comment exists ***
        if (imgData.userComment && imgData.userComment.trim() !== '') {
            const infoIcon = document.createElement('span');
            infoIcon.classList.add('info-icon');
            infoIcon.textContent = 'ℹ️'; // The info emoji

            const tooltipText = document.createElement('span');
            tooltipText.classList.add('tooltip-text');
            tooltipText.textContent = imgData.userComment; // Set the comment text

            infoIcon.appendChild(tooltipText); // Add tooltip inside the icon span
            itemDiv.appendChild(infoIcon);    // Add the icon (with tooltip) to the item div
        }
        // *** END NEW CODE ***

        // --- NEW: Add Progress Bar ---
        // Get progress value from the image data (default to 0 if not provided)
        const progress = (imgData.progressValue !== undefined) ? imgData.progressValue : 0;
        const progressContainer = document.createElement('div');
        progressContainer.classList.add('progress-container');

        const progressBar = document.createElement('div');
        progressBar.classList.add('progress-bar');
        // Set the width relative to the progress (0 to 5 scaled to percentage)
        progressBar.style.width = (progress) + '%';
        progressBar.style.backgroundColor = (progress < 100) ? '#FF6600' : 'green';

        progressContainer.appendChild(progressBar);
        itemDiv.appendChild(progressContainer);
        // --- END NEW CODE ---

        fragment.appendChild(itemDiv);
    });

    galleryGrid.appendChild(fragment); // Append all new items at once
}

function displayMetadata(metadata) {
    if (!metadata) {
        clearMetadataForm();
        return;
    }
    titleEntry.value = metadata.title || '';
    descEntry.value = metadata.description || '';
    keywordsEntry.value = metadata.keywords || ''; // Expecting semicolon string
    const hasVideo = metadata.hasMatchingVideo || false; // Get flag from Python
    filetypeEntry.value = hasVideo ? 'Video' : (metadata.fileType || 'N/A');
    kwCountEntry.value = metadata.keywordsCount !== undefined ? metadata.keywordsCount : 'N/A';
    selectedImagePathInput.value = metadata.imagePath || '';

    // Enable/disable buttons based on loaded data and video flag
    saveChangesBtn.disabled = false;
    autoKeywordBtn.disabled = false;
    selectPreviewBtn.disabled = !hasVideo; // <-- Enable only if video exists
}

function clearMetadataForm() {
    titleEntry.value = '';
    descEntry.value = '';
    keywordsEntry.value = '';
    filetypeEntry.value = '';
    kwCountEntry.value = '';
    selectedImagePathInput.value = ''; // Clear selected path

    // Disable buttons
    saveChangesBtn.disabled = true;
    autoKeywordBtn.disabled = true;
    selectPreviewBtn.disabled = true;
    // disableReloadButton(); // Reload button state depends on whether a folder IS loaded, not just selection cleared

     // Remove selection highlight from gallery
     const selected = galleryGrid.querySelector('.gallery-item.selected');
     if (selected) {
         selected.classList.remove('selected');
     }
}

function updateKeywordCount(count) {
    kwCountEntry.value = count;
}

// --- Helper Functions ---

function updateProgress(filename, newProgress) {
    // Find the gallery item with the matching filename
    const galleryItems = document.querySelectorAll('.gallery-item');

    galleryItems.forEach(item => {
        const fileNameElement = item.querySelector('p'); // Get filename text

        if (fileNameElement && fileNameElement.textContent === filename) {
            // Found the matching item, now update its progress bar
            const progressBar = item.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = ((newProgress / 5) * 100) + '%';
                progressBar.style.backgroundColor = (newProgress < 5) ? 'red' : 'green';
            }
        }
    });
}

function handleImageSelection(targetItem) {
    const imagePath = targetItem.dataset.path;

    // Update selection visual state
    const previouslySelected = galleryGrid.querySelector('.gallery-item.selected');
    if (previouslySelected) {
        previouslySelected.classList.remove('selected');
    }
    targetItem.classList.add('selected');

    // Store the selected path
    selectedImagePathInput.value = imagePath;

    // Request metadata from Python
    updateStatus(`Requesting metadata for ${imagePath.split(/[\\/]/).pop()}...`, 'info');
    // Disable buttons temporarily while loading
    saveChangesBtn.disabled = true;
    autoKeywordBtn.disabled = true;
    selectPreviewBtn.disabled = true;
    pywebview.api.select_image_api(imagePath);
}


// --- Initial State ---
document.addEventListener('DOMContentLoaded', () => {
    updateStatus("Application ready. Please open a folder.");
    clearMetadataForm();
    disableReloadButton();
});

console.log("gallery.js loaded");

// --- Error Indicator Functions ---

// Function to show the error indicator
function showErrorIndicator() {
    console.log("JS: Showing error indicator");
    const overlay = document.getElementById('error-overlay');
    if (overlay) {
        // Play error sound
        playErrorSound();

        overlay.classList.add('visible');
        // Automatically hide after 1.5 seconds
        setTimeout(() => {
            hideErrorIndicator();
        }, 1500);
    } else {
        console.error("Error overlay element not found!");
    }
}

// Function to play the error sound
function playErrorSound() {
    try {
        // Create an audio element
        const audio = new Audio('sounds/notification-error.mp3');
        // Set volume (0.0 to 1.0)
        audio.volume = 0.5;
        // Play the sound
        audio.play().catch(e => {
            // Handle any errors (e.g., file not found)
            console.warn("Could not play error sound:", e);
        });
    } catch (e) {
        console.warn("Error playing error sound:", e);
    }
}

// Function to hide the error indicator
function hideErrorIndicator() {
    console.log("JS: Hiding error indicator");
    const overlay = document.getElementById('error-overlay');
    if (overlay) {
        overlay.classList.remove('visible');
    }
}

// END OF FILE web/gallery.js