<!-- START OF FILE keyworder.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Keyworder</title>
    <link rel="stylesheet" href="key.css">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>


    <!-- Completion Indicator Overlay -->
    <div id="completion-overlay" class="completion-overlay">
        <div class="completion-circle">
            <svg width="40" height="40" viewBox="0 0 50 50">
                <path class="checkmark" d="M14,27 L22,35 L36,15" />
            </svg>
        </div>
    </div>

    <!-- Error Indicator Overlay -->
    <div id="error-overlay" class="error-overlay">
        <div class="error-circle">
            <svg width="40" height="40" viewBox="0 0 50 50">
                <path class="crossmark" d="M15,15 L35,35 M15,35 L35,15" />
            </svg>
        </div>
    </div>

    <!-- Progress Overlay -->
    <div id="progress-overlay" class="progress-overlay">
        <div class="progress-container">
            <div class="progress-bar-container">
                <div id="progress-bar" class="progress-bar"></div>
            </div>
            <div id="progress-text" class="progress-text">Processing...</div>
            <div id="progress-filename" class="progress-filename"></div>
        </div>
    </div>

    <div class="container">
        <h1 class="title">AI-Keyworder v2.0</h1>

        <p id="progress-label" class="progress-label">Drag image/video files, drop a folder, or click to browse</p>

        <div id="drop-area" class="drop-area">
            <p id="drop-label">Drop Files or Folder Here<br><span style="font-size: 0.85em;">or Click to Browse</span></p>
        </div>

        <!-- Area to display info about dropped files -->
        <div id="file-info-section" class="file-info-section hidden">
            <p id="file-count-label"></p>
            <!-- Optional: <ul id="file-list"></ul> -->
            <button id="clear-files-btn" class="btn-small btn-secondary">Change Files</button>
        </div>

        <!-- Settings Section - Initially Hidden -->
        <form id="keywording-settings-form">
            <div id="settings-section" class="settings-section hidden">
                <div class="settings-input-group">
                    <label for="file-type" class="settings-label">Video/Image Type:</label>
                    <input type="text" id="file-type" class="settings-input" placeholder="e.g., Aerial, Timelapse, Product Shot">
                </div>
                <div class="settings-input-group">
                    <label for="custom-prompt" class="settings-label">Specific Context/Details:</label>
                    <input type="text" id="custom-prompt" class="settings-input" placeholder="e.g., Bali, Snowy mountains, Minimalist interior">
                </div>
                <button type="submit" id="start-keywording-btn" class="btn settings-btn" disabled>Start Keywording</button>
            </div>
        </form>
        <!-- End Settings Section -->

        <button id="upload-btn" class="btn upload-btn">Upload to Stocks</button>
    </div>

    <script>
        // --- Javascript Functions ---

        // Function called by Python to update the progress label
        function updateProgressLabel(message, color = 'black') {
            const label = document.getElementById('progress-label');
            if (label) {
                label.textContent = message;
                label.style.color = color; // Apply color directly
                // Optionally add classes for more complex styling
                label.className = 'progress-label'; // Reset classes
                if (color === 'red') {
                    label.classList.add('error');
                } else if (color === 'green') {
                    label.classList.add('success');
                } else if (color === 'orange') {
                    label.classList.add('warning');
                } else if (color === 'blue') {
                    label.classList.add('info');
                }
            } else {
                console.error("Progress label element not found!");
            }
        }

        // Function called by Python to update the progress overlay
        function updateProgressOverlay(current, total, eta, filename = '') {
            const overlay = document.getElementById('progress-overlay');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const progressFilename = document.getElementById('progress-filename');

            if (!overlay || !progressBar || !progressText || !progressFilename) {
                console.error("Progress overlay elements not found!");
                return;
            }

            // Calculate percentage
            const percentage = total > 0 ? Math.round((current / total) * 100) : 0;

            // Update progress bar width
            progressBar.style.width = `${percentage}%`;

            // Update text
            progressText.textContent = `Processed ${current}/${total} - ETA: ${eta}`;
            progressFilename.textContent = filename || 'Processing...';

            // Show overlay if not already visible
            if (!overlay.classList.contains('visible')) {
                overlay.classList.add('visible');
            }
        }

        // Function to hide the progress overlay
        function hideProgressOverlay() {
            const overlay = document.getElementById('progress-overlay');
            if (overlay) {
                overlay.classList.remove('visible');
            }
        }

        // Function to show the progress overlay
        function showProgressOverlay() {
            console.log("JS: Showing progress overlay");
            const overlay = document.getElementById('progress-overlay');
            if (overlay) {
                overlay.classList.add('visible');
            } else {
                console.error("Progress overlay element not found!");
            }
        }

        // Function to hide the progress overlay
        function hideProgressOverlay() {
            console.log("JS: Hiding progress overlay");
            const overlay = document.getElementById('progress-overlay');
            if (overlay) {
                overlay.classList.remove('visible');
            }
        }

        // Function to show the completion indicator
        function showCompletionIndicator() {
            console.log("JS: Showing completion indicator");
            const progressOverlay = document.getElementById('progress-overlay');
            const completionOverlay = document.getElementById('completion-overlay');

            if (completionOverlay) {
                // Play completion sound
                playCompletionSound();

                // If progress overlay is visible, transition from it to completion
                if (progressOverlay && progressOverlay.classList.contains('visible')) {
                    // Add transition class to progress overlay
                    progressOverlay.classList.add('transitioning');

                    // After a short delay, hide progress and show completion
                    setTimeout(() => {
                        progressOverlay.classList.remove('visible');
                        progressOverlay.classList.remove('transitioning');
                        completionOverlay.classList.add('visible');

                        // Automatically hide completion after 1.5 seconds
                        setTimeout(() => {
                            hideCompletionIndicator();
                        }, 1500);
                    }, 400); // Short delay for transition effect
                } else {
                    // If progress overlay not visible, just show completion directly
                    completionOverlay.classList.add('visible');
                    // Automatically hide after 1.5 seconds
                    setTimeout(() => {
                        hideCompletionIndicator();
                    }, 1500);
                }
            } else {
                console.error("Completion overlay element not found!");
            }
        }

        // Function to play the completion sound
        function playCompletionSound() {
            try {
                // Create an audio element
                const audio = new Audio('sounds/notification-complete.mp3');
                // Set volume (0.0 to 1.0)
                audio.volume = 0.5;
                // Play the sound
                audio.play().catch(e => {
                    // Handle any errors (e.g., file not found)
                    console.warn("Could not play completion sound:", e);
                });
            } catch (e) {
                console.warn("Error playing completion sound:", e);
            }
        }

        // Function to hide the completion indicator
        function hideCompletionIndicator() {
            console.log("JS: Hiding completion indicator");
            const overlay = document.getElementById('completion-overlay');
            if (overlay) {
                overlay.classList.remove('visible');
            }
        }

        // Function to show the error indicator
        function showErrorIndicator() {
            console.log("JS: Showing error indicator");
            const progressOverlay = document.getElementById('progress-overlay');
            const errorOverlay = document.getElementById('error-overlay');

            // Hide progress overlay if it's visible
            if (progressOverlay && progressOverlay.classList.contains('visible')) {
                hideProgressOverlay();
            }

            if (errorOverlay) {
                // Play error sound
                playErrorSound();

                errorOverlay.classList.add('visible');
                // Automatically hide after 1.5 seconds
                setTimeout(() => {
                    hideErrorIndicator();
                }, 1500);
            } else {
                console.error("Error overlay element not found!");
            }
        }

        // Function to play the error sound
        function playErrorSound() {
            try {
                // Create an audio element
                const audio = new Audio('sounds/notification-error.mp3');
                // Set volume (0.0 to 1.0)
                audio.volume = 0.5;
                // Play the sound
                audio.play().catch(e => {
                    // Handle any errors (e.g., file not found)
                    console.warn("Could not play error sound:", e);
                });
            } catch (e) {
                console.warn("Error playing error sound:", e);
            }
        }

        // Function to hide the error indicator
        function hideErrorIndicator() {
            console.log("JS: Hiding error indicator");
            const overlay = document.getElementById('error-overlay');
            if (overlay) {
                overlay.classList.remove('visible');
            }
        }

        // Function called by Python (handle_drop) to show the settings inputs
        function showSettings() {
            console.log("JS: showSettings() called");
            const settingsSection = document.getElementById('settings-section');
            const dropArea = document.getElementById('drop-area');
            const startBtn = document.getElementById('start-keywording-btn');

            if (settingsSection) {
                settingsSection.classList.remove('hidden');
                // Enable the Start Keywording button
                if (startBtn) {
                    startBtn.disabled = false;
                    console.log("JS: Start keywording button enabled");
                }
                // Optional: Disable drop area while settings are shown
                if(dropArea) {
                    dropArea.classList.add('disabled');
                }
            } else {
                console.error("Settings section element not found!");
            }
        }
        function setButtonLoadingState(button, isLoading, loadingText = 'Processing...') {
            if (button) {
                button.disabled = isLoading;
                if (isLoading) {
                    button.dataset.originalText = button.innerHTML; // Store original text
                    button.innerHTML = `<span class="spinner"></span> ${loadingText}`; // Add a CSS class for spinner
                } else {
                    button.innerHTML = button.dataset.originalText || 'Submit'; // Restore or default
                }
            }
        }

         // Function called by Python (keywording_thread_target) to reset UI after processing
        function resetUIState() {
            console.log("JS: resetUIState() called");
            const settingsSection = document.getElementById('settings-section');
            const fileTypeInput = document.getElementById('file-type');
            const customPromptInput = document.getElementById('custom-prompt');
            const dropArea = document.getElementById('drop-area');
            const uploadBtn = document.getElementById('upload-btn');
            const startBtn = document.getElementById('start-keywording-btn');

            // Hide progress overlay if it's still visible
            hideProgressOverlay();

            // Hide settings
            if (settingsSection) {
                settingsSection.classList.add('hidden');
            }
            // Clear inputs
            if (fileTypeInput) fileTypeInput.value = '';
            if (customPromptInput) customPromptInput.value = '';
            // Re-enable the Start Keywording button after processing completes
            if (startBtn) {
                startBtn.disabled = false;
                setButtonLoadingState(startBtn, false);
                console.log("JS: Start keywording button re-enabled after processing");
            }
             // Re-enable drop area
            if(dropArea) {
                dropArea.classList.remove('disabled');
            }
            // The Upload to Stock button should always remain active
            // No need to modify its state here

            // Optionally reset progress label to default prompt after a short delay
            // setTimeout(() => {
            //     updateProgressLabel('Drag image files or a folder here', 'black');
            // }, 3000); // Reset after 3 seconds
        }


        // --- Event Listeners ---
        window.addEventListener('pywebviewready', () => {
            console.log("Pywebview ready!");

            const dropArea = document.getElementById('drop-area');
            const startBtn = document.getElementById('start-keywording-btn');
            const uploadBtn = document.getElementById('upload-btn');
            const keywordingForm = document.getElementById('keywording-settings-form');

            if (!dropArea) {
                console.error("Drop area element not found!");
                updateProgressLabel('Error: UI setup failed (drop area missing).', 'red');
                return;
            }
             if (!startBtn) {
                console.error("Start keywording button not found!");
                 updateProgressLabel('Error: UI setup failed (start button missing).', 'red');
            }
             if (!uploadBtn) {
                console.error("Upload button not found!");
                 updateProgressLabel('Error: UI setup failed (upload button missing).', 'red');
            }

            // Prevent form submission which might cause page reload
            if (keywordingForm) {
                keywordingForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    console.log("JS: Form submission prevented");
                    // The click handler on the button will handle the actual action
                });
            }


            // --- Drag and Drop Visuals (Handled purely in JS/CSS) ---
            dropArea.addEventListener('dragenter', (e) => {
                e.preventDefault();
                e.stopPropagation();
                 if (!dropArea.classList.contains('disabled')) { // Only show effect if not disabled
                    dropArea.classList.add('drag-over');
                }
            });

            dropArea.addEventListener('dragover', (e) => {
                e.preventDefault(); // Necessary to allow drop
                e.stopPropagation();
                 if (!dropArea.classList.contains('disabled')) {
                    dropArea.classList.add('drag-over'); // Keep class while hovering
                 }
            });

            dropArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                e.stopPropagation();
                // Check if the leave target is outside the drop area itself
                 if (e.relatedTarget === null || !dropArea.contains(e.relatedTarget)) {
                    dropArea.classList.remove('drag-over');
                 }
            });

            // The actual 'drop' event is handled by the Python DOM Event Handler (python_on_drop)
            // We just need to remove the visual feedback here.
            dropArea.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                dropArea.classList.remove('drag-over');
                console.log("JS: Drop event visually handled.");
                // Python handler (python_on_drop) bound via DOMEventHandler will execute now
            });

            // Add visual feedback for click events
            // The actual file dialog is handled by the Python DOM Event Handler (python_on_click)
            dropArea.addEventListener('click', (e) => {
                if (!dropArea.classList.contains('disabled')) {
                    // Add visual feedback
                    dropArea.classList.add('drag-over');
                    setTimeout(() => {
                        dropArea.classList.remove('drag-over');
                    }, 200); // Remove after a short delay for a "flash" effect
                    console.log("JS: Click event on drop area detected.");
                    // Python handler (python_on_click) bound via DOMEventHandler will execute now
                }
            });

            // --- Button Clicks ---
            if (startBtn) {
                startBtn.addEventListener('click', () => {
                    console.log("JS: Start Keywording button clicked.");
                    const fileTypeInput = document.getElementById('file-type');
                    const customPromptInput = document.getElementById('custom-prompt');

                    if (!fileTypeInput || !customPromptInput) {
                         console.error("Settings input elements not found!");
                         updateProgressLabel('Error: Could not read settings.', 'red');
                         return;
                    }

                    const fileType = fileTypeInput.value.trim();
                    const customPrompt = customPromptInput.value.trim();

                    // Disable the button and show loading state to prevent multiple clicks
                    startBtn.disabled = true;
                    setButtonLoadingState(startBtn, true, 'Processing...');
                    console.log("JS: Start keywording button disabled during processing");

                    // Update progress label immediately
                    updateProgressLabel('Starting keywording process...', 'blue');

                    // Show progress overlay
                    showProgressOverlay();
                    updateProgressOverlay(0, 1, 'estimating...', 'Starting process');

                    // Call the Python API function
                    window.pywebview.api.start_keywording_api(fileType, customPrompt).then(() => {
                        console.log("JS: Call to start_keywording_api successful.");
                        // Button will be re-enabled by resetUIState if needed
                    }).catch(err => {
                         console.error("JS: Error calling start_keywording_api:", err);
                         updateProgressLabel(`Error starting process: ${err}`, 'red');
                         // Hide progress overlay on error
                         hideProgressOverlay();
                         // Re-enable the button on error so user can try again
                         startBtn.disabled = false;
                         setButtonLoadingState(startBtn, false);
                    });
                });
            }

            if (uploadBtn) {
                uploadBtn.addEventListener('click', () => {
                    console.log("JS: Upload to Stocks button clicked.");
                    updateProgressLabel('Opening Stock Uploader...', 'blue');

                    // Add debugging to see if the API object exists
                    console.log("JS Debug - pywebview API object:", window.pywebview.api);
                    console.log("JS Debug - trigger_stock_uploader exists:", typeof window.pywebview.api.trigger_stock_uploader === 'function');

                    try {
                        // Call the Python API function to launch the other app
                        window.pywebview.api.trigger_stock_uploader().then((result) => {
                            console.log("JS: Call to trigger_stock_uploader successful. Result:", result);
                            setTimeout(() => {
                                updateProgressLabel('Drag image files or a folder here', 'black');
                            }, 2000);
                        }).catch(err => {
                            console.error("JS: Error calling trigger_stock_uploader:", err);
                            updateProgressLabel(`Error opening uploader: ${err}`, 'red');
                        });
                    } catch (error) {
                        console.error("JS: Exception when trying to call trigger_stock_uploader:", error);
                        updateProgressLabel(`Error: ${error.message}`, 'red');
                    }
                });
            }


        });
    </script>
</body>
</html>
<!-- END OF FILE keyworder.html -->