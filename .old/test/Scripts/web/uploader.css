/* START OF FILE uploader.css */

/* Basic Reset & Body */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    /* Subtle gradient background for depth */
    background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
    color: #333; /* Slightly softer black for text */
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 15px; /* Reduced body padding */
    /* Consider removing overflow: hidden if it causes issues */
    /* overflow: hidden; */
}

.container {
    background-color: transparent; /* Container is just for layout - Copied from uploader.css */
    padding: 20px 20px 20px 20px; /* Reduced top/bottom padding */
    border-radius: 0;
    width: 100%;
    max-width: 640px; /* Consistent max-width */
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Title */
.title {
    font-size: 30px; /* Slightly smaller */
    font-weight: 500; /* Rubik Medium */
    color: #222; /* Darker text for title */
    text-align: center;
    margin-bottom: 18px; /* Reduced spacing */
}

/* Progress Label */
.progress-label {
    font-size: 14px;
    font-weight: 500; /* Medium weight */
    text-align: center;
    margin-bottom: 15px; /* Reduced spacing */
    min-height: 20px;
    color: #555; /* Default softer color */
    transition: color 0.3s ease;
}

.progress-label.initial-warning {
     color: #D32F2F; /* A slightly softer, modern red */
     font-weight: 600; /* Bolder warning */
}

/* Drop Area - Glassmorphism */
.drop-area {
    width: calc(100% - 30px); /* Adjusted width based on container padding */
    max-width: 520px;
    height: 110px; /* Slightly shorter */
    /* --- Glassmorphism Effect --- */
    background: rgba(255, 255, 255, 0.3); /* Semi-transparent white */
    backdrop-filter: blur(10px); /* The blur effect */
    -webkit-backdrop-filter: blur(10px); /* Safari prefix */
    border: 1px solid rgba(255, 255, 255, 0.4); /* Subtle white border */
    /* --- Styling & Layout --- */
    border-radius: 15px; /* Softer corners */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); /* Soft shadow for depth */
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 5px auto 20px auto; /* Reduced margins */
    cursor: pointer;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    padding: 15px;
}

.drop-area p {
    font-size: 18px;
    font-weight: 500; /* Medium weight */
    color: #333; /* Ensure contrast */
    pointer-events: none;
}

.drop-area.drag-over {
    background: rgba(255, 255, 255, 0.45); /* Slightly more opaque */
    border-color: rgba(255, 255, 255, 0.6); /* More visible border */
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08); /* Slightly stronger shadow */
}

/* Upload Label */
.upload-label {
    font-size: 16px;
    font-weight: 500; /* Medium */
    color: #333;
    margin-bottom: 8px; /* Reduced space below label */
    align-self: flex-start;
    /* Indent slightly from container edge */
    margin-left: calc((100% - min(90%, 520px)) / 2); /* Align with checkbox start */
    padding-left: 10px; /* Small extra padding */
}

/* Checkbox Container */
.checkbox-container {
    display: flex;
    justify-content: space-between;
    width: 90%; /* Responsive width */
    max-width: 520px; /* Match drop area max-width */
    margin: 5px auto 20px auto; /* Reduced bottom margin */
    padding: 0 10px; /* Internal padding */
}

.checkbox-group {
    display: flex;
    flex-direction: column;
}

/* Custom Checkbox Styling - Modernized */
.checkbox-label {
    display: block;
    position: relative;
    padding-left: 32px; /* Space for custom checkbox */
    margin-bottom: 12px; /* Reduced spacing */
    cursor: pointer;
    font-size: 15px; /* Clean size */
    user-select: none;
    color: #444; /* Slightly softer text */
    line-height: 22px; /* Align text vertically */
}

/* Hide the default checkbox */
.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox appearance */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 22px; /* Slightly larger */
    width: 22px;
    background-color: rgba(255, 255, 255, 0.3); /* Subtle background */
    border: 1px solid rgba(0, 0, 0, 0.1); /* Soft border */
    border-radius: 5px; /* Rounded square */
    transition: all 0.2s ease-out;
}

/* Style the checkmark when hovered */
.checkbox-label:hover input ~ .checkmark {
    background-color: rgba(255, 255, 255, 0.4);
    border-color: rgba(0, 0, 0, 0.2);
}

/* Style the checkmark when the checkbox is checked */
.checkbox-label input:checked ~ .checkmark {
    background-color: #FF6600; /* Orange background */
    border-color: #FF6600; /* Orange border */
    /* Optional: subtle glow */
    /* box-shadow: 0 0 6px rgba(255, 102, 0, 0.3); */
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.checkbox-label .checkmark:after {
    /* Modern checkmark shape */
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2.5px 2.5px 0; /* Clean lines */
    transform: rotate(45deg);
}


/* Button Container */
.button-container {
    width: 100%;
    max-width: 450px; /* Consistent width */
    margin-top: 15px; /* Reduced space above buttons */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px; /* Reduced space between buttons */
}

/* General Button Styles - Modernized */
.btn {
    width: 100%;
    height: 55px; /* Slightly taller */
    font-size: 18px; /* Clean size */
    font-weight: 600; /* Bolder */
    color: white;
    /* Subtle gradient for depth */
    background: linear-gradient(145deg, #ff7e29, #ff6600);
    border: none;
    border-radius: 10px; /* Softer corners */
    cursor: pointer;
    /* Soft shadow for lifting effect */
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.25), 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Smooth, slightly bouncy transition */
    display: flex;
    justify-content: center;
    align-items: center;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); /* Subtle text shadow */
}

.btn:hover {
    background: linear-gradient(145deg, #ff8840, #e65c00); /* Slightly adjusted gradient */
    box-shadow: 0 6px 16px rgba(255, 102, 0, 0.3), 0 2px 5px rgba(0,0,0,0.15); /* Enhanced shadow */
    transform: translateY(-2px); /* Lift effect */
}

.btn:active {
    transform: translateY(0px); /* Press down effect */
    box-shadow: 0 2px 8px rgba(255, 102, 0, 0.2), 0 1px 2px rgba(0,0,0,0.1); /* Reduced shadow */
    background: linear-gradient(145deg, #e65c00, #ff7e29); /* Invert gradient slightly */
}

/* Completion Indicator Overlay */
.completion-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's on top of everything */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.completion-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.completion-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #ff7e29, #ff6600); /* Match button gradient */
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
    transform: scale(0.5);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy effect */
}

.completion-overlay.visible .completion-circle {
    transform: scale(1);
}

.checkmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    stroke: white;
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    animation: draw-checkmark 0.6s ease-in-out forwards 0.2s;
}

@keyframes draw-checkmark {
    from {
        stroke-dashoffset: 100;
    }
    to {
        stroke-dashoffset: 0;
    }
}

/* Subtle bounce animation for the circle */
@keyframes bounce {
    0%, 100% {transform: translateY(0);}
    50% {transform: translateY(-5px);}
}

.completion-overlay.visible .completion-circle {
    animation: bounce 1.2s cubic-bezier(0.445, 0.05, 0.55, 0.95) 0.4s;
}

/* END OF FILE uploader.css */