"""
Centralized error logging system for the keywording application.
Provides functions to log errors to both console and a persistent file.
"""
import logging
import os
import sys
import platform
import locale
import traceback
from datetime import datetime
from logging.handlers import RotatingFileHandler

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Configure the main logger
logger = logging.getLogger('keywording_app')
logger.setLevel(logging.DEBUG)

# Create a formatter that includes timestamp, level, and message
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Create a file handler for persistent logging with rotation
log_file = os.path.join(logs_dir, 'keywording_app.log')
file_handler = RotatingFileHandler(log_file, maxBytes=5*1024*1024, backupCount=5, encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)

# Create a console handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)

# Add handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def log_error(message, exc_info=None, file_path=None):
    """
    Log an error with optional exception info and file path context.

    Args:
        message (str): Error message
        exc_info (Exception, optional): Exception object
        file_path (str, optional): Path to the file where the error occurred
    """
    try:
        error_msg = message
        if file_path:
            try:
                error_msg = f"[{os.path.basename(file_path)}] {error_msg}"
            except UnicodeEncodeError:
                # If we can't encode the filename, use a placeholder
                error_msg = f"[filename encoding error] {error_msg}"

        if exc_info:
            logger.error(error_msg, exc_info=True)
        else:
            logger.error(error_msg)

        # Return the formatted message for UI display
        return error_msg
    except UnicodeEncodeError:
        # If we can't encode the message, log a generic error
        safe_msg = "Error with non-ASCII characters (encoding error)"
        logger.error(safe_msg)
        return safe_msg
    except Exception as e:
        # Catch any other exceptions to prevent crashes
        safe_msg = f"Error logging error: {type(e).__name__}"
        logger.error(safe_msg)
        return safe_msg

def log_warning(message, file_path=None):
    """
    Log a warning with optional file path context.

    Args:
        message (str): Warning message
        file_path (str, optional): Path to the file where the warning occurred
    """
    try:
        warning_msg = message
        if file_path:
            try:
                warning_msg = f"[{os.path.basename(file_path)}] {warning_msg}"
            except UnicodeEncodeError:
                # If we can't encode the filename, use a placeholder
                warning_msg = f"[filename encoding error] {warning_msg}"

        logger.warning(warning_msg)

        # Return the formatted message for UI display
        return warning_msg
    except UnicodeEncodeError:
        # If we can't encode the message, log a generic warning
        safe_msg = "Warning with non-ASCII characters (encoding error)"
        logger.warning(safe_msg)
        return safe_msg
    except Exception as e:
        # Catch any other exceptions to prevent crashes
        safe_msg = f"Error logging warning: {type(e).__name__}"
        logger.warning(safe_msg)
        return safe_msg

def log_info(message):
    """Log an info message."""
    try:
        logger.info(message)
        return message
    except UnicodeEncodeError:
        # If we can't encode the message, log a generic info message
        safe_msg = "Info with non-ASCII characters (encoding error)"
        logger.info(safe_msg)
        return safe_msg
    except Exception as e:
        # Catch any other exceptions to prevent crashes
        safe_msg = f"Error logging info: {type(e).__name__}"
        logger.info(safe_msg)
        return safe_msg

def get_error_log_path():
    """Return the path to the error log file."""
    return log_file

def log_system_info():
    """Log system information to help diagnose machine-specific issues."""
    try:
        system_info = {
            'python_version': sys.version,
            'platform': platform.platform(),
            'system': platform.system(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'default_encoding': sys.getdefaultencoding(),
            'filesystem_encoding': sys.getfilesystemencoding(),
            'locale': locale.getdefaultlocale(),
            'preferred_encoding': locale.getpreferredencoding()
        }

        logger.info("=== System Information ===")
        for key, value in system_info.items():
            logger.info(f"{key}: {value}")
        logger.info("==========================")

        return system_info
    except Exception as e:
        logger.error(f"Error logging system information: {e}")
        return {}

def get_last_errors(count=10):
    """
    Get the last N errors from the log file.

    Args:
        count (int): Number of error entries to retrieve

    Returns:
        list: List of error messages
    """
    errors = []
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in reversed(lines):
                if ' - ERROR - ' in line:
                    errors.append(line.strip())
                    if len(errors) >= count:
                        break
        return errors
    except UnicodeDecodeError as ude:
        # If UTF-8 fails, try with system default encoding
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                for line in reversed(lines):
                    if ' - ERROR - ' in line:
                        errors.append(line.strip())
                        if len(errors) >= count:
                            break
            return errors
        except Exception as e2:
            logger.error(f"Failed to read error log with fallback encoding: {e2}")
            return [f"Failed to read error log with fallback encoding: {e2}"]
    except Exception as e:
        logger.error(f"Failed to read error log: {e}")
        return [f"Failed to read error log: {e}"]
