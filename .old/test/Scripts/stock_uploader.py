import os
import webview
import threading
import json
import sys

# Add current directory (<PERSON>ripts) to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Add parent directory to Python path for FTP module
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from webview.dom import DOMEventHandler
# Import using relative imports since we've added the directories to sys.path
from FTP.ftp import process_hosts
from config import open_app

# Check if the shutterstock_csv_generator module is available
try:
    from FTP.stocks.shutterstock_csv_generator import generate_shutterstock_csv
except ImportError:
    print("Warning: Could not import shutterstock_csv_generator module")

# --- Pywebview API Class (Keep the decoupled version from last time) ---
class Api:
    def __init__(self):
        self.source_path = []

    def _get_window(self):
        if webview.windows:
            return webview.windows[0]
        return None

    def handle_drop(self, paths):
        """
        Handles file paths dropped onto the webview window.
        Now called FROM the Python DOM drop event handler.
        Updates progress label with the count of valid files found. # <- Added clarification
        """
        print(f"Python API: handle_drop received paths: {paths}") # Log received paths
        valid_extensions = ('.jpg', '.jpeg', '.png', '.raw', '.mp4', '.mov', '.avi')
        found_files = []

        if not isinstance(paths, (list, tuple)):
             print(f"Warning: handle_drop received non-iterable paths: {paths}")
             paths = []

        for path in paths: # Directly iterate, paths should be strings now
            if not isinstance(path, str):
                print(f"Warning: Received non-string path item: {path}")
                continue

            # Check if path is directory or file
            if os.path.isdir(path):
                try:
                    print(f"Python API: Processing dropped folder: {path}")
                    for f in os.listdir(path):
                        full_path = os.path.join(path, f)
                        if os.path.isfile(full_path) and full_path.lower().endswith(valid_extensions):
                            found_files.append(full_path)
                except Exception as e:
                    print(f"Error reading directory {path}: {e}")
                    self.update_progress(f"Error reading folder: {os.path.basename(path)}", "red")
            elif os.path.isfile(path):
                 if path.lower().endswith(valid_extensions):
                     print(f"Python API: Processing dropped file: {path}")
                     found_files.append(path)
                 else:
                    print(f"Python API: Skipped invalid file type: {path}")
            else:
                 print(f"Warning: Dropped item is neither file nor directory: {path}")

        self.source_path = found_files
        total = len(self.source_path)
        print(f"Python API: Found {total} valid files. Updated source_path.")

        if total > 0:
            message = f'{total} file(s) selected. Ready to upload.'
            print(f"PYTHON DEBUG: Calling update_progress with: '{message}', '{'green'}'") # <--- ADD THIS LINE
            self.update_progress(message, "green")
        else:
            if paths: # Dropped items existed but none were valid
                 self.update_progress('No valid media files found in drop.', "orange")
            else: # No paths received initially (shouldn't happen with DOM event)
                 self.update_progress('Drop valid files here.', "black") # Reset prompt

    def upload_files_thread(self, hosts_to_upload, files_to_upload):
        try:
            # Special handling for Shutterstock - generate CSV only
            if 'Shutterstock' in hosts_to_upload:
                # Remove Shutterstock from the hosts list for actual uploading
                upload_hosts = [h for h in hosts_to_upload if h != 'Shutterstock']

                # Generate Shutterstock CSV
                self.update_progress(f"Generating Shutterstock CSV...", "blue")
                self.generate_shutterstock_csv(files_to_upload)

                # If there are other hosts to upload to, proceed with them
                if upload_hosts:
                    self.update_progress(f"Uploading to {', '.join(upload_hosts)}...", "blue")
                    process_hosts(upload_hosts, files_to_upload)
                    self.update_progress(f"Uploaded {len(files_to_upload)} file(s) and generated Shutterstock CSV.", "green")
                    # Show completion indicator
                    self.show_completion_indicator()
                else:
                    # Only generated CSV, no uploads
                    self.update_progress(f"Generated Shutterstock CSV for {len(files_to_upload)} file(s).", "green")
                    # Show completion indicator
                    self.show_completion_indicator()
            else:
                # Normal upload process for other hosts
                self.update_progress(f"Uploading to {', '.join(hosts_to_upload)}...", "blue")
                process_hosts(hosts_to_upload, files_to_upload)
                self.update_progress(f"Uploaded {len(files_to_upload)} file(s).", "green")
                # Show completion indicator
                self.show_completion_indicator()
        except Exception as e:
            print(f"Error during upload process: {e}")
            self.update_progress(f"⚠ Error during upload: {e}", "red")

    def generate_shutterstock_csv(self, files_to_upload):
        """Generate a CSV file for Shutterstock based on the selected files."""
        try:
            # Use the function from ftp.py to generate the CSV
            success, output_csv_path = generate_shutterstock_csv_from_files(files_to_upload)

            if success:
                self.update_progress(f"Shutterstock CSV generated: {output_csv_path}", "green")
                # Show completion indicator for successful CSV generation
                self.show_completion_indicator()
                return True
            else:
                self.update_progress("⚠ Failed to generate Shutterstock CSV.", "red")
                return False

        except Exception as e:
            print(f"Error generating Shutterstock CSV: {e}")
            self.update_progress(f"⚠ Error generating Shutterstock CSV: {e}", "red")
            return False


    def request_upload(self, selected_hosts):
        print(f"Python API: Upload requested for hosts: {selected_hosts}")
        if not self.source_path:
            self.update_progress('⚠ No files are selected! Drop files first. ⚠', "red")
            return
        if not selected_hosts:
            self.update_progress('⚠ No destination hosts selected! ⚠', "red")
            return

        # Optional: iStock check
        if "iStock" in selected_hosts:
             non_videos = [f for f in self.source_path if not f.lower().endswith(('.mp4', '.mov', '.avi'))]
             if non_videos:
                  self.update_progress('⚠ Note: iStock only accepts videos. Non-videos will be ignored for iStock.', "orange")

        upload_thread = threading.Thread(
            target=self.upload_files_thread,
            args=(selected_hosts, list(self.source_path)),
            daemon=True
        )
        upload_thread.start()

    def review_files(self):
        open_app("gallery.py")

    def show_completion_indicator(self):
        """ Shows the completion indicator animation in the UI """
        print("Python API: Showing completion indicator")
        window = self._get_window()
        if window:
            try:
                window.evaluate_js('showCompletionIndicator()')
                print("Python API: Called JS showCompletionIndicator()")
            except Exception as e:
                print(f"Error calling JS showCompletionIndicator: {e}")
        else:
            print("Error: Could not show completion indicator (window not found).")

    def update_progress(self, message, color):
        window = self._get_window()
        if window:
            try:
                escaped_message = json.dumps(message)
                js_code = f"updateProgressLabel({escaped_message}, '{color}');"
                print(f"PYTHON DEBUG: Evaluating JS: {js_code}") # <--- ADD THIS LINE
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error evaluating JS for progress update: {e}")
        else:
            print(f"Progress (window not found): {message}")


# --- Helper function to get asset path (keep as before) ---
def get_asset_path(relative_path):
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # Not running as a PyInstaller bundle, use normal path
        base_path = os.path.abspath(".")
    # Ensure web files are loaded relative to the script's web folder
    return os.path.join(os.path.dirname(__file__), 'web', relative_path)


# --- Global reference to API instance (keep as before) ---
_api_instance = None

# *** NEW: Python Function to Handle DOM Drop Event ***
def python_on_drop(event):
    """
    Handles the drop event captured via window.dom.document.events.
    Extracts file paths using 'pywebviewFullPath'.
    """
    global _api_instance
    print("Python DOM Event: Drop detected!") # Log event detection

    try:
        # Access files from the event data structure
        files_data = event.get('dataTransfer', {}).get('files', [])
        if not files_data:
            print("Python DOM Event: No files found in event dataTransfer.")
            return

        dropped_paths = []
        print("Python DOM Event: Extracting file paths...")
        for file_info in files_data:
            # *** Use the correct key to get the full path ***
            full_path = file_info.get('pywebviewFullPath')
            if full_path and isinstance(full_path, str):
                print(f"  Found path: {full_path}") # Log each found path
                dropped_paths.append(full_path)
            else:
                # Log if path is missing or not a string (shouldn't happen if key is right)
                print(f"  Warning: Could not get 'pywebviewFullPath' for file: {file_info.get('name', 'N/A')}")

        # Call the API handler if paths were found and instance exists
        if dropped_paths and _api_instance:
            print(f"Python DOM Event: Calling API handle_drop with {len(dropped_paths)} path(s).")
            _api_instance.handle_drop(dropped_paths)
        elif not dropped_paths:
            print("Python DOM Event: No valid paths extracted.")
            if _api_instance: # Update UI even if no valid paths
                 _api_instance.update_progress('Could not extract file paths from drop.', 'red')

    except Exception as e:
        # Catch potential errors during event processing
        print(f"Error in python_on_drop event handler: {e}")
        if _api_instance:
             _api_instance.update_progress(f'Error processing drop: {e}', 'red')


# *** NEW: Function to Bind Drag and Drop Events to the DOM Element ***
def bind_dnd_events(window):
    """
    Binds the Python drop handler to the DOM element after the window loads.
    """
    print("Python: Attempting to bind DND events to DOM...")
    try:
        # Find the target DOM element (ensure ID matches your HTML)
        drop_area_element = window.dom.get_element('#drop-area')

        if drop_area_element:
            # Bind the Python drop handler to the 'drop' event of the specific element
            drop_area_element.events.drop += DOMEventHandler(python_on_drop, prevent_default=True, stop_propagation=True)
            print("Python: Successfully bound python_on_drop to #drop-area")

        else:
            print("Python Error: Could not find DOM element #drop-area to bind events.")

    except AttributeError as e:
         # Catch if window.dom or element.events is not available (should be, but good practice)
         print(f"Python Error: Failed to bind DOM events. DOM API might not be ready or supported. Error: {e}")
    except Exception as e:
         # Catch other potential errors during binding
         print(f"Python Error: An unexpected error occurred during DND event binding: {e}")


# --- Main Application Setup ---
def run_stock_uploader():
    global _api_instance # Ensure the global instance is assigned here
    _api_instance = Api() # Create API instance

    # *** Ensure HTML file path/name is correct ***
    html_file = get_asset_path('uploader.html') # Check if it's index.html or uploader.html
    if not os.path.exists(html_file):
       print(f"ERROR: HTML file not found at {html_file}")
       sys.exit(1)

    window = webview.create_window(
        'Stock Uploader V1.0',
        url=html_file,
        js_api=_api_instance, # API instance is passed here
        width=680,
        height=680,
        resizable=False,
        confirm_close=False,
        background_color='#f0f0f0',
    )

    # *** Use the startup argument to run the binding function AND ENABLE DEBUG ***
    webview.start(bind_dnd_events, window, debug=False) # Pass bind function, window object, and set debug=True

if __name__ == '__main__':
    run_stock_uploader()