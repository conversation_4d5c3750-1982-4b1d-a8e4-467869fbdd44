#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Shutterstock FTPS Upload Module

This module provides functionality to upload photos and videos to Shutterstock
via FTPS (FTP Secure). It handles authentication, file uploads, and error handling.

Based on Shutterstock's official FTPS upload documentation.
"""

import logging
import os
import socket
import time
import ssl
import random
from ftplib import FTP_TLS, error_perm, error_temp
from typing import List, Union, Dict

from Scripts.utils import mark_as_done, check_for_matching_video

# --- Configuration ---
# Create a custom formatter with more details
detailed_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Configure file handler with detailed logging
file_handler = logging.FileHandler("shutterstock_upload_detailed.log")
file_handler.setFormatter(detailed_formatter)
file_handler.setLevel(logging.DEBUG)  # Always log debug to file

# Configure console handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(detailed_formatter)
console_handler.setLevel(logging.INFO)  # Default to INFO for console

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)  # Capture all levels
root_logger.handlers = []  # Remove any existing handlers
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# --- Constants ---
SHUTTERSTOCK_HOSTNAME = "ftps.shutterstock.com"
SHUTTERSTOCK_PORT = 21  # Default FTPS port
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

# --- FTP Configuration ---
# Default to passive mode, but allow switching to active mode if needed
DEFAULT_PASSIVE_MODE = True
# Longer timeouts for better reliability with large files
CONNECTION_TIMEOUT = 180  # seconds - increased for better reliability
# Smaller block size may help with some firewalls
DEFAULT_BLOCK_SIZE = 32768  # 32KB blocks - reduced for better reliability
# TLS/SSL options
TLS_VERSION = ssl.PROTOCOL_TLS  # Use the highest available protocol version

# --- Verbose Logging ---
VERBOSE = True  # Enable detailed logging by default

# --- Supported File Extensions ---
SUPPORTED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.tif', '.tiff', '.eps'}
SUPPORTED_VIDEO_EXTENSIONS = {'.mov', '.mp4', '.avi', '.mpeg', '.wmv'}
SUPPORTED_EXTENSIONS = SUPPORTED_IMAGE_EXTENSIONS.union(SUPPORTED_VIDEO_EXTENSIONS)


def is_supported_file(file_path: str) -> bool:
    """
    Check if the file has a supported extension for Shutterstock upload.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if the file has a supported extension, False otherwise
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower() in SUPPORTED_EXTENSIONS


def is_video_file(file_path: str) -> bool:
    """
    Check if the file is a video file.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if the file is a video, False otherwise
    """
    _, ext = os.path.splitext(file_path)
    return ext.lower() in SUPPORTED_VIDEO_EXTENSIONS


def get_files_to_upload(source_path: Union[str, List[str]]) -> List[str]:
    """
    Get a list of files to upload from the source path(s).

    Args:
        source_path (str or list): Path to a file, directory, or list of files

    Returns:
        list: List of absolute paths to files that should be uploaded
    """
    files_to_upload = []

    # Handle single path string
    if isinstance(source_path, str):
        if os.path.isfile(source_path):
            if is_supported_file(source_path):
                files_to_upload.append(source_path)

                # Check for matching video if this is an image
                if not is_video_file(source_path):
                    has_video, video_path = check_for_matching_video(source_path)
                    if has_video and video_path:
                        files_to_upload.append(video_path)

        elif os.path.isdir(source_path):
            # Process all files in the directory
            for item in os.listdir(source_path):
                item_path = os.path.join(source_path, item)
                if os.path.isfile(item_path) and is_supported_file(item_path):
                    files_to_upload.append(item_path)

    # Handle list of paths
    elif isinstance(source_path, list):
        for path in source_path:
            if os.path.isfile(path) and is_supported_file(path):
                files_to_upload.append(path)

                # Check for matching video if this is an image
                if not is_video_file(path):
                    has_video, video_path = check_for_matching_video(path)
                    if has_video and video_path:
                        files_to_upload.append(video_path)

    return files_to_upload


def upload(username: str, password: str, source_path: Union[str, List[str]],
           host: str = SHUTTERSTOCK_HOSTNAME, port: int = SHUTTERSTOCK_PORT,
           passive_mode: bool = DEFAULT_PASSIVE_MODE, use_implicit_ftps: bool = False) -> List[str]:
    """
    Upload files to Shutterstock via FTPS.

    Args:
        username (str): Shutterstock FTPS username
        password (str): Shutterstock FTPS password
        source_path (str or list): Path to a file, directory, or list of files
        host (str): Shutterstock FTPS hostname
        port (int): FTPS port

    Returns:
        list: List of successfully uploaded file names
    """
    # 1. Validate and collect files to upload
    files_to_upload = get_files_to_upload(source_path)

    if not files_to_upload:
        logging.warning(f"No supported files found to upload from {source_path}")
        return []

    logging.info(f"Found {len(files_to_upload)} file(s) to upload to Shutterstock")

    # 2. Establish FTPS Connection
    ftp = None
    uploaded_files = []

    try:
        logging.info(f"Connecting to Shutterstock FTPS server: {host}:{port}")
        # Create a custom SSL context to handle TLS issues
        logging.debug("Creating custom SSL context")
        context = ssl.SSLContext(TLS_VERSION)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE  # Don't verify certificate

        # Log SSL/TLS configuration
        logging.debug(f"SSL Context: check_hostname={context.check_hostname}, verify_mode={context.verify_mode}")
        logging.debug(f"SSL Context options: {context.options}")

        # Use longer timeout for better reliability and custom SSL context
        logging.debug(f"Creating FTP_TLS instance with timeout={CONNECTION_TIMEOUT}")
        ftp = FTP_TLS(timeout=CONNECTION_TIMEOUT, context=context)

        # For implicit FTPS (less common, but sometimes required)
        if use_implicit_ftps:
            logging.info("Using implicit FTPS mode (port 990)")
            # For implicit FTPS, typically use port 990
            actual_port = 990 if port == 21 else port
            logging.debug(f"Connecting to {host}:{actual_port} (implicit FTPS)")
            try:
                ftp.connect(host, actual_port)
                logging.debug(f"Connected to {host}:{actual_port} successfully")
            except Exception as e:
                logging.error(f"Failed to connect to {host}:{actual_port}: {e}")
                logging.debug(f"Connection error details: {type(e).__name__}: {str(e)}", exc_info=True)
                raise
        else:
            # Standard explicit FTPS on port 21
            logging.debug(f"Connecting to {host}:{port} (explicit FTPS)")
            try:
                ftp.connect(host, port)
                logging.debug(f"Connected to {host}:{port} successfully")
            except Exception as e:
                logging.error(f"Failed to connect to {host}:{port}: {e}")
                logging.debug(f"Connection error details: {type(e).__name__}: {str(e)}", exc_info=True)
                raise

        logging.info("Connection established. Logging in...")

        # Log socket information
        logging.debug(f"Socket info: family={ftp.sock.family}, type={ftp.sock.type}, proto={ftp.sock.proto}")
        logging.debug(f"Socket timeout: {ftp.sock.gettimeout()}")

        # Disable Nagle's algorithm to prevent delays
        try:
            ftp.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            logging.debug("Disabled Nagle's algorithm (TCP_NODELAY=1)")
        except Exception as e:
            logging.warning(f"Could not set TCP_NODELAY: {e}")

        # Try to get local address information
        try:
            local_addr = ftp.sock.getsockname()
            logging.debug(f"Local socket address: {local_addr}")
        except Exception as e:
            logging.debug(f"Could not get local socket address: {e}")

        # Try to get remote address information
        try:
            remote_addr = ftp.sock.getpeername()
            logging.debug(f"Remote socket address: {remote_addr}")
        except Exception as e:
            logging.debug(f"Could not get remote socket address: {e}")

        # Login with credentials
        logging.debug(f"Attempting to login as user: {username}")
        try:
            ftp.login(username, password)
            logging.info(f"Successfully logged in as user: {username}")
        except Exception as e:
            logging.error(f"Login failed: {e}")
            logging.debug(f"Login error details: {type(e).__name__}: {str(e)}", exc_info=True)
            raise

        # Switch to secure data connection (required for FTPS)
        logging.debug("Enabling secure data connection (PROT P)")
        try:
            ftp.prot_p()
            logging.info("Data connection security enabled (PROT P)")
        except Exception as e:
            logging.error(f"Failed to enable secure data connection: {e}")
            logging.debug(f"PROT P error details: {type(e).__name__}: {str(e)}", exc_info=True)
            raise

        # Set passive/active mode based on parameter
        logging.debug(f"Setting {'passive' if passive_mode else 'active'} mode")
        try:
            ftp.set_pasv(passive_mode)
            logging.info(f"Using {'passive' if passive_mode else 'active'} mode")
        except Exception as e:
            logging.error(f"Failed to set {'passive' if passive_mode else 'active'} mode: {e}")
            logging.debug(f"set_pasv error details: {type(e).__name__}: {str(e)}", exc_info=True)
            raise

        # For active mode, try to configure the socket
        if not passive_mode:
            # Try to configure socket for active mode
            try:
                # Disable Nagle's algorithm to prevent delays
                ftp.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                # Set socket reuse
                ftp.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                logging.info("Active mode socket configured")
            except Exception as e:
                logging.warning(f"Could not configure socket for active mode: {e}")

        # 3. Upload Files
        logging.info(f"Starting upload of {len(files_to_upload)} file(s)...")
        success_count = 0
        failure_count = 0

        for file_path in files_to_upload:
            filename = os.path.basename(file_path)

            # Skip if already uploaded in this session
            if filename in uploaded_files:
                logging.info(f"Skipping {filename} - already uploaded in this session")
                continue

            # Implement retry logic for better reliability
            for attempt in range(1, MAX_RETRIES + 1):
                try:
                    file_size_mb = os.path.getsize(file_path) / 1024 / 1024
                    logging.info(f"Attempting to upload: {filename} ({file_size_mb:.2f} MB) - Attempt {attempt}/{MAX_RETRIES}")

                    # Ensure we're in binary mode
                    logging.debug("Setting binary transfer mode (TYPE I)")
                    try:
                        ftp.voidcmd('TYPE I')
                        logging.debug("Binary mode set successfully")
                    except Exception as e:
                        logging.error(f"Failed to set binary mode: {e}")
                        logging.debug(f"TYPE I error details: {type(e).__name__}: {str(e)}", exc_info=True)
                        raise

                    # For large files, try to use a more reliable approach
                    file_size = os.path.getsize(file_path)
                    logging.debug(f"File size: {file_size} bytes ({file_size_mb:.2f} MB)")

                    if file_size > 10 * 1024 * 1024:  # If file is larger than 10MB
                        logging.info(f"Large file detected ({file_size_mb:.2f} MB). Using chunked upload approach.")

                        try:
                            with open(file_path, 'rb') as f_obj:
                                # Open data connection manually
                                cmd = f'STOR {filename}'
                                logging.debug(f"Opening data connection with command: {cmd}")
                                try:
                                    conn = ftp.transfercmd(cmd)
                                    logging.debug("Data connection opened successfully")

                                    # Log data connection details
                                    try:
                                        data_local = conn.getsockname()
                                        data_remote = conn.getpeername()
                                        logging.debug(f"Data connection: local={data_local}, remote={data_remote}")
                                    except Exception as e:
                                        logging.debug(f"Could not get data connection details: {e}")

                                    # Upload in chunks
                                    bytes_sent = 0
                                    chunk_count = 0
                                    start_time = time.time()

                                    while True:
                                        chunk = f_obj.read(DEFAULT_BLOCK_SIZE)
                                        if not chunk:
                                            break

                                        # Log every 20 chunks at debug level
                                        chunk_count += 1
                                        if chunk_count % 20 == 0:
                                            logging.debug(f"Sending chunk #{chunk_count}, size={len(chunk)} bytes")

                                        try:
                                            conn.sendall(chunk)
                                            bytes_sent += len(chunk)

                                            # Log progress for larger uploads
                                            if bytes_sent % (1 * 1024 * 1024) == 0:  # Log every 1MB
                                                elapsed = time.time() - start_time
                                                speed = bytes_sent / (1024 * 1024 * elapsed) if elapsed > 0 else 0
                                                logging.info(f"Uploaded {bytes_sent / (1024 * 1024):.2f} MB so far... ({speed:.2f} MB/s)")
                                        except Exception as e:
                                            logging.error(f"Error sending chunk #{chunk_count}: {e}")
                                            logging.debug(f"Send error details: {type(e).__name__}: {str(e)}", exc_info=True)
                                            raise

                                    # Log final stats
                                    total_elapsed = time.time() - start_time
                                    avg_speed = file_size / (1024 * 1024 * total_elapsed) if total_elapsed > 0 else 0
                                    logging.debug(f"Upload completed: {file_size} bytes in {total_elapsed:.2f} seconds ({avg_speed:.2f} MB/s)")

                                    # Close the connection
                                    logging.debug("Closing data connection")
                                    conn.close()

                                    # Get server response
                                    logging.debug("Waiting for server response")
                                    resp = ftp.voidresp()
                                    logging.debug(f"Server response: {resp}")

                                except Exception as e:
                                    logging.error(f"Data connection error: {e}")
                                    logging.debug(f"Data connection error details: {type(e).__name__}: {str(e)}", exc_info=True)
                                    raise
                        except Exception as e:
                            logging.error(f"Chunked upload failed: {e}")
                            logging.debug(f"Chunked upload error details: {type(e).__name__}: {str(e)}", exc_info=True)
                            raise
                    else:
                        # For smaller files, use the standard approach
                        logging.debug(f"Using standard upload approach for {file_size_mb:.2f} MB file")
                        try:
                            with open(file_path, 'rb') as f_obj:
                                logging.debug(f"Sending STOR command for {filename} with blocksize={DEFAULT_BLOCK_SIZE}")
                                start_time = time.time()
                                ftp.storbinary(f'STOR {filename}', f_obj, blocksize=DEFAULT_BLOCK_SIZE,
                                              callback=lambda block: logging.debug(f"Sent block: {len(block)} bytes")
                                                                    if VERBOSE and random.random() < 0.05 else None)
                                total_elapsed = time.time() - start_time
                                avg_speed = file_size / (1024 * 1024 * total_elapsed) if total_elapsed > 0 else 0
                                logging.debug(f"Standard upload completed: {file_size} bytes in {total_elapsed:.2f} seconds ({avg_speed:.2f} MB/s)")
                        except Exception as e:
                            logging.error(f"Standard upload failed: {e}")
                            logging.debug(f"Standard upload error details: {type(e).__name__}: {str(e)}", exc_info=True)
                            raise

                    logging.info(f"Successfully uploaded: {filename}")
                    uploaded_files.append(filename)
                    success_count += 1

                    # Mark the file as done in EXIF data
                    try:
                        mark_as_done(file_path, "Shutterstock")
                        logging.info(f"Marked {filename} as done for Shutterstock")
                    except Exception as e:
                        logging.warning(f"Could not mark {filename} as done: {e}")

                    # Break the retry loop on success
                    break

                except socket.timeout:
                    if attempt < MAX_RETRIES:
                        logging.warning(f"Network timeout uploading {filename}. Retrying in {RETRY_DELAY} seconds...")
                        time.sleep(RETRY_DELAY)
                    else:
                        logging.error(f"Network timeout uploading {filename} after {MAX_RETRIES} attempts. Check firewall settings.")
                        failure_count += 1

                except socket.error as e:
                    if attempt < MAX_RETRIES:
                        logging.warning(f"Socket error uploading {filename}: {e}. Retrying in {RETRY_DELAY} seconds...")
                        time.sleep(RETRY_DELAY)
                    else:
                        logging.error(f"Socket error uploading {filename} after {MAX_RETRIES} attempts: {e}")
                        failure_count += 1

                except error_temp as e:
                    if attempt < MAX_RETRIES:
                        logging.warning(f"Temporary FTP error uploading {filename}: {e}. Retrying in {RETRY_DELAY} seconds...")
                        time.sleep(RETRY_DELAY)
                    else:
                        logging.error(f"Temporary FTP error uploading {filename} after {MAX_RETRIES} attempts: {e}")
                        failure_count += 1

                except error_perm as e:
                    logging.error(f"Permanent FTP error uploading {filename}: {e}. Skipping file.")
                    failure_count += 1
                    break  # No retry for permanent errors

                except Exception as e:
                    if attempt < MAX_RETRIES:
                        logging.warning(f"Error uploading {filename}: {e}. Retrying in {RETRY_DELAY} seconds...")
                        time.sleep(RETRY_DELAY)
                    else:
                        logging.error(f"Error uploading {filename} after {MAX_RETRIES} attempts: {e}")
                        failure_count += 1

        logging.info(f"Upload summary: {success_count} successful, {failure_count} failed")

    except socket.gaierror:
        logging.error(f"Could not resolve hostname: {host}. Check your internet connection.")
    except socket.timeout:
        logging.error("Connection timed out. Check your internet connection and firewall settings.")
    except error_perm as e:
        logging.error(f"FTP permission error: {e}. Check your username and password.")
    except Exception as e:
        logging.error(f"Unexpected error during Shutterstock upload: {e}")
    finally:
        # Close FTP connection if it was established
        if ftp:
            try:
                ftp.quit()
                logging.info("FTPS connection closed properly")
            except:
                logging.warning("Could not close FTPS connection properly")

    return uploaded_files


def diagnose_network(host: str = SHUTTERSTOCK_HOSTNAME, port: int = SHUTTERSTOCK_PORT) -> None:
    """
    Run network diagnostics to help troubleshoot connection issues.

    Args:
        host (str): Hostname to diagnose
        port (int): Port to check
    """
    print(f"\nRunning network diagnostics for {host}:{port}...")

    # Check DNS resolution
    print("\nChecking DNS resolution...")
    try:
        import socket
        ip_address = socket.gethostbyname(host)
        print(f"✓ DNS resolution successful: {host} resolves to {ip_address}")
    except Exception as e:
        print(f"✗ DNS resolution failed: {e}")

    # Check basic connectivity with ping
    print("\nChecking basic connectivity with ping...")
    try:
        import subprocess
        result = subprocess.run(["ping", "-n", "4", host],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ Ping successful")
            # Extract ping statistics
            for line in result.stdout.splitlines():
                if "loss" in line or "Average" in line:
                    print(f"  {line.strip()}")
        else:
            print(f"✗ Ping failed with return code {result.returncode}")
            print(f"  {result.stdout}")
    except Exception as e:
        print(f"✗ Ping test failed: {e}")

    # Check port connectivity
    print(f"\nChecking if port {port} is open...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        if result == 0:
            print(f"✓ Port {port} is open")
        else:
            print(f"✗ Port {port} is closed or blocked (error code: {result})")
        sock.close()
    except Exception as e:
        print(f"✗ Port check failed: {e}")

    # Check if a firewall might be blocking connections
    print("\nChecking for potential firewall issues...")
    try:
        # Try to connect to common ports to see if any connections work
        test_ports = [21, 80, 443]
        open_ports = []

        for test_port in test_ports:
            if test_port == port:  # Skip the port we already tested
                continue

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, test_port))
            if result == 0:
                open_ports.append(test_port)
            sock.close()

        if open_ports:
            print(f"✓ Successfully connected to other ports: {', '.join(map(str, open_ports))}")
            if port not in open_ports and port in test_ports:
                print(f"  This suggests a specific issue with port {port}, possibly firewall related")
        else:
            print(f"✗ Could not connect to any common ports ({', '.join(map(str, test_ports))})")
            print("  This suggests a general connectivity or firewall issue")
    except Exception as e:
        print(f"✗ Firewall check failed: {e}")

    # Check TLS/SSL connectivity
    if port == 21 or port == 990:  # Only for FTP ports
        print("\nChecking TLS/SSL connectivity...")
        try:
            import ssl
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            # For FTPS, we need to first establish a regular connection
            # and then upgrade to TLS
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((host, port))

            # For port 990 (implicit FTPS), wrap immediately
            if port == 990:
                try:
                    ssl_sock = context.wrap_socket(sock)
                    print(f"✓ Implicit TLS connection successful")
                    print(f"  TLS version: {ssl_sock.version()}")
                    print(f"  Cipher: {ssl_sock.cipher()[0]}")
                    ssl_sock.close()
                except Exception as e:
                    print(f"✗ Implicit TLS connection failed: {e}")
            else:  # For port 21 (explicit FTPS)
                try:
                    # Need to read the welcome message first
                    welcome = sock.recv(1024)
                    print(f"  Received welcome message: {welcome[:50]}...")

                    # Send AUTH TLS command
                    sock.sendall(b"AUTH TLS\r\n")
                    response = sock.recv(1024)
                    print(f"  AUTH TLS response: {response[:50]}...")

                    if response.startswith(b"2"):  # 2xx response is positive
                        ssl_sock = context.wrap_socket(sock)
                        print(f"✓ Explicit TLS connection successful")
                        print(f"  TLS version: {ssl_sock.version()}")
                        print(f"  Cipher: {ssl_sock.cipher()[0]}")
                        ssl_sock.close()
                    else:
                        print(f"✗ Server rejected AUTH TLS command")
                except Exception as e:
                    print(f"✗ Explicit TLS negotiation failed: {e}")
        except Exception as e:
            print(f"✗ TLS/SSL check failed: {e}")

    print("\nNetwork diagnostics complete.")


def test_connection(username: str, password: str, host: str = SHUTTERSTOCK_HOSTNAME,
                   port: int = SHUTTERSTOCK_PORT, passive_mode: bool = DEFAULT_PASSIVE_MODE,
                   use_implicit_ftps: bool = False) -> Dict[str, bool]:
    """
    Test the FTPS connection with different settings to help diagnose issues.

    Args:
        username (str): Shutterstock FTPS username
        password (str): Shutterstock FTPS password
        host (str): Shutterstock FTPS hostname
        port (int): FTPS port
        passive_mode (bool): Whether to use passive mode
        use_implicit_ftps (bool): Whether to use implicit FTPS

    Returns:
        dict: Dictionary with test results
    """
    results = {
        "connection": False,
        "login": False,
        "secure_data": False,
        "directory_list": False
    }

    print(f"\nTesting connection to {host}:{port} with {'passive' if passive_mode else 'active'} mode")
    print(f"Using {'implicit' if use_implicit_ftps else 'explicit'} FTPS")

    try:
        # Create a custom SSL context to handle TLS issues
        context = ssl.SSLContext(TLS_VERSION)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE  # Don't verify certificate

        # Create FTP_TLS instance with custom SSL context
        ftp = FTP_TLS(timeout=CONNECTION_TIMEOUT, context=context)

        # Try to connect
        try:
            if use_implicit_ftps:
                actual_port = 990 if port == 21 else port
                print(f"Connecting to {host}:{actual_port} (implicit FTPS)...")
                ftp.connect(host, actual_port)
            else:
                print(f"Connecting to {host}:{port} (explicit FTPS)...")
                ftp.connect(host, port)

            print("✓ Connection successful")
            results["connection"] = True
        except Exception as e:
            print(f"✗ Connection failed: {e}")
            return results

        # Try to login
        try:
            print(f"Logging in as {username}...")
            ftp.login(username, password)
            print("✓ Login successful")
            results["login"] = True
        except Exception as e:
            print(f"✗ Login failed: {e}")
            return results

        # Try to enable secure data channel
        try:
            print("Enabling secure data channel (PROT P)...")
            ftp.prot_p()
            print("✓ Secure data channel enabled")
            results["secure_data"] = True
        except Exception as e:
            print(f"✗ Failed to enable secure data channel: {e}")
            return results

        # Set passive/active mode
        try:
            print(f"Setting {'passive' if passive_mode else 'active'} mode...")
            ftp.set_pasv(passive_mode)
        except Exception as e:
            print(f"✗ Failed to set {'passive' if passive_mode else 'active'} mode: {e}")
            return results

        # Try to list directory
        try:
            print("Listing directory...")
            dir_list = ftp.nlst()
            print(f"✓ Directory listing successful: {len(dir_list)} items")
            results["directory_list"] = True
        except Exception as e:
            print(f"✗ Directory listing failed: {e}")

        # Close connection
        try:
            ftp.quit()
            print("Connection closed properly")
        except:
            print("Note: Could not close connection properly")

    except Exception as e:
        print(f"Error during connection test: {e}")

    # Print summary
    print("\nTest Summary:")
    for test, result in results.items():
        print(f"{test}: {'✓ Pass' if result else '✗ Fail'}")

    return results


def upload_shutterstock_process(username: str, password: str, source_path_or_list: Union[str, List[str]],
                               host: str = SHUTTERSTOCK_HOSTNAME,
                               passive_mode: bool = DEFAULT_PASSIVE_MODE,
                               use_implicit_ftps: bool = False) -> bool:
    """
    Orchestrates the full Shutterstock upload process.

    Args:
        username (str): Shutterstock FTPS username
        password (str): Shutterstock FTPS password
        source_path_or_list (str or list): Path(s) to upload
        host (str): Shutterstock FTPS hostname

    Returns:
        bool: True if at least one file was uploaded successfully, False otherwise
    """
    try:
        # Upload the files
        uploaded_files = upload(username, password, source_path_or_list, host,
                            passive_mode=passive_mode, use_implicit_ftps=use_implicit_ftps)

        # Return success if at least one file was uploaded
        return len(uploaded_files) > 0

    except Exception as e:
        logging.error(f"Error in Shutterstock upload process: {e}")
        return False


if __name__ == "__main__":
    # Example usage
    import sys
    import argparse

    parser = argparse.ArgumentParser(description="Upload files to Shutterstock via FTPS")
    parser.add_argument("username", help="Shutterstock FTPS username")
    parser.add_argument("password", help="Shutterstock FTPS password")
    parser.add_argument("source", help="File, directory, or comma-separated list of files to upload")
    parser.add_argument("--host", default=SHUTTERSTOCK_HOSTNAME, help="Shutterstock FTPS hostname")
    parser.add_argument("--active", action="store_true", help="Use active mode instead of passive mode")
    parser.add_argument("--implicit", action="store_true", help="Use implicit FTPS (port 990) instead of explicit")
    parser.add_argument("--test", action="store_true", help="Test connection only, don't upload files")
    parser.add_argument("--diagnose", action="store_true", help="Run network diagnostics")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Enable debug logging if requested
    if args.debug:
        console_handler.setLevel(logging.DEBUG)
        # Enable ftplib debug output
        from ftplib import FTP
        FTP.debugging = 2
        logging.debug("FTP debugging enabled at level 2")

        # Log system information
        logging.debug(f"Python version: {sys.version}")
        logging.debug(f"Operating system: {os.name} - {sys.platform}")
        logging.debug(f"SSL version: {ssl.OPENSSL_VERSION}")

        # Log available SSL protocols
        logging.debug("Available SSL protocols:")
        for protocol_name in dir(ssl):
            if protocol_name.startswith('PROTOCOL_'):
                logging.debug(f"  {protocol_name}")

    # Use active mode if specified (passive is default)
    passive_mode = not args.active

    # Run network diagnostics if requested
    if args.diagnose:
        diagnose_network(args.host)
        sys.exit(0)

    # Test connection only
    if args.test:
        test_connection(
            args.username,
            args.password,
            args.host,
            passive_mode=passive_mode,
            use_implicit_ftps=args.implicit
        )
        sys.exit(0)

    # Handle comma-separated list of files
    if "," in args.source:
        source_list = [s.strip() for s in args.source.split(",")]
    else:
        source_list = args.source

    print(f"Uploading to Shutterstock using {'active' if args.active else 'passive'} mode")
    print(f"Using {'implicit' if args.implicit else 'explicit'} FTPS")

    success = upload_shutterstock_process(
        args.username,
        args.password,
        source_list,
        args.host,
        passive_mode=passive_mode,
        use_implicit_ftps=args.implicit
    )

    print(f"Upload {'successful' if success else 'failed'}")
