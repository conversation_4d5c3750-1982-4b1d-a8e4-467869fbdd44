# File: ftp.py (Modified)

import logging
import os
import webbrowser
import sys

# Try to import tkinter, but provide fallback if not available
try:
    import tkinter as tk
    from tkinter import simpledialog
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    logging.warning("tkinter module not available. Will use console input for credentials.")

# Import the stock process orchestrators - use relative imports
try:
    from stocks.adobe import upload_adobe_stock_process, \
        upload_to_adobe_sftp  # Also import direct sftp if needed elsewhere
    from stocks.shutterstock_csv_generator import extract_metadata, generate_shutterstock_csv
except ImportError as e:
    logging.error(f"Error importing stock modules: {e}")
    # Fallback to absolute imports if needed
    try:
        from Scripts.FTP.stocks.adobe import upload_adobe_stock_process, \
            upload_to_adobe_sftp
        from Scripts.FTP.stocks.shutterstock_csv_generator import extract_metadata, generate_shutterstock_csv
        logging.info("Successfully imported stock modules using absolute imports")
    except ImportError as e2:
        logging.error(f"Failed to import stock modules with both relative and absolute imports: {e2}")
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- Your existing HOST_CONFIGS (Adjust as needed) ---
HOST_CONFIGS = {
    'Pond5': {
        'url': 'https://www.pond5.com/index.php?page=my_uploads',
        'username': 'FootagesBank',
        'password': '********',
        'protocol': None, # Web-based upload
        'website_login': None, # Add fields for consistency if needed
        'website_password': None
    },
    'iStock': {
        'url': 'https://contributor.istockphoto.com/en/uploads',
        'username': None,
        'password': None,
        'protocol': None, # Web-based upload
        'website_login': None,
        'website_password': None
    },
    'Envato': {
        'url': 'https://contributor.author.envato.com/en/uploads',
        'username': None,
        'password': None,
        'protocol': None, # Web-based upload
        'website_login': None,
        'website_password': None
    },
    'Shutterstock': {
        'url': 'https://submit.shutterstock.com/', # Web URL for manual uploads
        'username': None,
        'password': None,
        'protocol': None, # No protocol - CSV generation only
        'website_login': None,
        'website_password': None
    },
    'Adobe Stock': {
        'url': 'https://contributor.stock.adobe.com/en/uploads?upload=1', # Web URL for info/manual access
        'sftp_hostname': 'sftp.contributor.adobestock.com', # Explicit SFTP host
        'sftp_username': '211350730', # SFTP Username (numeric ID) - prompt if None
        'sftp_password': None,         # SFTP Password - MUST prompt
        'protocol': 'sftp_plus_selenium', # Custom protocol identifier
        'website_login': '<EMAIL>',         # Website Login Email - MUST prompt
        'website_password': 'Rw55key1278#'          # Website Login Password - MUST prompt
    }
}

# --- Your existing open_website function ---
def open_website(host):
    config = HOST_CONFIGS.get(host)
    if config:
        # Use the web URL if available, otherwise maybe log that no web URL exists
        web_url = config.get('url')
        if web_url and web_url.startswith('http'):
             webbrowser.open(web_url)
             logging.info(f"Opened web URL {web_url} for {host}.")
        else:
             logging.info(f"No standard web URL configured to open for {host} (SFTP/FTPS host: {config.get('sftp_hostname') or config.get('url')}).")
    else:
        logging.warning(f"No configuration found for host: {host}")


# --- Function to generate Shutterstock CSV from files ---
def generate_shutterstock_csv_from_files(files_to_process):
    """
    Generates a CSV file for Shutterstock based on the provided files.

    Args:
        files_to_process (list): A list of file paths to process.

    Returns:
        str: Path to the generated CSV file, or None if generation failed.
    """
    logging.info(f"Generating Shutterstock CSV for {len(files_to_process)} files")

    try:
        # Extract metadata from each file
        metadata_list = []
        for file_path in files_to_process:
            metadata = extract_metadata(file_path)
            if metadata:
                metadata_list.append(metadata)

        if not metadata_list:
            logging.error("No metadata could be extracted from the files.")
            return None

        # Create output directory
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        csv_dir = os.path.join(desktop_path, "CSV", "Shutterstock")
        os.makedirs(csv_dir, exist_ok=True)

        # Generate CSV filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_csv_path = os.path.join(csv_dir, f"shutterstock_metadata_{timestamp}.csv")

        # Generate CSV
        success = generate_shutterstock_csv(metadata_list, output_csv_path)

        if success:
            logging.info(f"Shutterstock CSV generated: {output_csv_path}")
            return output_csv_path
        else:
            logging.error("Failed to generate Shutterstock CSV.")
            return None

    except Exception as e:
        logging.error(f"Error generating Shutterstock CSV: {e}")
        return None

def ask_continuation():
    """
    Creates a dialog displaying a message and a 'Done' button.
    When the 'Done' button is clicked, the window is closed.
    Falls back to console input if tkinter is not available.
    """
    if TKINTER_AVAILABLE:
        # Initialize the Tkinter root window
        root = tk.Tk()
        root.title("Continuation")

        # Set a fixed size for the window if desired (optional)
        root.geometry("300x150")

        # Create and pack a label with the specified text
        label = tk.Label(root, text="Enter FTP password - ********")
        label.pack(padx=20, pady=20)

        # Define the action for the 'Done' button to close the window
        def on_done():
            root.destroy()

        # Create and pack the 'Done' button
        done_button = tk.Button(root, text="Done", command=on_done)
        done_button.pack(pady=10)

        # Start the Tkinter main event loop
        root.mainloop()
    else:
        # Fallback to console input if tkinter is not available
        print("\nContinuation")
        print("Enter FTP password - ********")
        input("Press Enter to continue...")

# --- Modified ask_credentials to be more specific and handle missing tkinter ---
def ask_credentials(host, prompt_message, is_password=True, existing_value=None):
    """
    Prompts the user for a specific credential using Tkinter simpledialog or console input.
    """
    if existing_value:
        return existing_value # Don't prompt if value already exists

    if TKINTER_AVAILABLE:
        # Use tkinter dialog if available
        # Ensure Tkinter root doesn't interfere if run multiple times
        root = tk.Tk()
        root.withdraw() # Hide the main window

        value = simpledialog.askstring(
            title=f"{host} Credential",
            prompt=prompt_message,
            show='*' if is_password else None,
            # minsize=(400, 100) # Adjust size if needed
        )

        root.destroy() # Destroy the hidden root window

        # If user cancels, askstring returns None. Handle this gracefully.
        if value is None:
            logging.warning(f"User cancelled credential input for: {prompt_message}")
            return None # Or raise an error if credentials are required
    else:
        # Fallback to console input if tkinter is not available
        print(f"\n{host} Credential")
        print(prompt_message)
        if is_password:
            # Use getpass if available, otherwise fall back to regular input
            try:
                import getpass
                value = getpass.getpass()
            except ImportError:
                print("Warning: getpass module not available. Password will be visible.")
                value = input()
        else:
            value = input()

        if not value.strip():
            logging.warning(f"User provided empty input for: {prompt_message}")
            return None

    return value

# --- Shutterstock CSV generation function ---
def generate_shutterstock_csv_from_files(files_to_upload):
    """
    Generate a CSV file for Shutterstock based on the selected files.

    Args:
        files_to_upload (list): List of file paths to process

    Returns:
        tuple: (success, output_csv_path) where success is a boolean and
               output_csv_path is the path to the generated CSV file
    """
    try:
        # Extract metadata from each file
        metadata_list = []
        for file_path in files_to_upload:
            metadata = extract_metadata(file_path)
            if metadata:
                metadata_list.append(metadata)

        if not metadata_list:
            logging.error("No metadata could be extracted from the files.")
            return False, None

        # Create output directory
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        csv_dir = os.path.join(desktop_path, "CSV", "Shutterstock")
        os.makedirs(csv_dir, exist_ok=True)

        # Generate CSV filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_csv_path = os.path.join(csv_dir, f"shutterstock_metadata_{timestamp}.csv")

        # Generate CSV
        success = generate_shutterstock_csv(metadata_list, output_csv_path)

        if success:
            logging.info(f"Shutterstock CSV generated: {output_csv_path}")
            return True, output_csv_path
        else:
            logging.error("Failed to generate Shutterstock CSV.")
            return False, None

    except Exception as e:
        logging.error(f"Error generating Shutterstock CSV: {e}")
        return False, None

def upload_to_pond5(username, password, source):
    """
    Upload files to Pond5, handling both single paths and lists of paths.

    Args:
        username (str): Pond5 username
        password (str): Pond5 password
        source (str or list): Single path or list of paths to upload

    Returns:
        list: List of uploaded file basenames
    """
    from Scripts.FTP.stocks.pond5 import upload_pond5_process

    # Call the main Pond5 process with the source directly
    success = upload_pond5_process(username, password, source)

    # Return list of processed filenames
    if isinstance(source, str):
        return [os.path.basename(source)] if success else []
    else:  # source is list
        return [os.path.basename(p) for p in source] if success else []


# --- Modified process_hosts ---
def process_hosts(hosts_to_upload, source_path_or_list):
    """
    Processes uploads for the specified hosts based on their protocol.

    Args:
        hosts_to_upload (list): List of host names (e.g., ['Shutterstock', 'Adobe Stock']).
        source_path_or_list (str or list): The absolute path to the file/directory
                                            or a list of absolute file paths to upload.
    """
    if not isinstance(source_path_or_list, (str, list)):
        logging.error("Invalid source: Must be a string path or a list of paths.")
        return

    # Ensure source(s) are absolute paths for clarity
    if isinstance(source_path_or_list, str):
        abs_source = os.path.abspath(source_path_or_list)
        if not os.path.exists(abs_source):
            logging.error(f"Source path does not exist: {abs_source}")
            return
    else:  # It's a list
        abs_source = [os.path.abspath(p) for p in source_path_or_list]
        for p in abs_source:
            if not os.path.exists(p):
                logging.error(f"Source path in list does not exist: {p}")
                return
        if not abs_source:
            logging.warning("Source list is empty. Nothing to upload.")
            return

    # Helper function to determine upload source for Pond5:
    def get_upload_source(src):
        # For Pond5, if the source is a file, use its containing directory.
        return os.path.dirname(src) if os.path.isfile(src) else src

    for host in hosts_to_upload:
        logging.info(f"\n--- Processing Host: {host} ---")
        if host not in HOST_CONFIGS:
            logging.warning(f"No configuration found for host: {host}. Skipping.")
            continue

        config = HOST_CONFIGS[host]
        protocol = config.get('protocol')

        # --- Adobe Stock Special Handling ---
        if protocol == 'sftp_plus_selenium' and host == 'Adobe Stock':
            sftp_user = config.get('sftp_username')
            sftp_pass = config.get('sftp_password')
            web_login = config.get('website_login')
            web_pass = config.get('website_password')
            if config.get('url'):
                webbrowser.open(config['url'])

            if not sftp_user:
                sftp_user = ask_credentials(host, "Enter Adobe SFTP Username (Numeric ID):", is_password=False, existing_value=sftp_user)
            if not sftp_pass:
                sftp_pass = ask_credentials(host, "Enter Adobe FTP Password:", is_password=True, existing_value=sftp_pass)
            if not web_login:
                web_login = ask_credentials(host, "Enter Adobe Contributor WEBSITE Login Email:", is_password=False, existing_value=web_login)
            if not web_pass:
                web_pass = ask_credentials(host, f"Enter Adobe Contributor WEBSITE Password for {web_login}:", is_password=True, existing_value=web_pass)

            if not all([sftp_user, sftp_pass, web_login, web_pass]):
                logging.error(f"Missing required credentials for Adobe Stock ({host}). Skipping.")
                continue

            logging.info("Starting Adobe Stock SFTP and Selenium CSV upload process...")
            success = upload_adobe_stock_process(sftp_user, sftp_pass, web_login, web_pass, abs_source)
            logging.info(f"Adobe Stock process finished for {host}. Success: {success}")

        # --- Shutterstock CSV Generation ---
        elif host == 'Shutterstock':
            logging.info(f"Processing Shutterstock - generating CSV file only.")
            csv_path = generate_shutterstock_csv_from_files(abs_source)
            if csv_path:
                logging.info(f"Successfully generated Shutterstock CSV at: {csv_path}")
                # Open the directory containing the CSV file
                try:
                    csv_dir = os.path.dirname(csv_path)
                    if os.path.exists(csv_dir):
                        os.startfile(csv_dir) if sys.platform == 'win32' else webbrowser.open(f'file://{csv_dir}')
                        logging.info(f"Opened directory containing the CSV file: {csv_dir}")
                except Exception as e:
                    logging.error(f"Error opening CSV directory: {e}")
            else:
                logging.error(f"Failed to generate Shutterstock CSV.")
            continue

        # --- FTPS Handling ---
        elif protocol == 'ftps':

            username = config.get('username')
            password = config.get('password')
            hostname = config.get('url')  # The 'url' here is the FTPS hostname

            if not hostname:
                logging.error(f"Missing FTPS hostname ('url') in config for {host}. Skipping.")
                continue

            if not username:
                username = ask_credentials(host, f"Enter {host} FTPS Username:", is_password=False, existing_value=username)
            if not password:
                password = ask_credentials(host, f"Enter {host} FTPS Password for user {username}:", is_password=True, existing_value=password)

            if not username or not password:
                logging.error(f"Missing credentials for {host}. Skipping.")
                continue

            logging.info(f"Starting FTPS upload process for {host} ({hostname})...")
            try:
                # Handle other FTPS hosts if needed in the future
                logging.warning(f"No FTPS upload handler implemented for {host}. Skipping.")

            except Exception as e:
                logging.error(f"Error during FTPS upload to {host}: {e}", exc_info=True)

        # --- SFTP Handling (Generic) ---
        elif protocol == 'sftp':
            username = config.get('sftp_username')
            password = config.get('sftp_password')
            hostname = config.get('sftp_hostname')

            if not hostname:
                logging.error(f"Missing SFTP hostname in config for {host}. Skipping.")
                continue

            if not username:
                username = ask_credentials(host, f"Enter {host} SFTP Username:", is_password=False, existing_value=username)
            if not password:
                password = ask_credentials(host, f"Enter {host} SFTP Password for user {username}:", is_password=True, existing_value=password)

            if not username or not password:
                logging.error(f"Missing credentials for {host}. Skipping.")
                continue

            logging.info(f"Starting SFTP upload to {host} ({hostname})...")
            try:
                uploaded = upload_to_adobe_sftp(username, password, abs_source, hostname)
                if uploaded:
                    logging.info(f"SFTP Upload to {host} completed for {len(uploaded)} files.")
                else:
                    logging.warning(f"SFTP Upload to {host} did not report any uploaded files.")
            except Exception as e:
                logging.error(f"Error during SFTP upload to {host}: {e}", exc_info=True)

        # --- Web-Based (No Protocol or 'None') ---
        # --- FTP Handling (Example: Pond5 where protocol is None) ---
        elif not protocol:
            # Assuming 'None' protocol is specifically for plain FTP for hosts like Pond5
            # Add a check if this assumption needs to be stricter
            if host == 'Pond5':  # Make sure this block only applies to Pond5 or intended FTP hosts
                logging.info(f"Detected {host} with no protocol, proceeding with plain FTP upload.")
                username = config.get('username')
                password = config.get('password')
                # Assuming 'url' in Pond5 config IS the FTP hostname, similar to FTPS config
                hostname = config.get('url')

                if not hostname:
                    logging.error(f"Missing FTP hostname ('url' expected) in config for {host}. Skipping.")
                    continue

                # Prompt if needed
                if not username:
                    username = ask_credentials(host, f"Enter {host} FTP Username:", is_password=False,
                                               existing_value=username)
                if not password:
                    password = ask_credentials(host, f"Enter {host} FTP Password for user {username}:",
                                               is_password=True, existing_value=password)

                if not username or not password:
                    logging.error(f"Missing credentials for {host}. Skipping.")
                    continue

                logging.info(f"Starting FTP upload to {host} ({hostname})...")
                try:
                    # --- MODIFICATION FOR MULTI-FILE FTP UPLOAD ---
                    # Pass the source (which can be a single path string OR a list of paths)
                    # DIRECTLY to the upload function.
                    # IMPORTANT: This requires upload_to_pond5 to be implemented to handle
                    #            both a single string path and a list of string paths correctly.
                    #            It also needs to handle potential path issues internally.
                    # Note: Assuming upload_to_pond5 doesn't need hostname, based on original code structure
                    uploaded = upload_to_pond5(username, password, abs_source)

                    # The return value 'uploaded' should ideally be a list of basenames
                    # returned by upload_to_pond5, regardless of whether input was str or list.
                    if uploaded:  # Check if list is not None and not empty
                        # Determine the number of files intended vs reported
                        num_intended = 1 if isinstance(abs_source, str) else len(abs_source)
                        num_reported = len(uploaded)
                        logging.info(
                            f"FTP Upload to {host} reported {num_reported} file(s) completed (intended: {num_intended}).")
                        # You might want more robust checking here based on what upload_to_pond5 returns
                    elif uploaded == []:  # Explicit empty list might mean success but nothing uploaded
                        logging.warning(
                            f"FTP Upload to {host} reported success, but no files were uploaded (function returned empty list).")
                    else:  # Function might return None or other non-list on failure
                        logging.warning(
                            f"FTP Upload to {host} did not report any uploaded files (function might have failed or returned None).")
                    # --- END OF MODIFICATION ---

                except FileNotFoundError as fnf_err:
                    # Catching specific errors might help diagnose path issues inside upload_to_pond5
                    logging.error(
                        f"Error during FTP upload to {host}: File not found - {fnf_err}. Check path validity and function implementation.",
                        exc_info=False)  # Keep exc_info False if the traceback isn't helpful here
                except Exception as e:
                    logging.error(f"Error during FTP upload to {host}: {e}", exc_info=True)

            else:
                # Fallback for other hosts with 'protocol: None' if they aren't meant for FTP
                logging.info(
                    f"{host} is configured with no protocol and is not Pond5. Opening website (default action).")
                open_website(host)  # Or handle as needed

    logging.info("\n--- All specified hosts processed. ---")
