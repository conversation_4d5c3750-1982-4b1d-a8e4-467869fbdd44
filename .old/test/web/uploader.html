<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Uploader</title>
    <link rel="stylesheet" href="uploader.css">
</head>
<body>
    <!-- Completion Indicator Overlay -->
    <div id="completion-overlay" class="completion-overlay">
        <div class="completion-circle">
            <svg width="40" height="40" viewBox="0 0 50 50">
                <path class="checkmark" d="M14,27 L22,35 L36,15" />
            </svg>
        </div>
    </div>

    <div class="container">
        <h1 class="title">Stock Uploader v1.0</h1>

        <p id="progress-label" class="progress-label initial-warning">iStock accepts only videos!</p>

        <!-- Drop Area -->
        <div id="drop-area" class="drop-area">
            <p>Drop Files or Folder Here</p>
            <!-- Optional: List dropped files here -->
            <!-- <ul id="file-list"></ul> -->
        </div>

        <p class="upload-label">Upload to:</p>

        <!-- Checkboxes -->
        <div class="checkbox-container">
            <div class="checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="adobe" name="host" value="Adobe Stock" checked>
                    <span class="checkmark"></span> Adobe Stock
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="shutterstock" name="host" value="Shutterstock" checked>
                    <span class="checkmark"></span> Shutterstock
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="envato" name="host" value="Envato">
                    <span class="checkmark"></span> Envato
                </label>
            </div>
             <div class="checkbox-group">
                 <label class="checkbox-label">
                    <input type="checkbox" id="pond5" name="host" value="Pond5">
                    <span class="checkmark"></span> Pond5
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="istock" name="host" value="iStock">
                    <span class="checkmark"></span> iStock
                </label>
            </div>
        </div>

        <!-- Buttons -->
        <div class="button-container">
            <button id="review-btn" class="btn review-btn">View in Gallery</button>
            <button id="upload-btn" class="btn upload-btn">Upload</button>
        </div>

    </div>

    <script src="uploader.js"></script>
</body>
</html>