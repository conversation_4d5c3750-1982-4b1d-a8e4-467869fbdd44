<!-- START OF FILE web/gallery.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meta-Reviewer</title>
    <!-- Link to the provided CSS file -->
    <link rel="stylesheet" href="key.css">
    <!-- Link to gallery-specific CSS -->
    <link rel="stylesheet" href="gallery.css">
    <style>
        /* --- MODIFIED FOR FULL WIDTH --- */
        body {
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
            margin: 0;
            padding: 0; /* Remove body padding, handled by main-container */
            height: 100vh;
            overflow: hidden;
            /* Override key.css body centering */
            justify-content: flex-start; /* Align content to the top */
            align-items: stretch;      /* Stretch children (main-container) full width */
        }

        .main-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            /* Padding inside the main area */
            padding: 15px;
            overflow: hidden;
            /* Ensure it takes full width */
            width: 100%;
            max-width: none; /* Override any max-width from key.css */
            box-sizing: border-box; /* Include padding in width calculation */
        }
        /* --- END OF MODIFICATIONS --- */

        .button-bar {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-shrink: 0;
        }
         .button-bar .btn {
             width: auto;
             min-width: 150px;
             padding: 0 20px;
             max-width: 200px;
        }

        .content-container {
            display: flex;
            flex-grow: 1;
            gap: 15px;
            overflow: hidden;
             min-height: 0; /* Fix flexbox overflow issue */
             /* Takes full width of its parent (main-container's content box) */
        }

        .gallery-panel {
            flex-grow: 1; /* Takes remaining space */
            display: flex;
            flex-direction: column;
            background-color: rgba(230, 230, 230, 0.5);
            border: 1px solid rgba(200, 200, 200, 0.4);
            border-radius: 10px;
            padding: 10px;
            overflow: hidden;
             min-width: 0; /* Fix flexbox overflow issue */
        }

         #gallery-grid {
             flex-grow: 1;
             overflow-y: auto;
             padding-right: 5px;
         }


        .metadata-panel {
            width: 380px; /* Fixed width */
            flex-shrink: 0; /* Prevent shrinking */
            display: flex;
            flex-direction: column;
            gap: 8px;
            background-color: rgba(230, 230, 230, 0.5);
            border: 1px solid rgba(200, 200, 200, 0.4);
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
        }

        .status-bar {
            padding: 5px 15px;
            font-size: 12px;
            color: #555;
            background-color: rgba(220, 220, 220, 0.6);
            border-top: 1px solid rgba(200, 200, 200, 0.4);
            flex-shrink: 0;
            min-height: 25px;
            display: flex;
            align-items: center;
        }
        .status-bar.error { color: #D32F2F; font-weight: bold; }
        .status-bar.success { color: #388E3C; font-weight: bold; }
        .status-bar.warning { color: #F57C00; font-weight: bold; }
        .status-bar.info { color: #1976D2; }


        /* Styles for metadata labels and inputs */
        .metadata-panel .settings-label {
            font-size: 14px;
            margin-bottom: 0;
            padding-left: 0;
        }
        .metadata-panel .settings-input,
        .metadata-panel textarea {
            width: 100%;
            padding: 8px 10px;
            font-size: 13px;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.7);
            color: #333;
            margin-bottom: 5px;
            resize: vertical;
            min-height: 80px;
        }
        .meta-info {
            width: 100%;
            padding: 8px 10px;
            font-size: 13px;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.7);
            color: #333;
            margin-bottom: 5px;
            resize: vertical;
            min-height: 20px;
        }
         .metadata-panel textarea {
             min-height: 80px;
             font-family: inherit;
         }
        .metadata-panel input[readonly] {
             background-color: rgba(230, 230, 230, 0.4);
             cursor: default;
             border-color: rgba(0,0,0,0.1);
        }
        .metadata-panel input[readonly]:focus {
             outline: none;
             box-shadow: none;
             border-color: rgba(0,0,0,0.1);
        }
         .info-frame {
             display: flex;
             align-items: center;
             gap: 8px;
             margin-top: 10px;
         }
         .info-frame .settings-label { flex-shrink: 0; margin-bottom: 0; }
         .info-frame .settings-input { width: 80px; height: 20px; margin-bottom: 0; text-align: center; }
         .metadata-buttons {
             display: flex;
             flex-direction: column;
             gap: 10px;
             margin-top: 15px;
             border-top: 1px solid rgba(200, 200, 200, 0.5);
             padding-top: 15px;
         }
         .metadata-buttons .btn { max-width: none; height: 40px; font-size: 15px; }
         .metadata-buttons .btn:disabled { background: linear-gradient(145deg, #cccccc, #b3b3b3); cursor: not-allowed; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); transform: none; }
    </style>
</head>
<body>
    <!-- Error Indicator Overlay -->
    <div id="error-overlay" class="error-overlay">
        <div class="error-circle">
            <svg width="40" height="40" viewBox="0 0 50 50">
                <path class="crossmark" d="M15,15 L35,35 M15,35 L35,15" />
            </svg>
        </div>
    </div>

    <div class="main-container">
        <!-- Title -->
        <h1 class="title">Gallery v1.0</h1> <!-- Use title class from key.css -->

        <!-- Top Button Bar -->
        <div class="button-bar">
            <button id="stats-btn" class="btn" disabled>Statistics</button>
            <button id="reload-folder-btn" class="btn" disabled>Reload</button>
            <button id="open-folder-btn" class="btn">Open Folder</button>
            <button id="ai-keyworder-btn" class="btn">AI-Keyworder</button>
            <button id="stock-uploader-btn" class="btn">Stock Uploader</button>
        </div>

        <!-- Main Content Area (Gallery + Metadata) -->
        <div class="content-container">
            <!-- Left Panel: Image Gallery -->
            <div class="gallery-panel">
                <div id="gallery-grid" class="gallery-grid">
                    <!-- Thumbnails will be loaded here by JavaScript -->
                     <p id="gallery-placeholder" style="text-align: center; color: #777; margin-top: 50px; font-size: 16px;">Select a folder to view images.</p>
                </div>
            </div>

            <!-- Right Panel: Metadata Editor -->
            <div class="metadata-panel">
                <label for="title-entry" class="settings-label">Title:</label>
                <textarea id="title-entry" class="settings-input" rows="3"></textarea>

                <label for="desc-entry" class="settings-label">Description:</label>
                <textarea id="desc-entry" class="settings-input" rows="4"></textarea>

                <label for="keywords-entry" class="settings-label">Keywords:</label>
                <textarea id="keywords-entry" class="settings-input" rows="6"></textarea>

                <div class="info-frame">
                    <label for="filetype-entry" class="settings-label">File Type:</label>
                    <input type="text" id="filetype-entry" class="meta-info" readonly>
                    <label for="kw-count-entry" class="settings-label">Count:</label>
                    <input type="text" id="kw-count-entry" class="meta-info" readonly>
                </div>

                    <div class="metadata-buttons">
                        <button id="auto-keyword-btn" class="btn" disabled>Auto Keyword</button>
                        <button id="save-changes-btn" class="btn" disabled>Save Changes</button>
                        <button id="select-preview-btn" class="btn" disabled>Select Preview</button>
                    </div>
                 <!-- Hidden input to store the path of the selected image -->
                 <input type="hidden" id="selected-image-path" value="">
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div id="status-bar" class="status-bar">Ready</div>

    <!-- Include the JavaScript file -->
    <script src="gallery.js"></script>
</body>
</html>
<!-- END OF FILE web/gallery.html -->