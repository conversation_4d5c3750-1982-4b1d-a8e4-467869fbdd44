# ai_keywording.py

# Keep all your existing imports at the top:
import logging
import os
import re
from os import path
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
import cv2
from PIL import Image
import piexif
from config import API_KEY, MODEL # Make sure config.py is in StockTools directory
from Scripts.utils import check_for_matching_video # Make sure utils.py is in Scripts directory

# Global variables to store processing results and errors
glob_keys = []
glob_titles = []
errs = 0
errs_files = []

def ai_keywording(file_path, custom_prompt, is_editorial=False, place="", country="", time=""):
    global glob_titles, glob_keys, errs, errs_files
    print(f"DEBUG AIK: START for {os.path.basename(file_path)}", flush=True)
    keywords = None # Initialize to ensure they exist for return
    description = None # Initialize

    try:
        print(f"DEBUG AIK: 1. Configuring GenAI for {os.path.basename(file_path)}", flush=True)
        genai.configure(api_key=API_KEY)
        print(f"DEBUG AIK: 2. GenAI configured for {os.path.basename(file_path)}", flush=True)

        is_video = False
        video_path_for_frames = None
        file_extension = os.path.splitext(file_path)[1].lower()

        if file_extension in ('.mp4', '.mov', '.avi', '.wmv'):
            is_video = True
            video_path_for_frames = file_path
        else:
            print(f"DEBUG AIK: Checking for matching video for image {os.path.basename(file_path)}", flush=True)
            has_matching_video, matching_video = check_for_matching_video(file_path)
            print(f"DEBUG AIK: Matching video check result: has_matching_video={has_matching_video}, matching_video={matching_video}", flush=True)
            if has_matching_video and matching_video:
                is_video = True
                video_path_for_frames = matching_video

        gemini_parts_list = []
        text_prompt = (
            f"""You are an AI expert stock media keyworder. Your goal is to generate a description and EXACTLY 50 unique, commercially valuable keywords a buyer would use. Emulate the style of top-performing stock media.

**REFERENCE EXAMPLE - KEYWORD STYLE & CONTENT (Aerial Amazon Shot):**
(Keywords like: amazon, forest, brazil, rainforest, amazonia, america, river, nature, south, landscape, jungle, brasil, peru, aerial, manaus, latin, rain, tree, water, ecuador, top, valley, sunset, above, drone, scenery, venezuela, sunrise, travel, national, overview, green, tropical, natural, dusk, latino, exotic, outdoor, amazonas, anavilhanas, rio negro, island, flooded forest, archipelago, canopy, environment, wilderness, remote, park...)
*Notice the mix of specific subjects, locations, regional terms, **and shot types like "aerial", "drone".***

**YOUR TASK:**
Analyze the provided asset (video/image). **If `custom_prompt` is provided (e.g., specifying "aerial", "close-up", "timelapse"), its details are PARAMOUNT. This information MUST be reflected in the description and as high-priority keywords.** {f"User Context: {custom_prompt}" if custom_prompt else ""}
For videos, consider start/end frames for progression (especially if "timelapse" is mentioned in a user context).

**Description Guidelines (15-30 words):**
*   Factual, concise: Main subject(s), key action/scene, specific visible named location(s) (e.g., "Anavilhanas, Rio Negro, Brazil"). Mention the location at the end!
*   **If `custom_prompt` specifies a shot type, incorporate it naturally (e.g., "Aerial view of...", "Timelapse sequence of...", "Close-up on...").**
*   AVOID: Subjective adjectives (stunning, beautiful, rich), fluff (pristine, scenic, paradise, tranquility), technical/photography terms (latitude, longitude, exposure, perspective), academic jargon (biodiversity, ecosystem, topography, habitat, flora, fauna, seasonal), and filler words (photo, image, showing).
{f"*   ADD TO THE END of the description: {place}, {country}, {time}." if is_editorial else ""}

**Keyword Guidelines (Exactly 50, numbered 1-50):**
1.  **CONTENT & PRIORITY:**
    *   **Core (Top 15-20):**
        *   **Shot types/techniques from `custom_prompt` (e.g., aerial, close-up, timelapse, drone, POV) or clearly evident visuals MUST be high-priority keywords.**
        *   Primary visual subjects (e.g., forest, river, building, person).
        *   Specific visible named locations (e.g., Anavilhanas, Rio Negro, Eiffel Tower).
        *   Primary country/city of the asset.
        *   Broader, highly relevant geographical terms (e.g., Amazonia, South America, Europe). These should be high in the list.
    *   **Supporting:** Other distinct visible elements, composition (if not in `custom_prompt`), general scene descriptors (jungle, urban, travel, nature - if truly applicable and concrete).
    *   **Geographic Expansion (to help reach 50 valuable keywords):** If the asset is from a well-known region (like the Amazon, Alps, Sahara, etc.), include other commonly associated and searched-for countries or specific sub-regions as keywords. Refer to the Reference Example.
2.  **STYLE:**
    *   Primarily single, impactful, concrete words.
    *   Common 2-word phrases for locations or very common terms (e.g., "Rio Negro", "South America", "rain forest", "close up"). DO NOT MERGE WORDS unless it's a standard compound (e.g., "rainforest").
3.  **CRITICALLY AVOID AS KEYWORDS:**
    *   The specific words/categories listed in the Description's AVOID section (biodiversity, latitude, longitude, exposure, perspective, seasonal, rich, pristine, paradise, tranquility, flora, fauna, ecosystem, etc.).
    *   Vague or unsearchable phrases.
    *   Duplicates.

**Output Format (Follow EXACTLY, NO special symbols except for commas, columns and dots!):**
Description: [Your 15-30 word description here]
Keywords: 1. keyword, 2. keyword, ..., 50. keyword
"""
        )
        gemini_parts_list.append(text_prompt)
        print(f"DEBUG AIK: Text prompt added for {os.path.basename(file_path)}", flush=True)

        pil_images_to_send = []

        if is_video and video_path_for_frames:
            print(f"DEBUG AIK: 3a. Processing video frames from: {video_path_for_frames}", flush=True)
            cap = cv2.VideoCapture(video_path_for_frames)
            print(f"DEBUG AIK: 3b. cv2.VideoCapture object created for {video_path_for_frames}. Is opened: {cap.isOpened()}", flush=True)
            if not cap.isOpened():
                print(f"DEBUG AIK: ERROR - Could not open video file: {video_path_for_frames}", flush=True)
                raise ValueError(f"Could not open video file: {video_path_for_frames}")

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0: fps = 30
            print(f"DEBUG AIK: 3c. Video info: total_frames={total_frames}, fps={fps} for {video_path_for_frames}", flush=True)

            print(f"DEBUG AIK: 3d. Setting frame pos to 0 for {video_path_for_frames}", flush=True)
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            print(f"DEBUG AIK: 3e. Reading first frame from {video_path_for_frames}", flush=True)
            ret_first, first_frame_cv = cap.read()
            if not ret_first:
                print(f"DEBUG AIK: ERROR - Could not read first frame from {video_path_for_frames}", flush=True)
                cap.release()
                raise ValueError(f"Could not read first frame from video: {video_path_for_frames}")
            print(f"DEBUG AIK: 3f. First frame read successfully for {video_path_for_frames}", flush=True)

            last_frame_pos = max(0, min(total_frames - 1, int(total_frames - fps * 1)))
            print(f"DEBUG AIK: 3g. Setting frame pos to {last_frame_pos} for last frame for {video_path_for_frames}", flush=True)
            cap.set(cv2.CAP_PROP_POS_FRAMES, last_frame_pos)
            print(f"DEBUG AIK: 3h. Reading last frame from {video_path_for_frames}", flush=True)
            ret_last, last_frame_cv = cap.read()
            if not ret_last:
                print(f"DEBUG AIK: ERROR - Could not read last frame from {video_path_for_frames}", flush=True)
                cap.release()
                raise ValueError(f"Could not read last frame from video: {video_path_for_frames}")
            print(f"DEBUG AIK: 3i. Last frame read successfully for {video_path_for_frames}", flush=True)

            cap.release()
            print(f"DEBUG AIK: 3j. cv2.VideoCapture released for {video_path_for_frames}", flush=True)

            print(f"DEBUG AIK: 3k. Converting first frame to RGB for {video_path_for_frames}", flush=True)
            first_frame_rgb = cv2.cvtColor(first_frame_cv, cv2.COLOR_BGR2RGB)
            print(f"DEBUG AIK: 3l. Converting last frame to RGB for {video_path_for_frames}", flush=True)
            last_frame_rgb = cv2.cvtColor(last_frame_cv, cv2.COLOR_BGR2RGB)

            print(f"DEBUG AIK: 3m. Creating PIL Image from first frame for {video_path_for_frames}", flush=True)
            first_img_pil = Image.fromarray(first_frame_rgb)
            print(f"DEBUG AIK: 3n. Creating PIL Image from last frame for {video_path_for_frames}", flush=True)
            last_img_pil = Image.fromarray(last_frame_rgb)

            scaling_factor = 2
            print(f"DEBUG AIK: 3o. Resizing first PIL image for {video_path_for_frames}", flush=True)
            first_img_pil = first_img_pil.resize((first_img_pil.width // scaling_factor, first_img_pil.height // scaling_factor), Image.LANCZOS)
            print(f"DEBUG AIK: 3p. Resizing last PIL image for {video_path_for_frames}", flush=True)
            last_img_pil = last_img_pil.resize((last_img_pil.width // scaling_factor, last_img_pil.height // scaling_factor), Image.LANCZOS)

            pil_images_to_send.extend([first_img_pil, last_img_pil])
            print(f"DEBUG AIK: 3q. Video frames processed and added to pil_images_to_send for {video_path_for_frames}", flush=True)

        if file_extension not in ('.mp4', '.mov', '.avi', '.wmv'):
            if os.path.exists(file_path):
                try:
                    print(f"DEBUG AIK: 4a. Processing image file: {file_path}", flush=True)
                    img = Image.open(file_path)
                    print(f"DEBUG AIK: 4b. Image.open successful for {file_path}", flush=True)
                    if img.mode == 'RGBA' or img.mode == 'P':
                        print(f"DEBUG AIK: 4c. Converting image {file_path} from {img.mode} to RGB", flush=True)
                        img = img.convert('RGB')

                    scaling_factor = 2
                    new_size = (img.width // scaling_factor, img.height // scaling_factor)
                    print(f"DEBUG AIK: 4d. Resizing image {file_path} to {new_size}", flush=True)
                    img = img.resize(new_size, Image.LANCZOS)
                    pil_images_to_send.append(img)
                    print(f"DEBUG AIK: 4e. Image {file_path} processed and added to pil_images_to_send", flush=True)
                except FileNotFoundError:
                    print(f"DEBUG AIK: WARNING - Image file not found (during Image.open): {file_path}", flush=True)
                    logging.warning(f"Image file not found: {file_path}. Proceeding without this specific image.")
                except Exception as e:
                    print(f"DEBUG AIK: ERROR - Processing image file {file_path}: {e}", flush=True)
                    logging.error(f"Error processing image file {file_path}: {e}. Proceeding without this image.")
            else:
                print(f"DEBUG AIK: WARNING - Image file {file_path} does not exist (checked by os.path.exists)", flush=True)
                logging.warning(f"Image file {file_path} does not exist.")

        if not pil_images_to_send:
            print(f"DEBUG AIK: 5. No images or video frames could be prepared for {os.path.basename(file_path)}. Proceeding with text prompt only.", flush=True)
            logging.warning(f"No images or video frames could be prepared for {file_path}. Proceeding with text prompt only.")
        else:
            print(f"DEBUG AIK: 5. Appending {len(pil_images_to_send)} PIL image(s) to gemini_parts_list for {os.path.basename(file_path)}", flush=True)
            for idx, pil_image in enumerate(pil_images_to_send):
                 gemini_parts_list.append(pil_image)
                 print(f"DEBUG AIK: 5_{idx}. Appended PIL image to gemini_parts_list", flush=True)

        print(f"DEBUG AIK: 6. Initializing GenerativeModel ({MODEL}) for {os.path.basename(file_path)}", flush=True)
        model = genai.GenerativeModel(MODEL)
        generation_config = genai.types.GenerationConfig(
            temperature=0.8,
            max_output_tokens=4096
        )
        print(f"DEBUG AIK: 6b. GenerativeModel initialized for {os.path.basename(file_path)}", flush=True)

        print(f"DEBUG AIK: 7. Sending request to Gemini for {os.path.basename(file_path)} with {len(pil_images_to_send)} image(s).", flush=True)
        response = model.generate_content(
            contents=gemini_parts_list,
            generation_config=generation_config
        )
        print(f"DEBUG AIK: 8. Gemini response received for {os.path.basename(file_path)}", flush=True)

        if not response.parts and hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
            block_msg = f"Gemini API request blocked for {file_path}. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}"
            print(f"DEBUG AIK: ERROR - {block_msg}", flush=True)
            raise ValueError(block_msg)
        if not response.text:
             empty_msg = f"Gemini API response for {file_path} is empty or malformed. Full response: {response}"
             print(f"DEBUG AIK: ERROR - {empty_msg}", flush=True)
             raise ValueError(empty_msg)
        print(f"DEBUG AIK: 9. Gemini response seems valid for {os.path.basename(file_path)}", flush=True)

        content = response.text
        print(f"DEBUG AIK: 10. Parsing Gemini response content for {os.path.basename(file_path)}", flush=True)
        description_match = re.search(r'Description:\s*(.*?)\s*Keywords:', content, re.DOTALL | re.IGNORECASE)
        keywords_match = re.search(r'Keywords:\s*(.*)', content, re.DOTALL | re.IGNORECASE)

        if not description_match or not keywords_match:
            parse_fail_msg = f"Failed to parse Gemini API response for {file_path}. Raw response: {content}"
            print(f"DEBUG AIK: ERROR - {parse_fail_msg}", flush=True)
            logging.error(parse_fail_msg)
            raise ValueError("Failed to parse the API response for description and keywords.")
        print(f"DEBUG AIK: 11. Description and Keywords regex matches found for {os.path.basename(file_path)}", flush=True)

        description = description_match.group(1).strip()
        keywords_raw = keywords_match.group(1).strip()
        keywords_list = re.split(r'\s*,\s*|\s*\n\s*', keywords_raw)
        keywords = [re.sub(r'^\d+\.\s*', '', kw).strip().lower() for kw in keywords_list if kw.strip()]
        keywords = [kw for kw in keywords if kw] # Remove any empty strings after processing
        print(f"DEBUG AIK: 12. Parsed: Description='{description[:30]}...', Keywords_count={len(keywords)} for {os.path.basename(file_path)}", flush=True)

        glob_titles.append(description)
        glob_keys.append(keywords)

        print(f"--- Successfully processed: {os.path.basename(file_path)} ---", flush=True)
        # ... (other prints)

    except google_exceptions.NotFound as e:
        print(f"DEBUG AIK: ERROR - Gemini API Model Error for {os.path.basename(file_path)}: {e}", flush=True)
        logging.error(f"Gemini API Model Error (e.g., model not found) processing file {file_path} with model {MODEL}: {e}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None # Return None, None explicitly
    except google_exceptions.GoogleAPIError as e:
        print(f"DEBUG AIK: ERROR - Gemini API Error for {os.path.basename(file_path)}: {e}", flush=True)
        logging.error(f"Gemini API Error processing file {file_path}: {e}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    except ValueError as e:
        print(f"DEBUG AIK: ERROR - ValueError for {os.path.basename(file_path)}: {e}", flush=True)
        logging.error(f"ValueError processing file {file_path}: {e}")
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    except Exception as e:
        print(f"DEBUG AIK: CRITICAL ERROR in ai_keywording for {os.path.basename(file_path)}: {type(e).__name__} - {e}", flush=True)
        logging.error(f"Generic error processing file {file_path}: {e}", exc_info=True)
        errs += 1
        errs_files.append(os.path.basename(file_path))
        return None, None
    finally:
        print(f"DEBUG AIK: FINALLY for {os.path.basename(file_path)}", flush=True)

    print(f"DEBUG AIK: END for {os.path.basename(file_path)}. Returning desc_len={len(description) if description else 0}, keys_len={len(keywords) if keywords else 0}", flush=True)
    return keywords, description



def update_exif(file_path, keywords, title):
    # Get logger instance if you use specific logging calls like warning() or error() directly here
    # otherwise, they might be from the global logging config.
    # For safety, let's assume module-level logging might be used.
    # from logging import warning, error # Can be here or ensure they are in scope

    print(f"DEBUG UPEXIF: START for {path.basename(file_path)} (XMP SKIPPED)", flush=True)
    exif_success = False # Specifically track EXIF success

    try:
        print(f"DEBUG UPEXIF: 1. Attempting to read image with PIL for {path.basename(file_path)}", flush=True)
        # First, try to open the image with PIL to ensure it's a valid image file
        img = Image.open(file_path)
        print(f"DEBUG UPEXIF: 2. PIL Image open successful for {path.basename(file_path)}", flush=True)

        # --- Prepare Keyword List for EXIF ---
        try:
            print(f"DEBUG UPEXIF: 2a. Formatting keywords for EXIF for {path.basename(file_path)}", flush=True)
            # Your existing keyword formatting logic
            formatted_keywords = [item.split('. ', 1)[-1] for item in keywords]
            if formatted_keywords:
                 formatted_keywords[-1] = formatted_keywords[-1].replace(".", "")
            else:
                 formatted_keywords = []
            print(f"DEBUG UPEXIF: 2b. EXIF Keywords formatted (count: {len(formatted_keywords)}) for {path.basename(file_path)}", flush=True)
        except Exception as fmt_err:
            print(f"DEBUG UPEXIF: WARNING - Could not format keywords for EXIF for {path.basename(file_path)}, using raw list: {fmt_err}", flush=True)
            # Consider using logging.warning if you have a logger configured
            # warning(f"Could not format keywords for {file_path}, using raw list: {fmt_err}")
            formatted_keywords = list(keywords)

        # --- Try to get existing EXIF data or create new ---
        try:
            print(f"DEBUG UPEXIF: 2c. Getting existing EXIF data for {path.basename(file_path)}", flush=True)
            exif_dict = piexif.load(file_path)
            print(f"DEBUG UPEXIF: 2d. Successfully loaded existing EXIF data for {path.basename(file_path)}", flush=True)
        except Exception as e:
            print(f"DEBUG UPEXIF: 2e. No existing EXIF data found or error loading it for {path.basename(file_path)}: {e}", flush=True)
            # Create a new EXIF dictionary with empty IFDs (Image File Directories)
            exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}

        # --- Prepare EXIF Data Dictionary ---
        # In piexif, we need to use the appropriate tags from piexif.ImageIFD
        # For XPKeywords, XPTitle, and XPSubject, we need to encode the strings as UTF-16LE with BOM
        print(f"DEBUG UPEXIF: 3a. Preparing EXIF data for {path.basename(file_path)}", flush=True)

        # Encode strings as UTF-16LE with BOM for Windows compatibility
        def encode_for_xp(text):
            if text:
                # UTF-16LE encoding with BOM
                encoded_text = text.encode('utf-16le')
                return encoded_text
            return b''

        # Join keywords with semicolons
        keywords_str = ";".join(formatted_keywords) if formatted_keywords else ""

        # Set the EXIF tags in the 0th IFD
        if keywords_str:
            exif_dict["0th"][piexif.ImageIFD.XPKeywords] = encode_for_xp(keywords_str)
        elif piexif.ImageIFD.XPKeywords in exif_dict["0th"]:
            del exif_dict["0th"][piexif.ImageIFD.XPKeywords]

        if title:
            exif_dict["0th"][piexif.ImageIFD.XPTitle] = encode_for_xp(title)
            exif_dict["0th"][piexif.ImageIFD.XPSubject] = encode_for_xp(title)
        else:
            if piexif.ImageIFD.XPTitle in exif_dict["0th"]:
                del exif_dict["0th"][piexif.ImageIFD.XPTitle]
            if piexif.ImageIFD.XPSubject in exif_dict["0th"]:
                del exif_dict["0th"][piexif.ImageIFD.XPSubject]

        print(f"DEBUG UPEXIF: 3b. EXIF data prepared for {path.basename(file_path)}", flush=True)

        # --- XMP DATA PREPARATION AND MODIFICATION IS NOW SKIPPED ---
        print(f"DEBUG UPEXIF: SKIPPING XMP data preparation and modification for {path.basename(file_path)}.", flush=True)

        # --- Attempt to Modify EXIF Metadata ---
        try:
            print(f"DEBUG UPEXIF: 4a. Converting EXIF dict to bytes for {path.basename(file_path)}", flush=True)
            exif_bytes = piexif.dump(exif_dict)
            print(f"DEBUG UPEXIF: 4b. Saving image with updated EXIF for {path.basename(file_path)}", flush=True)

            # Save the image with the new EXIF data
            img.save(file_path, exif=exif_bytes)
            print(f"DEBUG UPEXIF: 4c. Image saved successfully with updated EXIF for {path.basename(file_path)}", flush=True)
            exif_success = True # Mark EXIF success
        except Exception as exif_err:
            print(f"DEBUG UPEXIF: WARNING - Failed to modify EXIF for {path.basename(file_path)}: {exif_err}", flush=True)
            # warning(f"Failed to modify EXIF for {file_path}: {exif_err}")
            exif_success = False # EXIF modification failed

        if exif_success:
             print(f'DEBUG UPEXIF: EXIF metadata update SUCCEEDED for {path.basename(file_path)}.', flush=True)
        else:
             print(f'DEBUG UPEXIF: EXIF metadata update FAILED for {path.basename(file_path)}.', flush=True)
             # If EXIF failed, the overall function result should be failure.

    except Exception as e:
        # This catches errors during image opening or other unexpected issues
        print(f"DEBUG UPEXIF: CRITICAL ERROR in update_exif for {path.basename(file_path)}: {type(e).__name__} - {e}", flush=True)
        # error(f"Outer error updating metadata for {file_path}: {e}") # Use configured logger
        exif_success = False # Ensure failure is marked

    print(f"DEBUG UPEXIF: END for {path.basename(file_path)}, final EXIF success status: {exif_success}", flush=True)
    return exif_success # Return status based on EXIF processing only
