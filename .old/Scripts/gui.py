import os
import threading
import json
import sys
import webview
from webview.dom import DOMEventHandler
from ai_keyworder import ai_keywording, update_exif
from utils import eta

# --- Global Variables ---
_api_instance = None
_window = None

# --- Pywebview API Class ---
class Api:
    def __init__(self):
        self.dropped_paths = [] # Store paths received from drop event
        self.is_files_drop = False # Flag if drop was file(s) or folder

    def _get_window(self):
        # Helper to safely get the window object
        global _window
        if _window:
            return _window
        elif webview.windows:
             _window = webview.windows[0]
             return _window
        print("Error: Window object not found.")
        return None

    def update_progress_js(self, message, color="black"):
        """ Sends progress updates to the Javascript frontend """
        window = self._get_window()
        if window:
            try:
                # Escape message for safe JS execution
                escaped_message = json.dumps(message)
                js_code = f"updateProgressLabel({escaped_message}, '{color}');"
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error evaluating JS for progress update: {e}")
        else:
            print(f"Progress (window not found): {message}")

    def update_progress_overlay(self, current, total, eta, filename=""):
        """ Updates the progress overlay with current processing status """
        window = self._get_window()
        if window:
            try:
                # Escape filename for safe JS execution
                escaped_filename = json.dumps(filename)
                js_code = f"updateProgressOverlay({current}, {total}, '{eta}', {escaped_filename});"
                window.evaluate_js(js_code)
            except Exception as e:
                print(f"Error updating progress overlay: {e}")
        else:
            print(f"Progress overlay update failed (window not found): {current}/{total}")

    def handle_drop(self, paths):
        """
        Handles file paths dropped onto the webview window.
        Called FROM the Python DOM drop event handler.
        Stores paths and triggers the settings display in HTML.
        """
        print(f"Python API: handle_drop received paths: {paths}")
        valid_extensions = ('.jpg', '.jpeg', '.png', '.raw') # Only image types for keywording
        found_files = []
        is_folder = False # Check if a single folder was dropped

        if not isinstance(paths, (list, tuple)):
             print(f"Warning: handle_drop received non-iterable paths: {paths}")
             paths = []

        # Check if it was a single directory drop
        if len(paths) == 1 and os.path.isdir(paths[0]):
            is_folder = True
            folder_path = paths[0]
            print(f"Python API: Processing dropped folder: {folder_path}")
            try:
                for f in os.listdir(folder_path):
                    full_path = os.path.join(folder_path, f)
                    if os.path.isfile(full_path) and full_path.lower().endswith(valid_extensions):
                        found_files.append(full_path)
            except Exception as e:
                print(f"Error reading directory {folder_path}: {e}")
                self.update_progress_js(f"Error reading folder: {os.path.basename(folder_path)}", "red")
                return # Stop processing if folder read fails

        else: # Process as individual files
            print(f"Python API: Processing dropped files...")
            for path in paths:
                if not isinstance(path, str):
                    print(f"Warning: Received non-string path item: {path}")
                    continue
                if os.path.isfile(path) and path.lower().endswith(valid_extensions):
                    print(f"Python API: Valid file found: {path}")
                    found_files.append(path)
                elif os.path.isdir(path):
                     print(f"Warning: Dropping multiple folders or mixed items is not recommended. Processing files within folders dropped is not implemented in file-drop mode. Dropped folder: {path}")
                     self.update_progress_js(f"Cannot process dropped folder '{os.path.basename(path)}' alongside files.", "orange")
                else:
                    print(f"Python API: Skipped invalid or non-image file: {path}")


        if not found_files:
            print("Python API: No valid image files found in drop.")
            if paths: # Dropped items existed but none were valid
                 self.update_progress_js('No valid image files (.jpg, .jpeg, .png, .raw) found in drop.', "orange")
            else: # No paths received initially (shouldn't happen with DOM event)
                 self.update_progress_js('Drag files or a folder here.', "black") # Reset prompt
            return

        # Store the valid paths and type of drop
        self.dropped_paths = found_files
        self.is_files_drop = not is_folder # True if files were dropped, False if a single folder was dropped
        total_files = len(self.dropped_paths)
        print(f"Python API: Stored {total_files} valid files. is_files_drop={self.is_files_drop}")

        # Update progress and trigger settings UI in Javascript
        self.update_progress_js(f"{total_files} image file(s) ready. Enter settings below.", "blue")
        window = self._get_window()
        if window:
            try:
                window.evaluate_js('showSettings()') # Call JS function to show input fields
                print("Python API: Called JS showSettings()")
            except Exception as e:
                print(f"Error calling JS showSettings: {e}")
                self.update_progress_js("Error preparing settings UI.", "red")
        else:
             self.update_progress_js("Error: Could not display settings inputs.", "red")


    def start_keywording_api(self, file_type, custom_prompt, is_editorial=False, place="", country="", time=""):
        """
        API function called by Javascript when the 'Start Keywording' button is clicked.
        Retrieves stored paths and starts the keywording thread.
        """
        print(f"Python API: start_keywording_api called with Type='{file_type}', Prompt='{custom_prompt}', Editorial={is_editorial}, Place='{place}', Country='{country}', Time='{time}'")
        if not self.dropped_paths:
            self.update_progress_js('⚠ No files were selected/processed from the drop! Try dropping again. ⚠', "red")
            return

        print(f"Python API: Starting keywording thread for {len(self.dropped_paths)} files.")
        # Clear stored paths after starting to prevent accidental reuse if UI isn't reset properly
        paths_to_process = list(self.dropped_paths)
        # self.dropped_paths = [] # Optional: clear immediately, or clear on success/failure

        # Run the keywording process in a separate thread
        # Pass the necessary arguments including the API instance for progress updates
        threading.Thread(
            target=keywording_thread_target, # Use the dedicated thread target function
            args=(self.is_files_drop, paths_to_process, file_type, custom_prompt, self, is_editorial, place, country, time), # Pass self (API instance) and editorial fields
            daemon=True
        ).start()

    def open_file_dialog(self):
        """
        Opens a file dialog and processes selected files similar to drag & drop.
        Called when the drop area is clicked.
        """
        print("Python API: Opening file dialog...")
        window = self._get_window()
        if not window:
            print("Error: Window not available for file dialog.")
            self.update_progress_js("Error opening file dialog.", "red")
            return

        try:
            # Define file types for the dialog
            file_types = ('Image Files (*.jpg;*.jpeg;*.png;*.raw)', 'All files (*.*)')

            # Open the file dialog
            result = window.create_file_dialog(
                webview.OPEN_DIALOG,
                allow_multiple=True,
                file_types=file_types
            )

            # Process the selected files
            if result:
                print(f"Python API: Files selected from dialog: {result}")
                self.handle_drop(result)  # Reuse the existing drop handler
            else:
                print("Python API: No files selected from dialog.")
                # No need to show a message as this is a normal cancellation

        except Exception as e:
            error_msg = f"Error opening file dialog: {e}"
            print(f"Python API Exception: {error_msg}")
            self.update_progress_js(error_msg, "red")

    def show_completion_indicator(self):
        """ Shows the completion indicator animation in the UI """
        print("Python API: Showing completion indicator")
        window = self._get_window()
        if window:
            try:
                window.evaluate_js('showCompletionIndicator()')
                print("Python API: Called JS showCompletionIndicator()")
            except Exception as e:
                print(f"Error calling JS showCompletionIndicator: {e}")
        else:
            print("Error: Could not show completion indicator (window not found).")

    def trigger_stock_uploader(self):
        """ API function called by Javascript for the 'Upload to Stocks' button. """
        print("Python API: Triggering Stock Uploader...")
        # Get the absolute path to the stock_uploader.py script
        import os
        import sys
        import subprocess
        import traceback

        try:
            # Get the path to the Python executable
            python_exe = sys.executable
            print(f"Using Python executable: {python_exe}")

            # Get the path to the stock_uploader.py script
            scripts_dir = os.path.dirname(os.path.abspath(__file__))
            stock_uploader_path = os.path.join(scripts_dir, "stock_uploader.py")
            print(f"Looking for stock_uploader.py at: {stock_uploader_path}")
            print(f"File exists: {os.path.exists(stock_uploader_path)}")

            if not os.path.exists(stock_uploader_path):
                # Try other locations
                project_root = os.path.abspath(os.path.join(scripts_dir, '..'))
                alt_path = os.path.join(project_root, "Scripts", "stock_uploader.py")
                if os.path.exists(alt_path):
                    stock_uploader_path = alt_path
                    print(f"Found stock_uploader.py at alternate location: {stock_uploader_path}")

            if not os.path.exists(stock_uploader_path):
                error_msg = "Could not find stock_uploader.py"
                print(f"Python API Error: {error_msg}")
                self.update_progress_js(error_msg, "red")
                return False

            # Set up environment to ensure Scripts directory is in Python's path
            env = os.environ.copy()
            python_path = env.get("PYTHONPATH", "")
            if scripts_dir not in python_path:
                env["PYTHONPATH"] = f"{scripts_dir}{os.pathsep}{os.path.abspath('..')}{os.pathsep}{python_path}"

            # Run the stock_uploader.py script directly
            command = [python_exe, stock_uploader_path]
            print(f"Launching command: {command}")

            process = subprocess.Popen(
                command,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                close_fds=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                env=env
            )

            print(f"Launched stock_uploader.py with PID {process.pid}")
            self.update_progress_js("Stock Uploader window opened.", "blue")
            # No completion indicator for stock uploader as per user request
            return True

        except Exception as e:
            error_msg = f"Error launching Stock Uploader: {e}"
            print(f"Python API Exception: {error_msg}")
            print(f"Traceback: {traceback.format_exc()}")
            self.update_progress_js(error_msg, "red")
            return False

# --- Backend Logic (Adapted for Webview) ---

def update_progress_threadsafe(api_instance, message, color="black"):
    """ Safely updates progress from the worker thread """
    print(f"DEBUG PROGRESS: {message} (Color: {color})", flush=True) # Also print to console log
    if api_instance:
        api_instance.update_progress_js(message, color)
    else:
        print(f"Progress (API instance missing): {message}", flush=True)

def update_progress_overlay_threadsafe(api_instance, current, total, eta, filename=""):
    """ Safely updates progress overlay from the worker thread """
    print(f"DEBUG PROGRESS OVERLAY: {current}/{total}, ETA: {eta}, File: {filename}", flush=True) # Also print to console log
    if api_instance:
        api_instance.update_progress_overlay(current, total, eta, filename)
    else:
        print(f"Progress overlay update failed (API instance missing): {current}/{total}", flush=True)


def keywording_thread_target(is_files, files_to_process, filetype, custom_prompt, api_instance, is_editorial=False, place="", country="", time=""):
    """
    The function that runs in the keywording thread.
    Uses the api_instance to report progress back to the UI.
    """
    # global errs, errs_files # These are handled within ai_keywording.py module now
    print("DEBUG THREAD: Keywording thread START.", flush=True)
    print(f"DEBUG THREAD: is_files={is_files}, num_files={len(files_to_process)}, filetype='{filetype}', custom_prompt='{custom_prompt}'", flush=True)
    print(f"DEBUG THREAD: is_editorial={is_editorial}, place='{place}', country='{country}', time='{time}'", flush=True)

    total = len(files_to_process)
    processed = 0

    # Reset errors specific to this run by accessing the module where they are defined
    try:
        original_errs_module = __import__('ai_keywording')
        original_errs_module.errs = 0
        original_errs_module.errs_files = []
        print(f"DEBUG THREAD: Errors reset in ai_keywording module.", flush=True)
    except ImportError:
        print("DEBUG THREAD: ERROR - Could not import ai_keywording module to reset errors.", flush=True)
        # Decide if this is fatal or if we continue assuming errors are handled locally
    except AttributeError:
        print("DEBUG THREAD: ERROR - errs/errs_files not found in ai_keywording module for reset.", flush=True)


    print(f"DEBUG THREAD: Starting process for {total} files.", flush=True)
    update_progress_threadsafe(api_instance, f'Processing: 0/{total}, ETA: estimating...', "blue")
    # Initialize the progress overlay
    update_progress_overlay_threadsafe(api_instance, 0, total, "estimating...", "Starting process")

    try:
        for i, file_path in enumerate(files_to_process):
            current_file_basename = os.path.basename(file_path)
            print(f"DEBUG THREAD: Loop {i+1}/{total}. Processing file: {current_file_basename}", flush=True)

            eta_str = eta(total, processed) if 'eta' in globals() and callable(eta) else '...'
            update_progress_threadsafe(api_instance, f'Processing: {processed}/{total}, ETA: {eta_str} ({current_file_basename})', "blue")
            # Update the progress overlay with current file
            update_progress_overlay_threadsafe(api_instance, processed, total, eta_str, current_file_basename)

            print(f"DEBUG THREAD: Calling ai_keywording for {current_file_basename}", flush=True)
            keywords, title = ai_keywording(file_path, custom_prompt, is_editorial, place, country, time)
            print(f"DEBUG THREAD: ai_keywording returned for {current_file_basename}. K: {'Yes' if keywords else 'No'}, T: {'Yes' if title else 'No'}", flush=True)

            if keywords and title:
                print(f"DEBUG THREAD: Calling update_exif for {current_file_basename}", flush=True)
                update_exif_success = update_exif(file_path, keywords, title) # Capture return
                print(f"DEBUG THREAD: update_exif returned for {current_file_basename}, success: {update_exif_success}", flush=True)
            else:
                print(f"DEBUG THREAD: Skipped update_exif for {current_file_basename} (no keywords/title or ai_keywording failed)", flush=True)
                # ai_keywording already logs errors and updates its global error counts

            processed += 1
            eta_str = eta(total, processed) if 'eta' in globals() and callable(eta) else '...'
            update_progress_threadsafe(api_instance, f'Processing: {processed}/{total}, ETA: {eta_str}', "blue")
            # Update the progress overlay after processing each file
            update_progress_overlay_threadsafe(api_instance, processed, total, eta_str)

        print("DEBUG THREAD: Processing loop finished.", flush=True)

        final_errs = 0
        final_err_files = []
        try:
            # Re-access the module to get final error counts
            err_module = __import__('ai_keywording')
            final_errs = err_module.errs
            final_err_files = list(err_module.errs_files) # Make a copy
            print(f"DEBUG THREAD: Final errors from ai_keywording module: Count={final_errs}, Files={final_err_files}", flush=True)
        except (ImportError, AttributeError) as e:
            print(f"DEBUG THREAD: ERROR - Could not retrieve final errors from ai_keywording: {e}", flush=True)


        if final_errs == 0:
            success_message = f"Finished: Successfully processed {total}/{total} files."
            update_progress_threadsafe(api_instance, success_message, "green")
            print(f"DEBUG THREAD: {success_message}", flush=True)
            if api_instance:
                api_instance.show_completion_indicator()
        else:
            warning_message = f"Finished: Processed {total} files with {final_errs} error(s)."
            update_progress_threadsafe(api_instance, warning_message, "orange")
            print(f"DEBUG THREAD: {warning_message}", flush=True)
            print(f"DEBUG THREAD: Errored files list: {final_err_files}", flush=True)
            if api_instance:
                api_instance.show_completion_indicator()

    except Exception as e:
        error_message = f"⚠ Unexpected error during keywording thread: {e}"
        print(f"DEBUG THREAD: CRITICAL ERROR in thread: {type(e).__name__} - {e}", flush=True)
        import traceback
        traceback.print_exc() # Print full traceback to the log file
        update_progress_threadsafe(api_instance, error_message, "red")
        # Make sure to hide the progress overlay on error
        if api_instance:
            try:
                window = api_instance._get_window()
                if window:
                    window.evaluate_js("hideProgressOverlay()")
            except Exception as hide_err:
                print(f"DEBUG THREAD: Error hiding progress overlay on error: {hide_err}", flush=True)

    finally:
        print("DEBUG THREAD: Keywording thread FINALLY block.", flush=True)
        window = api_instance._get_window() if api_instance else None
        if window:
             try:
                 print("DEBUG THREAD: Calling JS resetUIState()", flush=True)
                 window.evaluate_js("resetUIState()")
                 print("DEBUG THREAD: JS resetUIState() called.", flush=True)
             except Exception as e_js:
                 print(f"DEBUG THREAD: Error calling JS resetUIState: {e_js}", flush=True)
        if api_instance:
            api_instance.dropped_paths = []
            api_instance.is_files_drop = False
            print("DEBUG THREAD: Cleared stored paths in API instance.", flush=True)

    print("DEBUG THREAD: Keywording thread END.", flush=True)


# --- Helper function to get asset path ---
def get_asset_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # Not running as a PyInstaller bundle, use normal path
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# --- Pywebview DOM Event Handlers (from stock_uploader example) ---
def python_on_drop(event):
    """
    Handles the drop event captured via window.dom.document.events.
    Extracts file paths using 'pywebviewFullPath'.
    """
    global _api_instance
    print("Python DOM Event: Drop detected!")

    if not _api_instance:
        print("Error: API instance not available for drop event.")
        return

    try:
        files_data = event.get('dataTransfer', {}).get('files', [])
        if not files_data:
            print("Python DOM Event: No files found in event dataTransfer.")
            # Provide feedback even if no files technically transferred
            _api_instance.update_progress_js("Drop event occurred, but no file data received.", "orange")
            return

        dropped_paths = []
        print("Python DOM Event: Extracting file paths...")
        for file_info in files_data:
            full_path = file_info.get('pywebviewFullPath')
            if full_path and isinstance(full_path, str):
                print(f"  Found path: {full_path}")
                dropped_paths.append(full_path)
            else:
                print(f"  Warning: Could not get 'pywebviewFullPath' for file: {file_info.get('name', 'N/A')}")

        if dropped_paths:
            print(f"Python DOM Event: Calling API handle_drop with {len(dropped_paths)} path(s).")
            # Call the API method to handle the logic
            _api_instance.handle_drop(dropped_paths)
        else:
            print("Python DOM Event: No valid paths extracted.")
            _api_instance.update_progress_js('Could not extract file paths from drop.', 'red')

    except Exception as e:
        print(f"Error in python_on_drop event handler: {e}")
        if _api_instance:
             _api_instance.update_progress_js(f'Error processing drop event: {e}', 'red')


def python_on_click(event):
    """
    Handles the click event on the drop area.
    Opens a file dialog when the drop area is clicked.
    """
    global _api_instance
    print("Python DOM Event: Drop area clicked!")

    if not _api_instance:
        print("Error: API instance not available for click event.")
        return

    try:
        # Call the API method to open the file dialog
        _api_instance.open_file_dialog()
    except Exception as e:
        print(f"Error in python_on_click event handler: {e}")
        if _api_instance:
            _api_instance.update_progress_js(f'Error handling click event: {e}', 'red')


def bind_dnd_events(window):
    """
    Binds the Python drop and click handlers to the DOM element after the window loads.
    """
    print("Python: Attempting to bind DND events to DOM...")
    try:
        # Ensure the ID matches your HTML drop area
        drop_area_element = window.dom.get_element('#drop-area')

        if drop_area_element:
            # Bind the Python drop handler to the 'drop' event
            # prevent_default=True is crucial for drop to work correctly
            drop_area_element.events.drop += DOMEventHandler(python_on_drop, prevent_default=True, stop_propagation=True)
            print("Python: Successfully bound python_on_drop to #drop-area")

            # Bind the Python click handler to the 'click' event
            drop_area_element.events.click += DOMEventHandler(python_on_click, prevent_default=False, stop_propagation=False)
            print("Python: Successfully bound python_on_click to #drop-area")

            # Note: Visual dragover feedback (like changing background) is best handled
            # purely in Javascript/CSS for responsiveness. Python binding adds latency.
            # If needed:
            # def python_on_dragover(e): pass # Dummy function, JS handles visuals
            # drop_area_element.events.dragover += DOMEventHandler(python_on_dragover, prevent_default=True, stop_propagation=True)

        else:
            print("Python Error: Could not find DOM element #drop-area to bind events.")
            # Try to inform the user via the UI if possible
            global _api_instance
            if _api_instance:
                _api_instance.update_progress_js("Error: Could not initialize drop area.", "red")

    except AttributeError as e:
         print(f"Python Error: Failed to bind DOM events. DOM API might not be ready or supported. Error: {e}")
    except Exception as e:
         print(f"Python Error: An unexpected error occurred during DND event binding: {e}")


# --- Main Application Setup ---
def run_gui():
    global _api_instance, _window
    _api_instance = Api() # Create the API instance

    # Define paths for HTML and CSS relative to script location
    # Make sure these files exist!
    html_file = get_asset_path('web/key.html')
    icon_path = get_asset_path('data/logo.ico') # Get path for icon

    if not os.path.exists(html_file):
       print(f"ERROR: HTML file not found at {html_file}")
       # Attempt to provide feedback in a way user might see if console isn't visible
       # Fallback: basic alert if webview can even start partially
       try:
           webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Could not find required file: {os.path.basename(html_file)}</p></body></html>', width=400, height=200)
           webview.start()
       except Exception as start_err:
           print(f"Further error trying to display message window: {start_err}")
       sys.exit(1)

    # Check for icon existence (optional, window will still open without it)
    if not os.path.exists(icon_path):
        print(f"Warning: Icon file not found at {icon_path}. Window will use default icon.")
        icon_path = None # Set to None if not found


    _window = webview.create_window(
        'AI-Keyworder V2.0', # Window Title
        url=html_file,        # Path to the HTML file
        js_api=_api_instance, # Expose the Api class instance to Javascript
        width=680,            # Match original width
        height=640,           # Adjusted height for settings section
        resizable=False,      # Match original setting
        confirm_close=False,  # Match original setting (usually True is safer)
        background_color='#f0f0f0', # Background color (can also be set in CSS body)
        # text_select=True, # Allow text selection (optional)
    )

    print("Starting Pywebview...")
    webview.start(bind_dnd_events, _window, debug=False) # Use CEF for better compatibility if needed

if __name__ == "__main__":
    run_gui()

# --- END OF REFACTORED main.py ---
