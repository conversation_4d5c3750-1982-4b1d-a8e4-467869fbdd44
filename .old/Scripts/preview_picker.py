# START OF FILE preview_picker.py
import webview
import sys
import os
import cv2 # OpenCV for video processing
from PIL import Image # Pillow for image handling
import pyexiv2 # For metadata read/write
import base64
from io import BytesIO
import json
import logging
import threading
import time # For potential delays

# --- Global Variables ---
_window = None
_video_capture = None
_original_metadata = None
_image_path = None
_video_path = None
_frame_count = 0
_pyexiv2_lock = threading.Lock() # Essential for safe metadata writing

# Basic Logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Helper to get asset path (relative to this script) ---
def get_asset_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(os.path.dirname(__file__)) # Use script's directory
    # Look inside 'web' subdirectory for assets
    return os.path.join(base_path, 'web', relative_path)


# --- Pywebview API Class for Preview Picker ---
class PreviewApi:
    def __init__(self):
         # Paths are set globally when the app starts
         pass

    def _get_window(self):
        global _window
        if _window:
            return _window
        elif webview.windows:
             _window = webview.windows[0]
             return _window
        logging.error("Window object not found.")
        return None

    def _call_js(self, function_name, *args):
        """Helper to safely call JS functions."""
        window = self._get_window()
        if not window:
            logging.error(f"Cannot call JS {function_name}, window not found.")
            return
        try:
            # Properly format arguments for JS call
            js_args = ', '.join(json.dumps(arg) for arg in args)
            js_code = f"{function_name}({js_args});"
            logging.debug(f"Evaluating JS: {js_code[:100]}...") # Log snippet
            window.evaluate_js(js_code)
        except Exception as e:
            logging.error(f"Error evaluating JS for {function_name}: {e}", exc_info=True)

    # **MODIFIED**: This function now only starts the background thread
    def initialize_video_api(self):
        """
        Called by JS on pywebviewready. Starts a background thread
        to get video info without blocking the main thread.
        """
        logging.info("API: initialize_video_api called by JS.")
        # Start the actual initialization in a background thread
        init_thread = threading.Thread(target=self._initialize_video_thread, name="VidInitThread", daemon=True)
        init_thread.start()
        logging.info("API: Video initialization thread started.")

    def _initialize_video_thread(self):
        """Background thread to open video, get frame count, and read metadata."""
        global _video_capture, _frame_count, _image_path, _video_path, _original_metadata
        logging.info("THREAD: Starting video initialization...")

        try:
            # --- 1. Load Original Metadata FIRST (Still potentially slow) ---
            logging.info("THREAD: Loading original metadata...")
            _original_metadata = load_original_metadata(_image_path) # Function defined below
            if _original_metadata is None:
                # If metadata fails, we can still proceed but warn the user later during save.
                # For now, let's proceed with video init.
                logging.warning(f"THREAD: Could not load original metadata from {_image_path}. Preview saving might lose metadata.")
                # Optionally inform JS immediately, or just handle during save attempt.
                # self._call_js('updateStatus', 'Warning: Could not read original metadata.', 'warning')

            # --- 2. Initialize Video Capture ---
            logging.info(f"THREAD: Initializing video capture for: {_video_path}")
            # Make sure _video_capture is assigned globally within the thread
            _video_capture = cv2.VideoCapture(_video_path)
            if not _video_capture or not _video_capture.isOpened():
                error_msg = f"Failed to open video file: {os.path.basename(_video_path)}"
                logging.error(f"THREAD: {error_msg}")
                self._call_js('setVideoInfo', {'error': error_msg})
                if _video_capture: _video_capture.release() # Release if object exists but failed to open
                _video_capture = None # Ensure it's None on failure
                return # Stop thread execution

            logging.info("THREAD: Video capture object created. Getting frame count...")

            # --- 3. Get Frame Count (Potential Blocking Point) ---
            start_time = time.time()
            _frame_count = int(_video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = time.time() - start_time
            logging.info(f"THREAD: Got frame count: {_frame_count} (took {duration:.2f}s)")

            if _frame_count <= 0:
                error_msg = f"Video has zero or invalid frame count ({_frame_count})."
                logging.error(f"THREAD: {error_msg}")
                self._call_js('setVideoInfo', {'error': error_msg})
                _video_capture.release()
                _video_capture = None
                return

            # --- 4. Success - Send info back to JS ---
            video_info = {
                'frameCount': _frame_count,
                'imageFilename': os.path.basename(_image_path) if _image_path else 'N/A',
                'videoFilename': os.path.basename(_video_path) if _video_path else 'N/A'
            }
            logging.info(f"THREAD: Video initialization successful. Sending info to JS: {video_info}")
            self._call_js('setVideoInfo', video_info)

        except Exception as e:
            logging.error(f"THREAD: Error during video initialization: {e}", exc_info=True)
            # Send a generic error back to JS
            self._call_js('setVideoInfo', {'error': f'Error initializing video: {e}'})
            # Ensure video capture is released on error
            if _video_capture and _video_capture.isOpened():
                _video_capture.release()
            _video_capture = None

    def get_frame_api(self, frame_number):
        """Called by JS when slider moves. Reads frame and sends base64 data."""
        global _video_capture
        if not _video_capture or not _video_capture.isOpened() or frame_number < 0:
            logging.warning(f"Get frame called with invalid state or frame number: {frame_number}")
            self._call_js('displayFrame', None) # Send null on error
            return

        # Run frame extraction in a thread to avoid blocking UI thread if slow
        threading.Thread(target=self._get_frame_thread, args=(frame_number,), daemon=True).start()

    def _get_frame_thread(self, frame_number):
        """Background thread to extract and encode a single frame."""
        global _video_capture
        base64_data = None
        try:
             # Ensure frame number is within bounds
             if frame_number >= _frame_count:
                  logging.warning(f"Requested frame {frame_number} exceeds frame count {_frame_count}. Clamping.")
                  frame_number = _frame_count - 1

             # Set the frame position
             # Note: CAP_PROP_POS_FRAMES setting can be slow or inaccurate on some videos/codecs.
             # If performance is bad, consider reading sequentially if slider jumps aren't too large.
             _video_capture.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
             ret, frame = _video_capture.read() # Read the frame

             if ret and frame is not None:
                 # Convert from BGR (OpenCV default) to RGB (Pillow default)
                 frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                 pil_img = Image.fromarray(frame_rgb)

                 # Save to buffer as JPEG
                 buffered = BytesIO()
                 pil_img.save(buffered, format="JPEG", quality=90) # Adjust quality as needed
                 img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')
                 base64_data = f"data:image/jpeg;base64,{img_str}"
             else:
                 logging.error(f"Failed to read frame {frame_number}. ret={ret}")

        except Exception as e:
             logging.error(f"Error processing frame {frame_number}: {e}", exc_info=True)
        finally:
             # Send result (data or None) back to JS on the main thread
             self._call_js('displayFrame', base64_data)


    def save_frame_api(self, frame_number):
        """Called by JS to save the selected frame, preserving metadata."""
        global _image_path, _video_capture, _original_metadata, _pyexiv2_lock
        if not all([_image_path, _video_capture, _original_metadata is not None, frame_number >= 0]):
            logging.error("Save called with invalid state or missing data.")
            self._call_js('handleSaveResult', {'success': False, 'error': 'Internal state error.'})
            return

        # Run save in background thread
        threading.Thread(target=self._save_frame_thread, args=(frame_number,), daemon=True).start()

    def _save_frame_thread(self, frame_number):
        """Background thread to save frame and apply original metadata."""
        global _image_path, _video_capture, _original_metadata, _pyexiv2_lock
        success = False
        error_message = None
        saved_filename = os.path.basename(_image_path)

        try:
            # --- 1. Get the selected frame data ---
            _video_capture.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = _video_capture.read()

            if not ret or frame is None:
                raise ValueError(f"Failed to retrieve frame {frame_number} for saving.")

            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(frame_rgb)

            # --- 2. Save the new image data (overwrite original image path) ---
            logging.info(f"Attempting to save frame {frame_number} to {_image_path}")
            # Use JPEG format, consistent with how previews were likely generated
            # Ensure directory exists (should always be true here, but good practice)
            os.makedirs(os.path.dirname(_image_path), exist_ok=True)
            pil_img.save(_image_path, format="JPEG", quality=95) # High quality save
            logging.info(f"Successfully saved new image data to {_image_path}")

            # --- 3. Apply original metadata using pyexiv2 (CRITICAL STEP) ---
            logging.info(f"Attempting to apply original metadata to {_image_path}")
            with _pyexiv2_lock: # Ensure exclusive access for writing
                 logging.debug(f"Acquired pyexiv2 lock for writing metadata to {saved_filename}")
                 img_modify = None # Define outside try
                 try:
                     img_modify = pyexiv2.Image(_image_path)
                     # Clear existing metadata first (optional, but safer)
                     # img_modify.clear_exif()
                     # img_modify.clear_iptc()
                     # img_modify.clear_xmp()
                     # Apply stored metadata
                     if _original_metadata.get('exif'):
                         img_modify.modify_exif(_original_metadata['exif'])
                     if _original_metadata.get('iptc'):
                         img_modify.modify_iptc(_original_metadata['iptc'])
                     if _original_metadata.get('xmp'):
                         img_modify.modify_xmp(_original_metadata['xmp'])

                     # No explicit save needed, happens on close/destruction
                     success = True
                     logging.info(f"Successfully applied metadata to {saved_filename}")

                 except FileNotFoundError:
                     error_message = f"File disappeared after saving image data: {saved_filename}"
                     logging.error(error_message)
                     success = False # Can't apply metadata if file is gone
                 except Exception as meta_err:
                     error_message = f"Failed to apply metadata: {meta_err}"
                     logging.error(f"{error_message} for {saved_filename}", exc_info=True)
                     # Image data is saved, but metadata failed. This is a partial success/failure.
                     success = False # Mark as failure if metadata step fails
                 finally:
                    if img_modify:
                        try:
                            img_modify.close() # Ensures changes are written & file handle released
                        except Exception as close_err:
                            logging.warning(f"Error closing pyexiv2 image object after modify: {close_err}")
                    logging.debug(f"Released pyexiv2 lock for {saved_filename}")
             # --- LOCK RELEASED ---

        except Exception as e:
            error_message = f"General error during save process: {e}"
            logging.error(f"{error_message} for {saved_filename}", exc_info=True)
            success = False
        finally:
            # Send result back to JS
            self._call_js('handleSaveResult', {'success': success, 'error': error_message, 'filename': saved_filename if success else None})

    def close_window(self):
        """ API method to request window close if needed """
        window = self._get_window()
        if window:
            window.destroy()

# --- Main Application Setup ---
def load_original_metadata(image_path_local):
    """Reads EXIF, IPTC, XMP from the original image. Returns metadata dict or None."""
    global _pyexiv2_lock
    metadata = {'exif': None, 'iptc': None, 'xmp': None}
    if not os.path.exists(image_path_local):
        logging.error(f"Metadata Load: Original image file not found: {image_path_local}")
        return None

    logging.info(f"Metadata Load: Attempting to read metadata from {os.path.basename(image_path_local)}")
    img_read = None
    start_time = time.time()
    with _pyexiv2_lock: # Lock for reading (might be overly cautious but safe)
         logging.debug(f"Metadata Load: Acquiring lock for {os.path.basename(image_path_local)}")
         try:
             img_read = pyexiv2.Image(image_path_local)
             metadata['exif'] = img_read.read_exif()
             metadata['iptc'] = img_read.read_iptc()
             metadata['xmp'] = img_read.read_xmp()
             duration = time.time() - start_time
             logging.info(f"Metadata Load: Successfully read metadata from {os.path.basename(image_path_local)} (took {duration:.2f}s)")
         except Exception as e:
             duration = time.time() - start_time
             logging.error(f"Metadata Load: Failed after {duration:.2f}s for {image_path_local}: {e}", exc_info=True)
             return None # Indicate failure
         finally:
            if img_read:
                try: img_read.close()
                except Exception as close_err: logging.warning(f"Metadata Load: Error closing pyexiv2 reader: {close_err}")
            logging.debug(f"Metadata Load: Released lock for {os.path.basename(image_path_local)}")
    return metadata


def main():
    global _window, _image_path, _video_path # Removed _video_capture, _original_metadata assignment here

    logging.info("Preview Picker starting...")
    # --- Argument Parsing --- (Keep as is)
    if len(sys.argv) < 3: # ... error handling ...
        sys.exit(1)
    _image_path = sys.argv[1]
    _video_path = sys.argv[2]
    logging.info(f"Image Path: {_image_path}")
    logging.info(f"Video Path: {_video_path}")
    if not os.path.exists(_image_path): # ... error handling ...
        sys.exit(1)
    if not os.path.exists(_video_path): # ... error handling ...
        sys.exit(1)

    _image_path = sys.argv[1]
    _video_path = sys.argv[2]

    if not os.path.exists(_image_path):
         print(f"Error: Image file not found: {_image_path}")
         # Show error window
         try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Image file not found:<br>{_image_path}</p></body></html>', width=400, height=150); webview.start()
         except Exception as e: logging.error(f"Failed to show error window: {e}")
         sys.exit(1)
    if not os.path.exists(_video_path):
         print(f"Error: Video file not found: {_video_path}")
         # Show error window
         try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Video file not found:<br>{_video_path}</p></body></html>', width=400, height=150); webview.start()
         except Exception as e: logging.error(f"Failed to show error window: {e}")
         sys.exit(1)

    # --- Load Original Metadata EARLY ---
    _original_metadata = load_original_metadata(_image_path)
    if _original_metadata is None:
         print(f"Error: Could not load original metadata from {_image_path}. Cannot proceed safely.")
         # Show error window
         try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Failed to read metadata from:<br>{os.path.basename(_image_path)}</p><p>Cannot guarantee metadata preservation.</p></body></html>', width=450, height=200); webview.start()
         except Exception as e: logging.error(f"Failed to show error window: {e}")
         sys.exit(1) # Exit if metadata can't be preserved

    # --- Initialize Video Capture ---
    _video_capture = cv2.VideoCapture(_video_path)
    if not _video_capture.isOpened():
        print(f"Error: Could not open video file: {_video_path}")
        # Show error window
        try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Could not open video file:<br>{_video_path}</p></body></html>', width=400, height=150); webview.start()
        except Exception as e: logging.error(f"Failed to show error window: {e}")
        sys.exit(1)

    # --- Create Pywebview Window ---
    api_instance = PreviewApi()
    html_file = get_asset_path('preview_picker.html')
    icon_path = get_asset_path('../logo.ico') # Assumes logo.ico is one level up from web/

    if not os.path.exists(html_file):
         print(f"ERROR: HTML file not found at {html_file}")
         # Fallback error display
         try: webview.create_window('Error', html=f'<html><body><h1>Error</h1><p>Missing UI file: preview_picker.html</p></body></html>', width=400, height=150); webview.start()
         except: pass
         sys.exit(1)

    if not os.path.exists(icon_path):
         logging.warning(f"Icon file not found at {icon_path}. Using default.")
         icon_path = None # pywebview will use default

    _window = webview.create_window(
        'Preview Picker v1.0',
        url=html_file,
        js_api=api_instance,
        width=700, # Adjust size as needed
        height=650,
        resizable=True,
        confirm_close=False, # Close without asking
    )

    # --- Start Event Loop ---
    try:
        webview.start(debug=False) # Set debug=True for development console
    finally:
        # --- Cleanup ---
        logging.info("Preview Picker closing. Releasing video capture.")
        if _video_capture:
            _video_capture.release()
        logging.info("Video capture released.")

    quit()


if __name__ == "__main__":
    main()

# END OF FILE preview_picker.py