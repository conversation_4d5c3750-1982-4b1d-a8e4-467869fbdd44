/* START OF FILE web/gallery.css */
/* Add gallery-specific styles here, complementing key.css */

.gallery-grid {
    display: grid;
    /* Auto-fill columns with a minimum width, creating a responsive grid */
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px; /* Space between grid items */
    padding: 10px; /* Padding inside the scrollable area */
    align-content: start; /* Align items to the top */
}

.gallery-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid transparent; /* For hover/selection border */
    border-radius: 6px;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.3); /* Slight background */
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    position: relative; /* For potential absolute positioning inside */
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    height: 180px; /* Fixed height for consistency */
    overflow: hidden; /* Prevent content spillover */
}
.gallery-item .progress-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: #ddd; /* Background for unfilled progress */
}

.gallery-item .progress-bar {
    height: 100%;
    width: 0%; /* Will be set dynamically */
    transition: width 0.3s ease;
}
.gallery-item:hover {
    background-color: rgba(255, 255, 255, 0.6);
    border-color: #FF6600; /* Highlight color on hover */
}

.gallery-item.selected {
    background-color: rgba(255, 102, 0, 0.2); /* Selection color */
    border-color: #FF6600;
    box-shadow: 0 2px 6px rgba(255, 102, 0, 0.3);
}

.gallery-item img {
    display: block;
    max-width: 100%; /* Ensure image fits width */
    height: 140px; /* Fixed height for thumbnail area */
    object-fit: contain; /* Scale image while maintaining aspect ratio */
    margin-bottom: 5px;
    border-radius: 4px; /* Slightly rounded corners for image */
    background-color: #ddd; /* Placeholder bg while loading */
}
.gallery-item .placeholder-icon {
    width: 100%;
    height: 140px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40px; /* Size of the icon */
    color: #aaa;
    background-color: #eee;
    border-radius: 4px;
    margin-bottom: 5px;
}
.gallery-item .placeholder-icon::before {
    content: '🖼️'; /* Placeholder emoji or use an SVG/FontAwesome */
}


.gallery-item p {
    font-size: 11px;
    color: #333;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* Add ... for long filenames */
    margin: 0;
    padding: 0 2px; /* Slight padding */
    line-height: 1.3;
}

/* Style for the placeholder message when no images are loaded */
#gallery-placeholder {
    grid-column: 1 / -1; /* Span all columns */
    text-align: center;
    color: #777;
    margin-top: 50px;
    font-size: 16px;
}

/* Scrollbar styling (optional, browser-specific) */
#gallery-grid::-webkit-scrollbar {
    width: 8px;
}

#gallery-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

#gallery-grid::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

#gallery-grid::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4);
}


.info-icon {
    position: absolute;
    top: 4px;             /* Adjust position */
    right: 4px;            /* Adjust position */
    font-size: 14px;       /* Size of the 'ℹ️' icon */
    cursor: help;
    z-index: 2;            /* Ensure it's above the image */
    padding: 2px;
    background-color: rgba(166, 237, 255, 0); /* Slight background for visibility */
    border-radius: 50%;    /* Make it round */
    width: 16px;           /* Fixed width for circle */
    height: 16px;          /* Fixed height for circle */
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;        /* Center emoji vertically */
    transition: background-color 0.2s ease;
}

.tooltip-text {
    visibility: hidden;     /* Hidden by default */
    width: 250px;          /* Adjust width as needed */
    background-color: #333; /* Dark background */
    color: #fff;            /* White text */
    text-align: left;
    border-radius: 6px;
    padding: 8px 10px;
    position: absolute;
    z-index: 3;            /* Above the icon and image */
    /* --- MODIFIED POSITION --- */
    top: 115%;           /* Position below the icon (adjust % as needed) */
    left: 50%;
    margin-left: -125px;   /* Use half of the width to center */
    /* --- END MODIFICATION --- */
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0s linear 0.3s; /* Fade effect, delay visibility change */
    font-size: 12px;       /* Tooltip font size */
    white-space: pre-wrap; /* !!! IMPORTANT: Respects newlines in the text !!! */
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    pointer-events: none;  /* Tooltip doesn't block hover on things below */
}

/* Show the tooltip on hover */
.info-icon:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.3s ease; /* Smooth fade-in */
}

/* Optional: Add a small arrow above the tooltip */
.tooltip-text::after {
    content: "";
    position: absolute;
    /* --- MODIFIED ARROW POSITION & DIRECTION --- */
    bottom: 100%;          /* At the top of the tooltip */
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #333 transparent; /* Arrow pointing up */
    /* --- END MODIFICATION --- */
}