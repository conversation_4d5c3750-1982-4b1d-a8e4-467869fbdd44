/* START OF FILE key.css (Modified for closer spacing) */

/* Basic Reset & Body */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    /* Subtle gradient background for depth - Copied from uploader.css */
    background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
    color: #333; /* Slightly softer black for text */
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 15px; /* Slightly reduced body padding */
    /* Consider if overflow: hidden is needed */
    /* overflow: hidden; */
}

.container {
    background-color: transparent; /* Container is just for layout - Copied from uploader.css */
    padding: 20px 20px 20px 20px; /* Reduced top/bottom padding */
    border-radius: 0;
    width: 100%;
    max-width: 640px; /* Consistent max-width */
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Title */
.title {
    font-size: 30px; /* Slightly smaller title */
    font-weight: 500; /* Rubik Medium */
    color: #222; /* Darker text for title */
    text-align: center;
    margin-bottom: 15px; /* Reduced spacing */
}

/* Progress Label */
.progress-label {
    font-size: 14px; /* Smaller, cleaner - From uploader.css */
    font-weight: 500; /* Medium weight */
    text-align: center;
    margin-bottom: 12px; /* Reduced spacing */
    min-height: 18px; /* Adjusted min-height */
    color: #555; /* Default softer color */
    transition: color 0.3s ease;
    width: 100%; /* Take full width */
    padding: 0 10px; /* Add padding */
    word-wrap: break-word; /* Wrap long messages */
}

/* Progress Label Status Colors (Keep specific feedback colors) */
.progress-label.error { color: #D32F2F; /* Red */ font-weight: 600; }
.progress-label.success { color: #388E3C; /* Green */ font-weight: 600; }
.progress-label.warning { color: #F57C00; /* Orange */ font-weight: 600; }
.progress-label.info { color: #1976D2; /* Blue */ font-weight: 600; }


/* Drop Area - Glassmorphism - Copied/Adapted from uploader.css */
.drop-area {
    width: calc(100% - 40px); /* Consistent calculation */
    max-width: 520px; /* Consistent max-width */
    height: 110px; /* Slightly reduced height */
    /* --- Glassmorphism Effect --- */
    background: rgba(255, 255, 255, 0.3); /* Semi-transparent white */
    backdrop-filter: blur(10px); /* The blur effect */
    -webkit-backdrop-filter: blur(10px); /* Safari prefix */
    border: 1px solid rgba(255, 255, 255, 0.4); /* Subtle white border */
    /* --- Styling & Layout --- */
    border-radius: 12px; /* Slightly smaller radius */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.04); /* Adjusted shadow */
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 5px auto 18px auto; /* Reduced top/bottom margins */
    cursor: pointer;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    padding: 10px; /* Slightly reduced padding */
    position: relative;
}

.drop-area p#drop-label { /* Target the specific p tag if needed */
    font-size: 17px; /* Slightly smaller font */
    font-weight: 500; /* Medium weight */
    color: #333; /* Ensure contrast */
    pointer-events: none; /* Allow drops on text */
}

/* Style when dragging over - Adapted from uploader.css */
.drop-area.drag-over {
    background: rgba(255, 255, 255, 0.45); /* Slightly more opaque */
    border-color: rgba(255, 255, 255, 0.6); /* More visible border */
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07); /* Slightly stronger shadow */
}

/* Style when disabled (e.g., while settings are shown) */
.drop-area.disabled {
    cursor: not-allowed;
    background: rgba(230, 230, 230, 0.2); /* Dimmer background */
    backdrop-filter: blur(2px); /* Less blur when disabled */
     -webkit-backdrop-filter: blur(2px);
    border-color: rgba(200, 200, 200, 0.3);
    opacity: 0.6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); /* Reduced shadow */
}
.drop-area.disabled p#drop-label {
    color: #888;
}


/* Settings Section - Styled consistently */
.settings-section {
    width: 480px; /* Match drop area width */
    max-width: 520px; /* Match drop area max-width */
    margin: 0 auto 18px auto; /* Reduced bottom margin */
    padding: 15px; /* Reduced padding */
    background-color: rgba(255, 255, 255, 0.15); /* Subtle background */
    border: 1px solid rgba(255, 255, 255, 0.25); /* Subtle border */
    border-radius: 12px; /* Match drop area radius */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); /* Very subtle shadow */
    display: flex;
    flex-direction: column;
    gap: 10px; /* Reduced space between elements inside */
    transition: opacity 0.3s ease, max-height 0.3s ease, margin 0.3s ease, padding 0.3s ease, border 0.3s ease; /* Smooth transition */
    opacity: 1;
    max-height: 500px; /* Allow space to expand */
    overflow: hidden; /* Hide content during transition */
}

.settings-section.hidden {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0; /* No margin when hidden */
    border: none;
    box-shadow: none;
}

.settings-input-group {
    display: flex;
    flex-direction: column;
    gap: 4px; /* Reduced gap */
}

.settings-label {
    font-size: 13px; /* Slightly smaller */
    font-weight: 500;
    color: #444;
    padding-left: 2px; /* Slight indent */
}

.settings-input {
    width: 100%;
    padding: 8px 10px; /* Reduced padding */
    font-size: 14px; /* Slightly smaller */
    border: 1px solid rgba(0, 0, 0, 0.1); /* Softer border */
    border-radius: 6px; /* Slightly smaller radius */
    background-color: rgba(255, 255, 255, 0.5); /* Semi-transparent input background */
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
    color: #333;
}
.settings-input::placeholder {
    color: #777;
}

.settings-input:focus {
    outline: none;
    border-color: #FF6600; /* Highlight focus with theme color */
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.15);
}

/* General Button Styles - Copied from uploader.css */
.btn {
    /* --- Size & Layout --- */
    width: 100%; /* Default to full width within container */
    max-width: 450px; /* Consistent max width */
    height: 50px; /* Reduced height */
    margin: 0 auto; /* Center buttons by default */
    display: flex;
    justify-content: center;
    align-items: center;
    /* --- Font & Text --- */
    font-size: 17px; /* Slightly smaller */
    font-weight: 600; /* Bolder */
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); /* Subtle text shadow */
    /* --- Appearance --- */
    background: linear-gradient(145deg, #ff7e29, #ff6600); /* Subtle gradient */
    border: none;
    border-radius: 8px; /* Reduced radius */
    cursor: pointer;
    /* --- Effects --- */
    box-shadow: 0 3px 10px rgba(255, 102, 0, 0.2), 0 1px 2px rgba(0,0,0,0.08); /* Adjusted shadow */
    transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Smooth, bouncy transition */
}

.btn:hover {
    background: linear-gradient(145deg, #ff8840, #e65c00); /* Adjusted gradient */
    box-shadow: 0 5px 14px rgba(255, 102, 0, 0.25), 0 2px 4px rgba(0,0,0,0.12); /* Adjusted shadow */
    transform: translateY(-2px); /* Lift effect */
}

.btn:active {
    transform: translateY(0px); /* Press down effect */
    box-shadow: 0 2px 6px rgba(255, 102, 0, 0.18), 0 1px 2px rgba(0,0,0,0.08); /* Adjusted shadow */
    background: linear-gradient(145deg, #e65c00, #ff7e29); /* Invert gradient slightly */
}

/* Specific button adjustments */
.settings-btn {
     /* Button takes full width *within* the settings section */
     max-width: none; /* Override general max-width for this specific button */
     margin-top: 8px; /* Reduced space above start button */
     height: 45px; /* Slightly shorter button inside settings */
     font-size: 16px;
}

.upload-btn {
    /* Spacing is handled by settings section margin when visible */
     margin-top: 8px; /* Reduced top margin when settings are hidden */
}

.file-info-section {
    width: 480px;
    max-width: 520px;
    margin: 0 auto 10px auto; /* Space it out */
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
    display: flex; /* For aligning count and clear button */
    justify-content: space-between;
    align-items: center;
}
.file-info-section.hidden {
    display: none;
}
#file-count-label {
    font-size: 14px;
    color: #333;
}
.btn-small { /* Basic style for a smaller secondary button */
    padding: 6px 12px;
    font-size: 13px;
    height: auto;
    max-width: 120px; /* Adjust as needed */
}
.btn-secondary {
    background: linear-gradient(145deg, #777, #555); /* Greyish */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 1px 1px rgba(0,0,0,0.06);
}
.btn-secondary:hover {
    background: linear-gradient(145deg, #888, #666);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0,0,0,0.08);
}

/* Spinner for buttons */
.spinner {
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5em;
    vertical-align: middle;
}
@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Progress Overlay */
.progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's on top of everything */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.progress-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.progress-container {
    width: 80%;
    max-width: 500px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-bar-container {
    width: 100%;
    height: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 15px;
    align-self: stretch;
}

.progress-bar {
    height: 100%;
    width: 0%; /* Will be set dynamically */
    background: linear-gradient(145deg, #ff7e29, #ff6600); /* Match button gradient */
    border-radius: 5px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    text-align: center;
    width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.progress-filename {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
    text-align: center;
    width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto;
    word-break: break-word; /* Handle long filenames */
}

/* Completion Indicator Overlay */
.completion-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's on top of everything */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.completion-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.completion-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #ff7e29, #ff6600); /* Match button gradient */
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.3);
    transform: scale(0.5);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy effect */
}

.completion-overlay.visible .completion-circle {
    transform: scale(1);
}

.checkmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    stroke: white;
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    animation: draw-checkmark 0.6s ease-in-out forwards 0.2s;
}

@keyframes draw-checkmark {
    from {
        stroke-dashoffset: 100;
    }
    to {
        stroke-dashoffset: 0;
    }
}

/* Subtle bounce animation for the circle */
@keyframes bounce {
    0%, 100% {transform: translateY(0);}
    50% {transform: translateY(-5px);}
}

.completion-overlay.visible .completion-circle {
    animation: bounce 1.2s cubic-bezier(0.445, 0.05, 0.55, 0.95) 0.4s;
}

/* Error Indicator Overlay */
.error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9); /* Semi-transparent white background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's on top of everything */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.error-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.error-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #ff3333, #cc0000); /* Red gradient for error */
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(204, 0, 0, 0.3);
    transform: scale(0.5);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy effect */
}

.error-overlay.visible .error-circle {
    transform: scale(1);
}

.crossmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    stroke: white;
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    animation: draw-crossmark 0.6s ease-in-out forwards 0.2s;
}

@keyframes draw-crossmark {
    from {
        stroke-dashoffset: 100;
    }
    to {
        stroke-dashoffset: 0;
    }
}

.error-overlay.visible .error-circle {
    animation: bounce 1.2s cubic-bezier(0.445, 0.05, 0.55, 0.95) 0.4s;
}
/* Editorial Checkbox and Fields Styles */
.editorial-container {
    position: relative;
    overflow: hidden;
}

.editorial-checkbox-container {
    display: flex;
    align-items: center;
    /* Removed transition for checkbox container */
}

/* Custom checkbox with orange background and white checkmark */
.editorial-checkbox {
    margin-right: 8px;
    cursor: pointer;
    width: 18px;
    height: 18px;
    /* Hide the default checkbox appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* Create custom checkbox */
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 3px;
    outline: none;
    transition: background-color 0.2s ease;
    position: relative;
}

/* Style for checked state */
.editorial-checkbox:checked {
    background-color: #FF6600; /* Orange background */
    border-color: #FF6600;
}

/* Create the checkmark using pseudo-element */
.editorial-checkbox:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white; /* White checkmark */
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.editorial-label {
    cursor: pointer;
    font-weight: 500;
    color: #444;
    padding-left: 0;
}

.editorial-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    opacity: 0;
    max-height: 0;
    transform: translateY(-20px); /* Start from above */
    overflow: hidden;
    transition: opacity 0.4s ease, max-height 0.4s ease, transform 0.4s ease;
}

.editorial-fields.fade-in {
    opacity: 1;
    max-height: 300px; /* Adjust as needed */
    transform: translateY(0); /* Slide down to normal position */
}

.editorial-fields.fade-out {
    opacity: 0;
    transform: translateY(-20px); /* Slide back up when hiding */
}

.editorial-fields.hidden {
    display: none;
}

.editorial-field {
    flex: 1;
    min-width: 120px;
}

/* Removed shift-left class as we're not animating the checkbox position */

/* END OF FILE key.css */