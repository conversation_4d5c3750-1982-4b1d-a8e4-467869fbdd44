import os
import csv
import logging
import paramiko
import pyexiv2
import tempfile
from Scripts.utils import mark_as_done, check_for_matching_video
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Metadata Extraction ---
def extract_metadata(file_path):
    """
    Extracts Title and Keywords from image metadata (EXIF/IPTC/XMP) using a
    robust method, adapted for CSV generation.

    If the file is a video or has a matching video file, the metadata is extracted from
    the image but the video filename is used in the CSV.

    Args:
        file_path (str): Absolute path to the image file.

    Returns:
        tuple: (filename, title, keywords_string) or None if extraction fails.
               Keywords are returned as a comma-separated string.
               Returns empty strings for title/keywords if tags are not found.
    """
    # Check if this is an image with a matching video file
    has_matching_video, video_path = check_for_matching_video(file_path)

    # Determine which filename to use in the CSV
    if has_matching_video and video_path:
        # Use the video filename for the CSV
        filename_for_csv = os.path.basename(video_path)
        # But extract metadata from the image file
        metadata_source_path = file_path
        logging.info(f"Using video filename {filename_for_csv} but extracting metadata from {os.path.basename(file_path)}")
    else:
        # Regular file - use its own filename and extract metadata from it
        filename_for_csv = os.path.basename(file_path)
        metadata_source_path = file_path

    image_exif = None
    title = ''
    keywords = [] # Store final keywords list here

    # Check if the metadata source file exists before attempting to open
    if not os.path.isfile(metadata_source_path):
         logging.error(f"Metadata extraction skipped: File not found or not a file at {metadata_source_path}")
         return None

    try:
        # --- Read Metadata using pyexiv2 ---
        # No lock needed here as it's not explicitly multi-threaded in this context
        logging.debug(f"Attempting to read metadata for {os.path.basename(metadata_source_path)}")
        image_exif = pyexiv2.Image(metadata_source_path)
        # Read all types - note read_all() might be less performant than specific reads
        # Using specific reads as per the example provided:
        raw_exif = image_exif.read_exif()
        raw_iptc = image_exif.read_iptc()
        raw_xmp = image_exif.read_xmp()
        logging.debug(f"Raw metadata read for {os.path.basename(metadata_source_path)}")

        # --- Extract Title (Priority: EXIF XPTitle > IPTC ObjectName > XMP dc:title) ---
        # Extract XMP title safely:
        xmp_title = ''
        if 'Xmp.dc.title' in raw_xmp:
            value = raw_xmp['Xmp.dc.title']
            if isinstance(value, list) and value:
                xmp_title = value[0]
            elif isinstance(value, str):
                xmp_title = value

        title_bytes_or_str = raw_exif.get('Exif.Image.XPTitle',
                                          raw_iptc.get('Iptc.Application2.ObjectName',
                                                       xmp_title))

        # Decode if bytes, ensure string type, handle None/empty
        if isinstance(title_bytes_or_str, bytes):
            try:
                # Try common encodings, UTF-16LE often used for XP tags
                title = title_bytes_or_str.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not title: # Try UTF-8 if LE failed
                     title = title_bytes_or_str.decode('utf-8', errors='ignore').rstrip('\x00')
            except Exception as dec_err:
                 logging.warning(f"Could not decode title bytes for {filename_for_csv}: {dec_err}. Raw: {title_bytes_or_str}")
                 title = str(title_bytes_or_str) # Fallback
        elif title_bytes_or_str: # Check if it's a non-empty string already
            title = str(title_bytes_or_str)
        else:
            title = '' # Ensure empty string if no title found

        # --- Extract Keywords (Combine EXIF, IPTC, XMP) ---
        keywords_semi = raw_exif.get('Exif.Image.XPKeywords', b'') # Default to empty bytes
        keywords_iptc = raw_iptc.get('Iptc.Application2.Keywords', [])
        keywords_xmp = raw_xmp.get('Xmp.dc.subject', [])

        kw_set = set() # Use set to avoid duplicates

        # Process EXIF XPKeywords (often semicolon-separated bytes)
        if isinstance(keywords_semi, bytes) and keywords_semi:
            try:
                kw_str = keywords_semi.decode('utf-16le', errors='ignore').rstrip('\x00')
                if not kw_str and len(keywords_semi) > 0: # Try UTF-8 if LE failed or was empty but bytes existed
                    kw_str = keywords_semi.decode('utf-8', errors='ignore').rstrip('\x00')
                if kw_str:
                    kw_set.update(k.strip() for k in kw_str.split(';') if k.strip())
            except Exception as dec_err:
                 logging.warning(f"Could not decode XPKeywords bytes for {filename_for_csv}: {dec_err}. Raw: {keywords_semi}")
        elif isinstance(keywords_semi, str) and keywords_semi: # Handle if already string
             kw_set.update(k.strip() for k in keywords_semi.split(';') if k.strip())


        # Process IPTC Keywords (list, potentially bytes)
        if isinstance(keywords_iptc, list):
            for k in keywords_iptc:
                if isinstance(k, bytes):
                     try:
                          kw_set.add(k.decode('utf-8', errors='ignore').strip())
                     except Exception as dec_err:
                          logging.warning(f"Could not decode IPTC keyword bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k: # Handle non-empty strings/other types
                     kw_set.add(str(k).strip())

        # Process XMP Subject (list, potentially bytes)
        if isinstance(keywords_xmp, list):
             for k in keywords_xmp:
                if isinstance(k, bytes):
                     try:
                          kw_set.add(k.decode('utf-8', errors='ignore').strip())
                     except Exception as dec_err:
                          logging.warning(f"Could not decode XMP subject bytes for {filename_for_csv}: {dec_err}. Raw: {k}")
                elif k: # Handle non-empty strings/other types
                     kw_set.add(str(k).strip())

        # Remove any remaining empty strings that might have slipped through
        kw_set.discard('')
        keywords = sorted(list(kw_set)) # Final sorted list

        # --- Prepare return values ---
        keywords_string = ",".join(keywords) # Comma-separated for Adobe CSV

        logging.info(f"Extracted metadata for {os.path.basename(metadata_source_path)}: Title='{title[:50]}...', Keywords='{keywords_string[:50]}...' ({len(keywords)} keywords)")
        # Return the filename to use in CSV (video filename if available), along with metadata
        return filename_for_csv, title, keywords_string

    except FileNotFoundError:
        # This check is now redundant due to the check at the start, but keep for safety
        logging.error(f"Metadata extraction failed: File not found at {metadata_source_path}")
        return None
    except Exception as e:
        # Catch pyexiv2 errors or other issues during processing
        logging.error(f"Error extracting metadata from {filename_for_csv}: {e}", exc_info=True)
        # Decide on fallback: return None (fail) or return filename with empty metadata?
        # Returning None is safer for the CSV process to avoid adding incomplete rows.
        return None
    finally:
        # Ensure file handle is closed
        if image_exif:
            try:
                image_exif.close()
                logging.debug(f"Closed pyexiv2 image object for {os.path.basename(metadata_source_path)}")
            except Exception as close_err:
                # This error might indicate changes weren't saved if we were writing, but here it's less critical
                logging.warning(f"Error closing pyexiv2 Image object for {os.path.basename(metadata_source_path)}: {close_err}")

# --- CSV Generation ---
def generate_adobe_csv(metadata_list, output_csv_path):
    """
    Generates a CSV file for Adobe Stock based on extracted metadata. Includes debug logging.

    Args:
        metadata_list (list): A list of tuples [(filename, title, keywords_string), ...].
        output_csv_path (str): The full path where the CSV file should be saved.

    Returns:
        bool: True if CSV generation was successful, False otherwise.
    """
    header = ['Filename', 'Title', 'Keywords', 'Category', 'Releases']
    logging.info(f"Attempting to generate Adobe CSV at: {output_csv_path}")
    logging.debug(f"Data received for CSV generation (first 5 items): {metadata_list[:5]}") # Log input data

    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(header)
            logging.debug(f"CSV Header written to {output_csv_path}")

            if not metadata_list:
                 logging.warning("metadata_list provided to generate_adobe_csv is empty.")

            # Loop through the received list
            for item_index, metadata_item in enumerate(metadata_list):
                try:
                    # Unpack the tuple
                    filename, title, keywords_string = metadata_item
                    logging.debug(f"CSV_ROW {item_index+1} - Unpacked: Filename='{filename}', Title='{title}', Keywords='{keywords_string}'") # Log unpacked data

                    # Define hardcoded columns
                    category = ""
                    releases = ""

                    # Prepare the row list
                    row_to_write = [filename, title, keywords_string, category, releases]
                    logging.debug(f"CSV_ROW {item_index+1} - Writing row: {row_to_write}") # Log the exact list being written

                    # Write the row
                    writer.writerow(row_to_write)

                except ValueError as unpack_err:
                     logging.error(f"Error unpacking metadata item #{item_index+1} in generate_adobe_csv: {metadata_item}. Error: {unpack_err}. Skipping item.")
                     continue # Skip this item and continue with the next
                except Exception as loop_err:
                     logging.error(f"Unexpected error processing item #{item_index+1} ({metadata_item}) in generate_adobe_csv loop: {loop_err}", exc_info=True)
                     continue # Skip this item

        logging.info(f"Successfully generated Adobe CSV: {output_csv_path} with {len(metadata_list)} data rows.")
        return True
    except IOError as e:
        logging.error(f"Error writing CSV file {output_csv_path}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error generating CSV {output_csv_path}: {e}", exc_info=True)
        return False


# --- SFTP Upload Logic (Specific to Adobe Stock) ---
def upload_to_adobe_sftp(username, password, source_path, hostname="sftp.contributor.adobestock.com", port=22):
    """
    Uploads files/directories to Adobe Stock via SFTP.

    Args:
        username (str): Adobe Stock SFTP username (often numeric ID).
        password (str): Adobe Stock SFTP password.
        source_path (str): Absolute path to the local file or directory to upload.
        hostname (str): Adobe SFTP hostname.
        port (int): SFTP port.

    Returns:
        list: A list of base filenames that were successfully uploaded.
              Returns an empty list if the upload fails or no files are uploaded.
    """
    uploaded_files = []
    transport = None
    sftp = None

    try:
        logging.info(f"Connecting to Adobe SFTP: {hostname}:{port} as {username}")
        transport = paramiko.Transport((hostname, port))
        transport.connect(username=username, password=password)
        sftp = paramiko.SFTPClient.from_transport(transport)
        logging.info("SFTP connection established.")

        # Ensure remote directory exists (optional, usually root is fine for Adobe)
        # try:
        #     sftp.chdir('.') # Check if root is accessible
        # except IOError:
        #     logging.error("Cannot access SFTP root directory.")
        #     return []

        # Handle single file or directory upload
        if os.path.isfile(source_path):
            # Check if this is an image with a matching video file
            has_matching_video, video_path = check_for_matching_video(source_path)

            if has_matching_video and video_path:
                # Upload the video file instead
                file_to_upload = video_path
                filename = os.path.basename(video_path)
                file_to_mark = source_path  # Mark the image as done
                logging.info(f"Found matching video for {os.path.basename(source_path)}: {filename}")
            else:
                # Regular file - upload as is
                file_to_upload = source_path
                filename = os.path.basename(source_path)
                file_to_mark = source_path

            remote_path = filename # Upload to root
            logging.info(f"Uploading file: {file_to_upload} to {remote_path}")
            try:
                sftp.put(file_to_upload, remote_path)
                logging.info(f"Successfully uploaded {filename}")
                uploaded_files.append(filename)
                try:
                    mark_as_done(file_to_mark, "Adobe Stock")
                except Exception as e:
                    logging.warning(f"Couldn't mark the file {file_to_mark} uploaded: {e}")
            except Exception as e:
                logging.error(f"Failed to upload {filename}: {e}")

        elif os.path.isdir(source_path):
            logging.info(f"Uploading directory contents: {source_path}")

            # First, scan the directory to identify image files with matching videos
            image_to_video_map = {}
            for item in os.listdir(source_path):
                local_item_path = os.path.join(source_path, item)
                if os.path.isfile(local_item_path):
                    # Check if this is an image with a matching video
                    has_matching_video, video_path = check_for_matching_video(local_item_path)
                    if has_matching_video and video_path:
                        image_to_video_map[local_item_path] = video_path

            # Now process all files in the directory
            for item in os.listdir(source_path):
                local_item_path = os.path.join(source_path, item)
                if os.path.isfile(local_item_path):
                    # Skip video files that will be uploaded via their image counterparts
                    if any(video_path == local_item_path for video_path in image_to_video_map.values()):
                        logging.info(f"Skipping video file {item} - will be uploaded via its image counterpart")
                        continue

                    # Determine what to upload
                    if local_item_path in image_to_video_map:
                        # This is an image with a matching video - upload the video instead
                        video_path = image_to_video_map[local_item_path]
                        file_to_upload = video_path
                        upload_filename = os.path.basename(video_path)
                        file_to_mark = local_item_path  # Mark the image as done
                        logging.info(f"Uploading video for image {item}: {os.path.basename(video_path)}")
                    else:
                        # Regular file - upload as is
                        file_to_upload = local_item_path
                        upload_filename = item
                        file_to_mark = local_item_path
                        logging.info(f"Uploading file: {item}")

                    remote_path = upload_filename # Upload to root
                    logging.info(f"Uploading to Adobe: {file_to_upload} as {remote_path}")
                    try:
                        sftp.put(file_to_upload, remote_path)
                        logging.info(f"Successfully uploaded {upload_filename}")
                        uploaded_files.append(upload_filename)
                        try:
                            mark_as_done(file_to_mark, "Adobe Stock")
                        except Exception as mark_err:
                            logging.warning(f"Couldn't mark the file {file_to_mark} as done for Adobe Stock: {mark_err}")
                    except Exception as e:
                        logging.error(f"Failed to upload {upload_filename}: {e}")
                else:
                    logging.warning(f"Skipping non-file item: {item}")
        else:
            logging.error(f"Source path is neither a file nor a directory: {source_path}")

    except paramiko.AuthenticationException:
        logging.error(f"SFTP Authentication failed for user {username}. Check credentials.")
    except paramiko.SSHException as sshException:
        logging.error(f"Unable to establish SSH connection: {sshException}")
    except Exception as e:
        logging.error(f"An error occurred during SFTP operation: {e}", exc_info=True)

    finally:
        if sftp:
            sftp.close()
            logging.info("SFTP client closed.")
        if transport:
            transport.close()
            logging.info("SFTP transport closed.")

    return uploaded_files


# --- Main Adobe Stock Process Orchestrator ---
def upload_adobe_stock_process(sftp_username, sftp_password, website_login, website_password, source_path_or_list):
    """
    Orchestrates the full Adobe Stock upload process:
    1. Uploads image(s) via SFTP (Handles single path or list of paths).
    2. Extracts metadata from uploaded files.
    3. Generates a CSV metadata file.
    4. Uploads the CSV file via Selenium.

    Args:
        sftp_username (str): Adobe SFTP username (numeric ID).
        sftp_password (str): Adobe SFTP password.
        website_login (str): Adobe Contributor website login email.
        website_password (str): Adobe Contributor website login password.
        source_path_or_list (str or list): Absolute path(s) to the local file(s) or a single directory containing images.

    Returns:
        bool: True if the entire process (SFTP + CSV Upload) seems successful, False otherwise.
    """
    logging.info(f"Starting full Adobe Stock upload process for: {source_path_or_list}")

    all_uploaded_filenames = [] # Accumulate filenames from all SFTP uploads
    source_files_map = {} # Map filename back to original full path

    # --- Step 1: Upload Images via SFTP (Handle list or single path) ---
    if isinstance(source_path_or_list, list):
        logging.info("Processing a list of source paths.")
        # Build initial map for individual files in the list
        for item_path in source_path_or_list:
            abs_item_path = os.path.abspath(item_path)
            if not os.path.exists(abs_item_path):
                 logging.warning(f"Source path in list does not exist: {abs_item_path}. Skipping.")
                 continue
            if os.path.isfile(abs_item_path):
                 # Add individual files directly to map before upload attempt
                 source_files_map[os.path.basename(abs_item_path)] = abs_item_path
                 # Call SFTP upload for each item
                 uploaded_for_item = upload_to_adobe_sftp(sftp_username, sftp_password, abs_item_path)
                 all_uploaded_filenames.extend(uploaded_for_item)
            elif os.path.isdir(abs_item_path):
                 logging.warning(f"Source list contains a directory: {abs_item_path}. Uploading its contents.")
                 # Call SFTP upload for the directory
                 uploaded_for_item = upload_to_adobe_sftp(sftp_username, sftp_password, abs_item_path)
                 all_uploaded_filenames.extend(uploaded_for_item)
                 # Map files *within* the directory after successful upload
                 for fname in uploaded_for_item:
                      potential_path = os.path.join(abs_item_path, fname)
                      if os.path.exists(potential_path):
                           source_files_map[fname] = potential_path
                      else:
                           logging.warning(f"Could not map uploaded file '{fname}' from directory '{abs_item_path}' back to source.")
            else:
                logging.warning(f"Item in source list is neither file nor directory: {abs_item_path}. Skipping.")

    elif isinstance(source_path_or_list, str):
        logging.info("Processing a single source path.")
        abs_source_path = os.path.abspath(source_path_or_list)
        if not os.path.exists(abs_source_path):
            logging.error(f"Source path does not exist: {abs_source_path}")
            return False

        # Call SFTP upload for the single path (file or directory)
        all_uploaded_filenames = upload_to_adobe_sftp(sftp_username, sftp_password, abs_source_path)

        # Build map after upload for the single source path
        if all_uploaded_filenames:
             if os.path.isfile(abs_source_path):
                  base_name = os.path.basename(abs_source_path)
                  if base_name in all_uploaded_filenames:
                       source_files_map[base_name] = abs_source_path
             elif os.path.isdir(abs_source_path):
                  for fname in all_uploaded_filenames:
                       potential_path = os.path.join(abs_source_path, fname)
                       if os.path.exists(potential_path):
                            source_files_map[fname] = potential_path
                       else:
                            logging.warning(f"Could not map uploaded file '{fname}' from directory '{abs_source_path}' back to source.")
    else:
        logging.error(f"Invalid source_path_or_list type: {type(source_path_or_list)}. Must be string or list.")
        return False


    # --- Step 1 check ---
    if not all_uploaded_filenames:
        logging.error("SFTP upload failed or no files were uploaded across all sources. Aborting CSV process.")
        return False

    logging.info(f"Successfully SFTP'd {len(all_uploaded_filenames)} files in total: {all_uploaded_filenames}")
    logging.debug(f"Source files map created: {source_files_map}") # Debug log for map

    # --- Step 2: Extract Metadata ---
    metadata_for_csv = []
    logging.info("Extracting metadata from original source files using the map...")
    processed_filenames_for_metadata = set() # Ensure we only process each unique filename once

    # Iterate through unique filenames that were successfully uploaded
    unique_uploaded_filenames = sorted(list(set(all_uploaded_filenames)))

    for filename in unique_uploaded_filenames:
        if filename in source_files_map:
            original_file_path = source_files_map[filename]
            logging.info(f"Extracting metadata for {filename} from {original_file_path}")
            metadata = extract_metadata(original_file_path)
            if metadata:
                # Ensure metadata tuple is correct (filename, title, keywords_string)
                if len(metadata) == 3:
                     metadata_for_csv.append(metadata)
                     processed_filenames_for_metadata.add(filename)
                else:
                     logging.warning(f"Incorrect metadata tuple format returned for {filename}: {metadata}. Skipping.")
            else:
                logging.warning(f"Failed to extract metadata for {filename} (source: {original_file_path}). It might be excluded or have empty fields in CSV.")
                # Optionally add with empty fields if desired:
                # metadata_for_csv.append((filename, "", ""))
                # processed_filenames_for_metadata.add(filename) # Mark as processed even if failed
        else:
             logging.warning(f"Filename '{filename}' was uploaded but couldn't be mapped back to a source file path. Skipping metadata extraction.")


    if not metadata_for_csv:
        # Check if files were uploaded but just couldn't be mapped or had no metadata
        if all_uploaded_filenames:
             logging.warning("Successfully uploaded files via SFTP, but no metadata could be extracted or mapped. Cannot generate meaningful CSV.")
        else:
             # This case should have been caught earlier, but added for safety
             logging.error("No files were uploaded via SFTP. Cannot generate CSV.")
        return False # Can't proceed without metadata for the CSV

    # --- Step 3: Generate CSV ---
    # Define the target directory
    csv_dir = os.path.expanduser("~/Desktop/CSV/Adobe")
    os.makedirs(csv_dir, exist_ok=True)  # Ensure the directory exists

    # Generate the final CSV file path
    csv_filename = "adobe_metadata.csv"
    csv_final_path = os.path.join(csv_dir, csv_filename)
    csv_file_obj = None
    temp_csv_path = ""
    try:
        # Create a temporary file for the CSV
        csv_file_obj = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".csv", encoding='utf-8', newline='')
        temp_csv_path = csv_file_obj.name
        csv_file_obj.close()  # Close it so generate_adobe_csv can open and write

        logging.info(
            f"Generating Adobe metadata CSV at temporary path: {temp_csv_path} for {len(metadata_for_csv)} files.")
        if not generate_adobe_csv(metadata_for_csv, temp_csv_path):
            logging.error("Failed to generate Adobe metadata CSV.")
            if os.path.exists(temp_csv_path): os.remove(temp_csv_path)
            return False

        # Move the temporary CSV to the final destination
        os.replace(temp_csv_path, csv_final_path)
        logging.info(f"CSV successfully saved to: {csv_final_path}")
    except Exception as e:
        logging.error(f"Error during CSV file creation: {e}")
        if csv_file_obj and not csv_file_obj.closed:
            csv_file_obj.close()
        if temp_csv_path and os.path.exists(temp_csv_path):
            os.remove(temp_csv_path)
        return False

    return True