<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metadata Comparison</title>
    <link rel="stylesheet" href="comparison.css">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Completion Indicator Overlay -->
    <div id="completion-overlay" class="completion-overlay">
        <div class="completion-circle">
            <svg width="40" height="40" viewBox="0 0 50 50">
                <path class="checkmark" d="M14,27 L22,35 L36,15" />
            </svg>
        </div>
        <div class="completion-text">All metadata choices saved!</div>
    </div>

    <div class="container">
        <div id="progress-info" class="progress-info">
            <span id="file-counter">File 1 of 1</span>
            <span id="filename">filename.jpg</span>
        </div>

        <!-- Image Preview -->
        <div class="image-preview-container">
            <img id="preview-image" src="" alt="File preview" />
        </div>

        <!-- Comparison Container -->
        <div class="comparison-container">
            <!-- Original Metadata (Left) -->
            <div class="metadata-box original-box">
                <div class="metadata-header">
                    <h2>Original (No Refinement)</h2>
                </div>
                <div class="metadata-content">
                    <div class="metadata-item">
                        <label>Title:</label>
                        <p id="original-title" class="metadata-text"></p>
                    </div>
                    <div class="metadata-item">
                        <label>Description:</label>
                        <p id="original-description" class="metadata-text"></p>
                    </div>
                    <div class="metadata-item">
                        <label>Keywords:</label>
                        <div id="original-keywords" class="keywords-container"></div>
                    </div>
                </div>
                <button id="keep-original-btn" class="choice-btn original-btn">Keep Original</button>
            </div>

            <!-- Divider Line -->
            <div class="divider-line"></div>

            <!-- Refined Metadata (Right) -->
            <div class="metadata-box refined-box">
                <div class="metadata-header">
                    <h2>Refined (AI Enhanced)</h2>
                </div>
                <div class="metadata-content">
                    <div class="metadata-item">
                        <label>Title:</label>
                        <p id="refined-title" class="metadata-text"></p>
                    </div>
                    <div class="metadata-item">
                        <label>Description:</label>
                        <p id="refined-description" class="metadata-text"></p>
                    </div>
                    <div class="metadata-item">
                        <label>Keywords:</label>
                        <div id="refined-keywords" class="keywords-container"></div>
                    </div>
                </div>
                <button id="keep-refined-btn" class="choice-btn refined-btn">Keep Refined</button>
            </div>
        </div>
    </div>

    <script>
        let currentFileData = null;

        // Initialize the comparison interface
        window.addEventListener('DOMContentLoaded', function() {
            loadCurrentFile();
        });

        function loadCurrentFile() {
            if (window.pywebview && window.pywebview.api) {
                window.pywebview.api.get_current_file_data().then(data => {
                    if (data) {
                        currentFileData = data;
                        updateInterface(data);
                    } else {
                        console.error('No file data received');
                    }
                }).catch(err => {
                    console.error('Error loading file data:', err);
                });
            }
        }

        function updateInterface(data) {
            // Update progress info
            document.getElementById('file-counter').textContent = `File ${data.file_index} of ${data.total_files}`;
            document.getElementById('filename').textContent = data.filename;

            // Update image preview
            const previewImg = document.getElementById('preview-image');
            if (data.thumbnail) {
                previewImg.src = data.thumbnail;
                previewImg.style.display = 'block';
            } else {
                previewImg.style.display = 'none';
            }

            // Update original metadata
            document.getElementById('original-title').textContent = data.initial.title;
            document.getElementById('original-description').textContent = data.initial.description;
            updateKeywords('original-keywords', data.initial.keywords);

            // Update refined metadata
            document.getElementById('refined-title').textContent = data.refined.title;
            document.getElementById('refined-description').textContent = data.refined.description;
            updateKeywords('refined-keywords', data.refined.keywords);
        }

        function updateKeywords(containerId, keywords) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            if (keywords && Array.isArray(keywords)) {
                keywords.forEach(keyword => {
                    const keywordBox = document.createElement('span');
                    keywordBox.className = 'keyword-box';
                    keywordBox.textContent = keyword;
                    container.appendChild(keywordBox);
                });
            }
        }

        function makeChoice(choice) {
            if (!currentFileData) return;

            // Disable buttons to prevent double-clicking
            document.getElementById('keep-original-btn').disabled = true;
            document.getElementById('keep-refined-btn').disabled = true;

            // Add visual feedback
            const chosenBtn = choice === 'original' ? 
                document.getElementById('keep-original-btn') : 
                document.getElementById('keep-refined-btn');
            chosenBtn.classList.add('chosen');

            // Send choice to Python
            window.pywebview.api.choose_metadata(choice).then(result => {
                if (result.status === 'next') {
                    // Load next file
                    setTimeout(() => {
                        currentFileData = result.data;
                        updateInterface(result.data);
                        // Re-enable buttons
                        document.getElementById('keep-original-btn').disabled = false;
                        document.getElementById('keep-refined-btn').disabled = false;
                        document.getElementById('keep-original-btn').classList.remove('chosen');
                        document.getElementById('keep-refined-btn').classList.remove('chosen');
                    }, 500);
                } else if (result.status === 'completed') {
                    // All files processed
                    console.log('All files processed!');
                } else {
                    console.error('Error processing choice');
                    // Re-enable buttons on error
                    document.getElementById('keep-original-btn').disabled = false;
                    document.getElementById('keep-refined-btn').disabled = false;
                    chosenBtn.classList.remove('chosen');
                }
            }).catch(err => {
                console.error('Error sending choice:', err);
                // Re-enable buttons on error
                document.getElementById('keep-original-btn').disabled = false;
                document.getElementById('keep-refined-btn').disabled = false;
                chosenBtn.classList.remove('chosen');
            });
        }

        function showCompletionAnimation() {
            const overlay = document.getElementById('completion-overlay');
            overlay.classList.add('visible');
        }

        // Event listeners
        document.getElementById('keep-original-btn').addEventListener('click', () => makeChoice('original'));
        document.getElementById('keep-refined-btn').addEventListener('click', () => makeChoice('refined'));
    </script>
</body>
</html>
