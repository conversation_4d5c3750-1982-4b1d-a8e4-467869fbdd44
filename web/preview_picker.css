/* START OF FILE web/preview_picker.css */
/* Reuse body, main-container, title, btn styles from key.css */

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    margin: 0;
     /* Align content to the top */
    justify-content: flex-start;
    align-items: stretch;
}

.main-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 15px;
    overflow: hidden;
    width: 100%;
    max-width: 800px; /* Limit width for better layout */
    margin: 0 auto; /* Center the container */
    box-sizing: border-box;
    gap: 15px; /* Space between elements */
}


.preview-area {
    flex-grow: 1; /* Takes up most space */
    background-color: #333; /* Dark background for contrast */
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; /* Prevent image overflow */
    border-radius: 8px;
    position: relative; /* For loading indicator */
    min-height: 200px; /* Ensure it has some height */
}

#frame-preview {
    display: block;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* Scale image nicely */
}

#loading-indicator {
    position: absolute;
    color: white;
    font-size: 1.2em;
    background-color: rgba(0,0,0,0.5);
    padding: 10px 20px;
    border-radius: 5px;
}


.timeline-area {
    flex-shrink: 0; /* Don't shrink */
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#timeline-slider {
    width: 95%; /* Nearly full width */
    cursor: pointer;
    margin-bottom: 5px;
}
#timeline-slider:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}


.time-display {
    font-size: 13px;
    color: #444;
}

.controls-area {
    flex-shrink: 0;
    display: flex;
    flex-direction: column; /* Stack info text and button */
    align-items: center;
    gap: 10px; /* Space between info text and button */
    border-top: 1px solid rgba(200, 200, 200, 0.5);
    padding-top: 15px;
}
 .info-text {
     font-size: 12px;
     color: #555;
     margin: 0 0 5px 0; /* Margin below text */
     text-align: center;
 }
 .info-text span {
     font-weight: bold;
 }

#save-preview-btn {
    width: 200px; /* Fixed width for the button */
    /* Uses .btn style from key.css */
}
#save-preview-btn:disabled {
    background: linear-gradient(145deg, #cccccc, #b3b3b3);
    cursor: not-allowed;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: none;
}


.status-bar {
    padding: 5px 15px;
    font-size: 12px;
    color: #555;
    background-color: rgba(220, 220, 220, 0.6);
    border-top: 1px solid rgba(200, 200, 200, 0.4);
    flex-shrink: 0;
    min-height: 25px;
    display: flex;
    align-items: center;
}
.status-bar.error { color: #D32F2F; font-weight: bold; }
.status-bar.success { color: #388E3C; font-weight: bold; }
.status-bar.warning { color: #F57C00; font-weight: bold; }
.status-bar.info { color: #1976D2; }

/* END OF FILE web/preview_picker.css */